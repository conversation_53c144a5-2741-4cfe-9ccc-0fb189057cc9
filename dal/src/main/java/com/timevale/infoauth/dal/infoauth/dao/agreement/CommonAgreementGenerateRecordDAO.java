package com.timevale.infoauth.dal.infoauth.dao.agreement;

import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementGenerateRecordDO;
import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementReleaseRecordDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 通用协议内部发布记录DAO
 *
 * <AUTHOR> Kunpeng
 */
@Repository
public class CommonAgreementGenerateRecordDAO {

    /**
     * The Mapper for common agreement release record.
     */
    @Autowired
    private CommonAgreementGenerateRecordMapper recordMapper;

    /**
     * Save a common agreement release record.
     *
     * @param infoDO the information data object to be saved
     * @return true if the save operation is successful, otherwise false
     */
    public boolean save(CommonAgreementGenerateRecordDO infoDO){
        return recordMapper.insert(infoDO) > 0;
    }


    /**
     * Get a common agreement release record by its release record ID.
     *
     * @param generateRecordId the release record ID
     * @return the queried common agreement release record data object, or null if not found
     */
    public CommonAgreementGenerateRecordDO getOne(String generateRecordId){
        return recordMapper.getByGenerateRecordId(generateRecordId);
    }
}
