package com.timevale.infoauth.service.request.backstage;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/7/24 14:17
 */
@Data
public class RouteInfoAuthResponse extends ToString {

    private long id;
    private String certNo;
    private String name;
    private Integer verifyType;
    private String provider;
    private Date createTime;
    private Date lostTime;
    private boolean expired;

    private RouteInfoAuthResponse() {

    }

    public RouteInfoAuthResponse(boolean expired) {
        this.expired = expired;
    }
}
