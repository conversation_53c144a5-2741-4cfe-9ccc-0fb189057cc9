package com.timevale.infoauth.service.api;

import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.impl.provider.client.qixinbao.AesUtil;
import com.timevale.infoauth.service.impl.provider.constants.OrgSummaryProviderEnum;
import com.timevale.infoauth.service.impl.provider.dto.QXBHaSocialorg3ElementResponse;
import com.timevale.infoauth.service.request.OrgSummaryRequest;
import com.timevale.infoauth.service.response.OrgSummaryResponse;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2024/6/23 16:09
 */
@JsonDataIgnore
public class QinxinbaoTest extends ApplicationTest {
    @Resource
    InfoauthService infoauthService;

    @Test
    public void testGetOrgSummary() {
        Assert.assertEquals(OrgSummaryProviderEnum.from("QIXINBAO_NEW"), OrgSummaryProviderEnum.QIXINBAO_NEW);


        OrgSummaryRequest request = new OrgSummaryRequest();
        request.setOrgName("杭州天谷信息科技有限公司");
        request.setBizAppId("**********");
        RpcOutput<OrgSummaryResponse> rpcOutput = infoauthService.getOrgSummary(request);
        Assert.assertTrue(rpcOutput.isSuccess());
    }


    @Test
    public void testEncoder() {
        AesUtil.encoder("1", "2", "2");
        QXBHaSocialorg3ElementResponse.parasList("1,2");
        List<String> strings = QXBHaSocialorg3ElementResponse.parasList("[1,2]");
        Assert.assertNotNull(strings);
    }


}
