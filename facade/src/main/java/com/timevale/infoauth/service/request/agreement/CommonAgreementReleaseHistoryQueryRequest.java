package com.timevale.infoauth.service.request.agreement;

import com.timevale.infoauth.service.model.BaseModel;
import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;


/**
 * 通用协议信息查询
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CommonAgreementReleaseHistoryQueryRequest extends ToString {


    /**
     * 协议编号
   */
  @NotBlank
  private String commonAgreementId;

}
