package com.timevale.infoauth.service.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.impl.common.dto.OrganProvin;
import com.timevale.infoauth.service.impl.common.dto.Provout;
import com.timevale.infoauth.service.impl.dto.BankCardOcrProvin;
import com.timevale.infoauth.service.impl.dto.BankauthProvin;
import com.timevale.infoauth.service.impl.dto.BankthreeProvin;
import com.timevale.infoauth.service.impl.dto.CompareWithSourceProvin;
import com.timevale.infoauth.service.impl.dto.CompareWithoutSourceProvin;
import com.timevale.infoauth.service.impl.dto.DrivingLicenceOcrProvin;
import com.timevale.infoauth.service.impl.dto.DrivingPermitOcrProvin;
import com.timevale.infoauth.service.impl.dto.OcrProvin;
import com.timevale.infoauth.service.impl.dto.OrgLicenseOcrProvin;
import com.timevale.infoauth.service.impl.dto.PersonProvin;
import com.timevale.infoauth.service.impl.dto.TelecomauthProvin;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.Bank3Adaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.Bank4Adaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.BankCardOcrAdaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.CommunityAdaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.CompareWithSourceAdaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.CompareWithoutSourceAdaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.DriversLicenceOcrAdaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.DriversPermitOcrAdaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.Org2Adaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.Org3Adaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.Org4Adaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.OrgLicenceOcrAdaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.Psn2Adaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.PsnIdOcrAdaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.TelecomAdaptors;
import com.timevale.infoauth.service.impl.provider.biz.dto.TencentOcrBaseProvout;
import com.timevale.infoauth.service.impl.provider.utils.TencentUtil;
import com.timevale.infoauth.service.impl.utils.Response;
import com.timevale.infoauth.utils.ResourceUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.qa.apitest.model.TestJsonObj;
import esign.utils.IOUtil;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Locale;
import lombok.SneakyThrows;
import org.apache.http.Header;
import org.apache.http.HeaderIterator;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.ProtocolVersion;
import org.apache.http.StatusLine;
import org.apache.http.params.HttpParams;
import org.testng.Assert;
import org.testng.annotations.Ignore;
import org.testng.annotations.Test;

/**
 * @author: jiuchen
 * @since: 2020-07-30 19:30
 */
public class ProviderClientTest extends ApplicationTest {

//  @Test
//  public void testShuheTelecomAdaptors() {
//    String json =
//        "{\"availableProviderList\":[\"SHUHE\"],\"certNumber\":\"6*****************\",\"confirmProvider\":false,\"mobile\":\"***********\",\"name\":\"黄珂\",\"providerName\":\"SHUHE\",\"serviceType\":\"MOBILE_3_ELEMENT\",\"transactionId\":\"********04T88T15961100272470000\",\"userCenterCertType\":0}";
//    TelecomauthProvin provin = JsonUtils.json2pojo(json, TelecomauthProvin.class);
//    Provout provout = new TelecomAdaptors().SHUHE_auth.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testShujubaoTelecomAdaptors() {
//    String json =
//        "{\"availableProviderList\":[\"SHUJUBAO\"],\"certNumber\":\"6*****************\",\"confirmProvider\":false,\"mobile\":\"***********\",\"name\":\"黄珂\",\"providerName\":\"SHUJUBAO\",\"serviceType\":\"MOBILE_3_ELEMENT\",\"transactionId\":\"********04T88T15961100272470000\",\"userCenterCertType\":0}";
//    TelecomauthProvin provin = JsonUtils.json2pojo(json, TelecomauthProvin.class);
//    Provout provout = new TelecomAdaptors().SHUJUBAO_auth.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testHchxTelecomAdaptors() {
//    String json =
//            "{\"availableProviderList\":[\"HCHX\"],\"certNumber\":\"6*****************\",\"confirmProvider\":false,\"mobile\":\"***********\",\"name\":\"黄珂\",\"providerName\":\"SHUHE\",\"serviceType\":\"MOBILE_3_ELEMENT\",\"transactionId\":\"********04T88T15961100272470000\",\"userCenterCertType\":0}";
//    TelecomauthProvin provin = JsonUtils.json2pojo(json, TelecomauthProvin.class);
//    Provout provout = new TelecomAdaptors().HCHX_auth.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testBytedanceTelecomAdaptors() {
//    String json =
//            "{\"availableProviderList\":[\"BYTEDANCE\"],\"certNumber\":\"6*****************\",\"confirmProvider\":false,\"mobile\":\"***********\",\"name\":\"黄珂\",\"providerName\":\"BYTEDANCE\",\"serviceType\":\"MOBILE_3_ELEMENT\",\"transactionId\":\"********04T88T15961100272470000\",\"userCenterCertType\":0}";
//    TelecomauthProvin provin = JsonUtils.json2pojo(json, TelecomauthProvin.class);
//    Provout provout = new TelecomAdaptors().BYTEDANCE_auth.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testTencentTelecomAdaptors() {
//    String json =
//        "{\"availableProviderList\":[\"TENCENT\"],\"certNumber\":\"6*****************\",\"confirmProvider\":false,\"mobile\":\"***********\",\"name\":\"黄珂\",\"providerName\":\"BYTEDANCE\",\"serviceType\":\"MOBILE_3_ELEMENT\",\"transactionId\":\"********04T88T15961100272470000\",\"userCenterCertType\":0}";
//    TelecomauthProvin provin = JsonUtils.json2pojo(json, TelecomauthProvin.class);
//    Provout provout = new TelecomAdaptors().TENCENT_auth.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testShuheBank3Adaptors() {
//    String json =
//        "{\"availableProviderList\":[\"SHUHE\"],\"cardno\":\"****************\",\"certNumber\":\"******************\",\"confirmProvider\":false,\"name\":\"姜娇\",\"providerName\":\"SHUHE\",\"serviceType\":\"BANKCARD_3_ELEMENT\",\"transactionId\":\"********04T89T15961100692260000\",\"userCenterCertType\":0}";
//    BankthreeProvin provin = JsonUtils.json2pojo(json, BankthreeProvin.class);
//    Provout provout = new Bank3Adaptors().Shuhe_bankthreeValid.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testHchxBank3Adaptors() {
//    String json =
//            "{\"availableProviderList\":[\"HCHX\"],\"cardno\":\"****************\",\"certNumber\":\"******************\",\"confirmProvider\":false,\"name\":\"姜娇\",\"providerName\":\"SHUHE\",\"serviceType\":\"BANKCARD_3_ELEMENT\",\"transactionId\":\"********04T89T15961100692260000\",\"userCenterCertType\":0}";
//    BankthreeProvin provin = JsonUtils.json2pojo(json, BankthreeProvin.class);
//    Provout provout = new Bank3Adaptors().HCHX_bankthreeValid.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testShuheBank4Adaptors() {
//    String json =
//        "{\"availableProviderList\":[\"SHUHE\"],\"cardno\":\"6222081203009064490\",\"certNumber\":\"330327199106085684\",\"confirmProvider\":false,\"mobile\":\"***********\",\"name\":\"林书芳\",\"providerName\":\"SHUHE\",\"serviceType\":\"BANKCARD_4_ELEMENT\",\"transactionId\":\"********04T90T15961101016590000\",\"userCenterCertType\":1}";
//    BankauthProvin provin = JsonUtils.json2pojo(json, BankauthProvin.class);
//    Provout provout = new Bank4Adaptors().Shuhe_bankauthValid.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testHchxBank4Adaptors() {
//    String json =
//            "{\"availableProviderList\":[\"HCHX\"],\"cardno\":\"6222081203009064490\",\"certNumber\":\"330327199106085684\",\"confirmProvider\":false,\"mobile\":\"***********\",\"name\":\"林书芳\",\"providerName\":\"SHUHE\",\"serviceType\":\"BANKCARD_4_ELEMENT\",\"transactionId\":\"********04T90T15961101016590000\",\"userCenterCertType\":1}";
//    BankauthProvin provin = JsonUtils.json2pojo(json, BankauthProvin.class);
//    Provout provout = new Bank4Adaptors().HCHX_bankfourValid.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testShujubaoBank4Adaptors() {
//    String json =
//            "{\"availableProviderList\":[\"SHUJUBAO\"],\"cardno\":\"6222081203009064490\",\"certNumber\":\"330327199106085684\",\"confirmProvider\":false,\"mobile\":\"***********\",\"name\":\"林书芳\",\"providerName\":\"SHUJUBAO\",\"serviceType\":\"BANKCARD_4_ELEMENT\",\"transactionId\":\"********04T90T15961101016590000\",\"userCenterCertType\":1}";
//    BankauthProvin provin = JsonUtils.json2pojo(json, BankauthProvin.class);
//    Provout provout = new Bank4Adaptors().SJB_bankfourValid.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testBytedanceBank4Adaptors() {
//    String json =
//            "{\"availableProviderList\":[\"BYTEDANCE\"],\"cardno\":\"6222081203009064490\",\"certNumber\":\"330327199106085684\",\"confirmProvider\":false,\"mobile\":\"***********\",\"name\":\"林书芳\",\"providerName\":\"BYTEDANCE\",\"serviceType\":\"BANKCARD_4_ELEMENT\",\"transactionId\":\"********04T90T15961101016590000\",\"userCenterCertType\":1}";
//    BankauthProvin provin = JsonUtils.json2pojo(json, BankauthProvin.class);
//    Provout provout = new Bank4Adaptors().BYTEDANCE_bankfourValid.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testTencentPsn2Adaptors() {
//    String json =
//        "{\"availableProviderList\":[\"TENCENT\"],\"certNumber\":\"******************\",\"confirmProvider\":false,\"name\":\"韦锋\",\"providerName\":\"TENCENT\",\"serviceType\":\"IDCARD_2_ELEMENT\",\"transactionId\":\"********04T98T15976417804060000\",\"userCenterCertType\":0}";
//    PersonProvin provin = JsonUtils.json2pojo(json, PersonProvin.class);
//    Provout provout = new Psn2Adaptors().TENCENT_identityAuthValid.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testYIKEYUNPsn2Adaptors() {
//    String json =
//        "{\"availableProviderList\":[\"YIKEYUN\"],\"certNumber\":\"******************\",\"confirmProvider\":false,\"name\":\"韦锋\",\"providerName\":\"YIKEYUN\",\"serviceType\":\"IDCARD_2_ELEMENT\",\"transactionId\":\"********04T98T15976417804060000\",\"userCenterCertType\":0}";
//    PersonProvin provin = JsonUtils.json2pojo(json, PersonProvin.class);
//    Provout provout = new Psn2Adaptors().Yky_identityauthValid.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testYIKEYUNPsn2AdaptorsFailure() {
//    String json =
//        "{\"availableProviderList\":[\"YIKEYUN\"],\"certNumber\":\"******************\",\"confirmProvider\":false,\"name\":\"韦一锋\",\"providerName\":\"YIKEYUN\",\"serviceType\":\"IDCARD_2_ELEMENT\",\"transactionId\":\"********04T98T15976417804060000\",\"userCenterCertType\":0}";
//    PersonProvin provin = JsonUtils.json2pojo(json, PersonProvin.class);
//    Provout provout = new Psn2Adaptors().Yky_identityauthValid.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testYIKEYUNPsn2AdaptorsUnknown() {
//    String json =
//        "{\"availableProviderList\":[\"YIKEYUN\"],\"certNumber\":\"******************\",\"confirmProvider\":false,\"name\":\"韦1锋\",\"providerName\":\"YIKEYUN\",\"serviceType\":\"IDCARD_2_ELEMENT\",\"transactionId\":\"********04T98T15976417804060000\",\"userCenterCertType\":0}";
//    PersonProvin provin = JsonUtils.json2pojo(json, PersonProvin.class);
//    Provout provout = new Psn2Adaptors().Yky_identityauthValid.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testHchxPsn2Adaptors() {
//    String json =
//            "{\"availableProviderList\":[\"HCHX\"],\"certNumber\":\"******************\",\"confirmProvider\":false,\"name\":\"韦锋\",\"providerName\":\"TENCENT\",\"serviceType\":\"IDCARD_2_ELEMENT\",\"transactionId\":\"********04T98T15976417804060000\",\"userCenterCertType\":0}";
//    PersonProvin provin = JsonUtils.json2pojo(json, PersonProvin.class);
//    Provout provout = new Psn2Adaptors().HCHX_identityauthValid.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testBytedancePsn2Adaptors() {
//    String json =
//            "{\"availableProviderList\":[\"BYTEDANCE\"],\"certNumber\":\"******************\",\"confirmProvider\":false,\"name\":\"韦锋\",\"providerName\":\"BYTEDANCE\",\"serviceType\":\"IDCARD_2_ELEMENT\",\"transactionId\":\"********04T98T15976417804060000\",\"userCenterCertType\":0}";
//    PersonProvin provin = JsonUtils.json2pojo(json, PersonProvin.class);
//    Provout provout = new Psn2Adaptors().BYTEDANCE_identityauthValid.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testAlipayCompareWithoutSourceAdaptors() {
//    TestJsonObj testJsonObj = jsonDataMap.get("testAlipayCompareWithoutSourceAdaptors");
//    CompareWithoutSourceProvin provin = (CompareWithoutSourceProvin) testJsonObj.getReqData();
//    Provout provout = new CompareWithoutSourceAdaptors().ALIPAY_auth.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testHaixinCompareWithoutSourceAdaptors() {
//    TestJsonObj testJsonObj = jsonDataMap.get("testHaixinCompareWithoutSourceAdaptors");
//    CompareWithoutSourceProvin provin = (CompareWithoutSourceProvin) testJsonObj.getReqData();
//    Provout provout = new CompareWithoutSourceAdaptors().HAIXIN_auth.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testYkyCompareWithoutSourceAdaptors() {
//    TestJsonObj testJsonObj = jsonDataMap.get("testHaixinCompareWithoutSourceAdaptors");
//    CompareWithoutSourceProvin provin = (CompareWithoutSourceProvin) testJsonObj.getReqData();
//    Provout provout = new CompareWithoutSourceAdaptors().YIKEYUN_auth.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testYkyCompareWithoutSourceAdaptorsFailure() {
//    TestJsonObj testJsonObj = jsonDataMap.get("testHaixinCompareWithoutSourceAdaptors");
//    CompareWithoutSourceProvin provin = (CompareWithoutSourceProvin) testJsonObj.getReqData();
//    List<String> availableProviderList = new ArrayList<>();
//    availableProviderList.add(ProviderEnum.YIKEYUN.dbKey());
//    provin.setAvailableProviderList(availableProviderList);
//    provin.setName("朱一涛");
//    Provout provout = new CompareWithoutSourceAdaptors().YIKEYUN_auth.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testHchxCompareWithoutSourceAdaptors() {
//    TestJsonObj testJsonObj = jsonDataMap.get("testHaixinCompareWithoutSourceAdaptors");
//    CompareWithoutSourceProvin provin = (CompareWithoutSourceProvin) testJsonObj.getReqData();
//    Provout provout = new CompareWithoutSourceAdaptors().HCHX_auth.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testYkyCompareWithoutSourceAdaptorsFailure2() {
//    TestJsonObj testJsonObj = jsonDataMap.get("testHaixinCompareWithoutSourceAdaptors2");
//    CompareWithoutSourceProvin provin = (CompareWithoutSourceProvin) testJsonObj.getReqData();
//    List<String> availableProviderList = new ArrayList<>();
//    availableProviderList.add(ProviderEnum.YIKEYUN.dbKey());
//    provin.setAvailableProviderList(availableProviderList);
//    new CompareWithoutSourceAdaptors().YIKEYUN_auth.apply(provin);
//  }
//
//  @Test
//  public void testYkyCompareWithoutSourceAdaptorsFailure3() {
//    TestJsonObj testJsonObj = jsonDataMap.get("testHaixinCompareWithoutSourceAdaptors3");
//    CompareWithoutSourceProvin provin = (CompareWithoutSourceProvin) testJsonObj.getReqData();
//    List<String> availableProviderList = new ArrayList<>();
//    availableProviderList.add(ProviderEnum.YIKEYUN.dbKey());
//    provin.setAvailableProviderList(availableProviderList);
//    new CompareWithoutSourceAdaptors().YIKEYUN_auth.apply(provin);
//  }

//  @Test
//  public void testYkyCompareWithoutSourceAdaptorsFailure4() {
//    TestJsonObj testJsonObj = jsonDataMap.get("testHaixinCompareWithoutSourceAdaptors");
//    CompareWithoutSourceProvin provin = (CompareWithoutSourceProvin) testJsonObj.getReqData();
//    List<String> availableProviderList = new ArrayList<>();
//    availableProviderList.add(ProviderEnum.YIKEYUN.dbKey());
//    provin.setAvailableProviderList(availableProviderList);
//    provin.setName("朱*涛");
//    Provout provout = new CompareWithoutSourceAdaptors().YIKEYUN_auth.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testTimevaletCompareWithSourceAdaptors() {
//    TestJsonObj testJsonObj = jsonDataMap.get("testTimevaletCompareWithSourceAdaptors");
//    CompareWithSourceProvin provin = (CompareWithSourceProvin) testJsonObj.getReqData();
//    Provout provout = new CompareWithSourceAdaptors().TIMEVALE_auth.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testZXJKOrg4Adaptors() {
//    String json =
//        "{\"availableProviderList\":[\"ZXJK\"],\"certNumber\":\"913301087458306077\",\"confirmProvider\":false,\"legalRepCertNo\":\"110108196710262291\",\"legalRepName\":\"何一兵\",\"name\":\"913301087458306077\",\"nameType\":2,\"orgCertNo\":\"913301087458306077\",\"orgName\":\"杭州天谷信息科技有限公司\",\"providerName\":\"ZXJK\",\"serviceType\":\"ENTERPRISE_4_ELEMENT\",\"transactionId\":\"********04T98T15982399368620000\",\"userCenterCertType\":0}";
//    OrganProvin provin = JsonUtils.json2pojo(json, OrganProvin.class);
//    Provout provout = new Org4Adaptors().ZXJK_info4.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testHchxOrg4Adaptors() {
//    String json =
//            "{\"availableProviderList\":[\"HCHX\"],\"certNumber\":\"913301087458306077\",\"confirmProvider\":false,\"legalRepCertNo\":\"110108196710262291\",\"legalRepName\":\"何一兵\",\"name\":\"913301087458306077\",\"nameType\":2,\"orgCertNo\":\"913301087458306077\",\"orgName\":\"杭州天谷信息科技有限公司\",\"providerName\":\"ZXJK\",\"serviceType\":\"ENTERPRISE_4_ELEMENT\",\"transactionId\":\"********04T98T15982399368620000\",\"userCenterCertType\":0}";
//    OrganProvin provin = JsonUtils.json2pojo(json, OrganProvin.class);
//    Provout provout = new Org4Adaptors().HCHX_info4.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testSjbOrg4Adaptors() {
//    String json =
//            "{\"availableProviderList\":[\"SHUJUBAO\"],\"certNumber\":\"913301087458306077\",\"confirmProvider\":false,\"legalRepCertNo\":\"110108196710262291\",\"legalRepName\":\"何一兵\",\"name\":\"913301087458306077\",\"nameType\":2,\"orgCertNo\":\"913301087458306077\",\"orgName\":\"杭州天谷信息科技有限公司\",\"providerName\":\"SHUJUBAO\",\"serviceType\":\"ENTERPRISE_4_ELEMENT\",\"transactionId\":\"********04T98T15982399368620000\",\"userCenterCertType\":0}";
//    OrganProvin provin = JsonUtils.json2pojo(json, OrganProvin.class);
//    Provout provout = new Org4Adaptors().SJB_info4.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testSjbSuccessOrg4Adaptors() {
//    String json =
//            "{\"availableProviderList\":[\"SHUJUBAO\"],\"certNumber\":\"913301087458306077\",\"confirmProvider\":false,\"legalRepCertNo\":\"330722197904110013\",\"legalRepName\":\"金宏洲\",\"name\":\"913301087458306077\",\"nameType\":2,\"orgCertNo\":\"913301087458306077\",\"orgName\":\"杭州天谷信息科技有限公司\",\"providerName\":\"SHUJUBAO\",\"serviceType\":\"ENTERPRISE_4_ELEMENT\",\"transactionId\":\"********04T98T15982399368620000\",\"userCenterCertType\":0}";
//    OrganProvin provin = JsonUtils.json2pojo(json, OrganProvin.class);
//    Provout provout = new Org4Adaptors().SJB_info4.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testZszhOrg4Adaptors() {
//    String json =
//        "{\"availableProviderList\":[\"ZSZH\"],\"certNumber\":\"913301087458306077\",\"confirmProvider\":false,\"legalRepCertNo\":\"110108196710262291\",\"legalRepName\":\"何一兵\",\"name\":\"913301087458306077\",\"nameType\":2,\"orgCertNo\":\"913301087458306077\",\"orgName\":\"杭州天谷信息科技有限公司\",\"providerName\":\"ZSZH\",\"serviceType\":\"ENTERPRISE_4_ELEMENT\",\"transactionId\":\"********04T98T15982399368620000\",\"userCenterCertType\":0}";
//    OrganProvin provin = JsonUtils.json2pojo(json, OrganProvin.class);
//    Provout provout = new Org4Adaptors().ZSZH_info4.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testUnitedTelecomAdaptors() {
//    String json =
//        "{\"availableProviderList\":[\"UNITED\"],\"certNumber\":\"6*****************\",\"confirmProvider\":false,\"mobile\":\"***********\",\"name\":\"黄珂\",\"providerName\":\"UNITED\",\"serviceType\":\"MOBILE_3_ELEMENT\",\"transactionId\":\"********04T88T15961100272470000\",\"userCenterCertType\":0}";
//    TelecomauthProvin provin = JsonUtils.json2pojo(json, TelecomauthProvin.class);
//    TelecomAdaptors telecomAdaptors = new TelecomAdaptors();
//    Config config =
//        telecomAdaptors.determineConfig(
//            provin.getProviderName(), provin.getServiceType().dbValue());
//    UnitedUtil.refreshToken(config);
//    Provout provout = new TelecomAdaptors().UnitedClient_auth.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testBaiduPsnIdOcrAdaptors() {
//    String json =
//        "{\"availableProviderList\":[\"HUAWEI\",\"BAIDU\"],\"bizAppId\":\"**********\",\"bizId\":\"a1c5de19-d8dd-4dcc-8aa3-2a462439d697\",\"confirmProvider\":false,\"infoauthId\":\"a1c5de19-d8dd-4dcc-8aa3-2a462439d697\",\"providerName\":\"BAIDU\",\"serviceType\":\"BUSINESS_LICENSE_OCR\",\"shuntTag\":true,\"transactionId\":\"192168650T226T16171983495650000\",\"userCenterCertType\":0}\n";
//    OcrProvin provin = JsonUtils.json2pojo(json, OcrProvin.class);
//    JSONObject jsonObject = getImageBase64("idcardOcr.txt");
//    provin.setIdnoFaceImgData(jsonObject.getString("idnoFaceImgData"));
//    provin.setIdnoEmblemImgData(jsonObject.getString("idnoEmblemImgData"));
//    Provout provout = new PsnIdOcrAdaptors().baidu_ocr.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testHuaweiPsnIdOcrAdaptors() {
//    String json =
//        "{\"availableProviderList\":[\"HUAWEI\",\"BAIDU\"],\"bizAppId\":\"**********\",\"bizId\":\"a1c5de19-d8dd-4dcc-8aa3-2a462439d697\",\"confirmProvider\":false,\"infoauthId\":\"a1c5de19-d8dd-4dcc-8aa3-2a462439d697\",\"providerName\":\"BAIDU\",\"serviceType\":\"BUSINESS_LICENSE_OCR\",\"shuntTag\":true,\"transactionId\":\"192168650T226T16171983495650000\",\"userCenterCertType\":0}\n";
//    OcrProvin provin = JsonUtils.json2pojo(json, OcrProvin.class);
//    JSONObject jsonObject = getImageBase64("idcardOcr.txt");
//    provin.setIdnoFaceImgData(jsonObject.getString("idnoFaceImgData"));
//    provin.setIdnoEmblemImgData(jsonObject.getString("idnoEmblemImgData"));
//    Provout provout = new PsnIdOcrAdaptors().huawei_ocr.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testFailureBaiduPsnIdOcrAdaptors() {
//    String json =
//        "{\"availableProviderList\":[\"HUAWEI\",\"BAIDU\"],\"bizAppId\":\"**********\",\"bizId\":\"a1c5de19-d8dd-4dcc-8aa3-2a462439d697\",\"confirmProvider\":false,\"infoauthId\":\"a1c5de19-d8dd-4dcc-8aa3-2a462439d697\",\"providerName\":\"BAIDU\",\"serviceType\":\"BUSINESS_LICENSE_OCR\",\"shuntTag\":true,\"transactionId\":\"192168650T226T16171983495650000\",\"userCenterCertType\":0}\n";
//    OcrProvin provin = JsonUtils.json2pojo(json, OcrProvin.class);
//    JSONObject jsonObject = getImageBase64("idcardOcrFail.txt");
//    provin.setIdnoFaceImgData(jsonObject.getString("idnoFaceImgData"));
//    Provout provout = new PsnIdOcrAdaptors().baidu_ocr.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testFailureHuaweiPsnIdOcrAdaptors() {
//    String json =
//        "{\"availableProviderList\":[\"HUAWEI\",\"BAIDU\"],\"bizAppId\":\"**********\",\"bizId\":\"a1c5de19-d8dd-4dcc-8aa3-2a462439d697\",\"confirmProvider\":false,\"infoauthId\":\"a1c5de19-d8dd-4dcc-8aa3-2a462439d697\",\"providerName\":\"BAIDU\",\"serviceType\":\"BUSINESS_LICENSE_OCR\",\"shuntTag\":true,\"transactionId\":\"192168650T226T16171983495650000\",\"userCenterCertType\":0}\n";
//    OcrProvin provin = JsonUtils.json2pojo(json, OcrProvin.class);
//    JSONObject jsonObject = getImageBase64("idcardOcr.txt");
//    JSONObject jsonObjectFail = getImageBase64("idcardOcrFail.txt");
//
//    provin.setIdnoFaceImgData(jsonObjectFail.getString("idnoFaceImgData"));
//    Assert.assertNotNull(new PsnIdOcrAdaptors().huawei_ocr.apply(provin));
//
//    provin.setIdnoFaceImgData(jsonObjectFail.getString("idnoFaceImgData"));
//    provin.setIdnoEmblemImgData(jsonObjectFail.getString("idnoEmblemImgData"));
//    Assert.assertNotNull(new PsnIdOcrAdaptors().huawei_ocr.apply(provin));
//
//    provin.setIdnoFaceImgData(jsonObjectFail.getString("idnoFaceImgData"));
//    provin.setIdnoEmblemImgData(jsonObject.getString("idnoEmblemImgData"));
//    Assert.assertNotNull(new PsnIdOcrAdaptors().huawei_ocr.apply(provin));
//
//    provin.setIdnoFaceImgData(jsonObject.getString("idnoFaceImgData"));
//    provin.setIdnoEmblemImgData(jsonObjectFail.getString("idnoEmblemImgData"));
//    Assert.assertNotNull(new PsnIdOcrAdaptors().huawei_ocr.apply(provin));
//  }

  @Test
  public void testTencentPsnIdCardOcrAdaptors() {
    String json =
            "{\"availableProviderList\":[\"TENCENT\"],\"bizAppId\":\"**********\",\"bizId\":\"a1c5de19-d8dd-4dcc-8aa3-2a462439d697\",\"confirmProvider\":false,\"infoauthId\":\"a1c5de19-d8dd-4dcc-8aa3-2a462439d697\",\"providerName\":\"TENCENT\",\"serviceType\":\"BUSINESS_LICENSE_OCR\",\"shuntTag\":true,\"transactionId\":\"192168650T226T16171983495650000\",\"userCenterCertType\":0}\n";
    OcrProvin provin = JsonUtils.json2pojo(json, OcrProvin.class);
    JSONObject jsonObject = getImageBase64("idcardOcr.txt");
    provin.setIdnoFaceImgData(jsonObject.getString("idnoFaceImgData"));
    provin.setIdnoEmblemImgData(jsonObject.getString("idnoEmblemImgData"));
    Provout provout = new PsnIdOcrAdaptors().tencent_ocr.apply(provin);
    Assert.assertTrue(true);
    Assert.assertNotNull(provout);
  }

//  @Test
//  public void testBaiduBankCardOcrAdaptors() {
//    String json =
//        "{\"availableProviderList\":[\"HUAWEI\",\"BAIDU\"],\"bizAppId\":\"**********\",\"bizId\":\"558ac326-1960-43d6-a7df-9be4bf266206\",\"confirmProvider\":false,\"infoauthId\":\"558ac326-1960-43d6-a7df-9be4bf266206\",\"providerName\":\"HUAWEI\",\"serviceType\":\"BANKCARD_OCR\",\"shuntTag\":true,\"transactionId\":\"192168650T224T16171982548190000\",\"userCenterCertType\":0}\n";
//    BankCardOcrProvin provin = JsonUtils.json2pojo(json, BankCardOcrProvin.class);
//    JSONObject jsonObject = getImageBase64("bankcardOcr.txt");
//    provin.setFrontImgKey(jsonObject.getString("frontImgKey"));
//    Provout provout = new BankCardOcrAdaptors().baidu_ocr.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }
//
//  @Ignore
//  @Test
//  public void testHuaweiBankCardOcrAdaptors() {
//    String json =
//        "{\"availableProviderList\":[\"HUAWEI\",\"BAIDU\"],\"bizAppId\":\"**********\",\"bizId\":\"558ac326-1960-43d6-a7df-9be4bf266206\",\"confirmProvider\":false,\"infoauthId\":\"558ac326-1960-43d6-a7df-9be4bf266206\",\"providerName\":\"HUAWEI\",\"serviceType\":\"BANKCARD_OCR\",\"shuntTag\":true,\"transactionId\":\"192168650T224T16171982548190000\",\"userCenterCertType\":0}\n";
//    BankCardOcrProvin provin = JsonUtils.json2pojo(json, BankCardOcrProvin.class);
//    JSONObject jsonObject = getImageBase64("bankcardOcr.txt");
//    provin.setFrontImgKey(jsonObject.getString("frontImgKey"));
//    Provout provout = new BankCardOcrAdaptors().huawei_ocr.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

  @Test
  public void testTencentBankCardOcrAdaptors() {
    String json =
            "{\"availableProviderList\":[\"TENCENT\"],\"bizAppId\":\"**********\",\"bizId\":\"558ac326-1960-43d6-a7df-9be4bf266206\",\"confirmProvider\":false,\"infoauthId\":\"558ac326-1960-43d6-a7df-9be4bf266206\",\"providerName\":\"TENCENT\",\"serviceType\":\"BANKCARD_OCR\",\"shuntTag\":true,\"transactionId\":\"192168650T224T16171982548190000\",\"userCenterCertType\":0}\n";
    BankCardOcrProvin provin = JsonUtils.json2pojo(json, BankCardOcrProvin.class);
    JSONObject jsonObject = getImageBase64("bankcardOcr.txt");
    provin.setFrontImgKey(jsonObject.getString("frontImgKey"));
    Provout provout = new BankCardOcrAdaptors().tencent_ocr.apply(provin);
    Assert.assertTrue(true);
    Assert.assertNotNull(provout);
  }

//  @Test
//  public void testBaiduOrgLicenceOcrAdaptors() {
//    String json =
//        "{\"availableProviderList\":[\"HUAWEI\",\"BAIDU\"],\"bizAppId\":\"**********\",\"bizId\":\"a1c5de19-d8dd-4dcc-8aa3-2a462439d697\",\"confirmProvider\":false,\"infoauthId\":\"a1c5de19-d8dd-4dcc-8aa3-2a462439d697\",\"providerName\":\"BAIDU\",\"serviceType\":\"BUSINESS_LICENSE_OCR\",\"shuntTag\":true,\"transactionId\":\"192168650T226T16171983495650000\",\"userCenterCertType\":0}\n";
//    OrgLicenseOcrProvin provin = JsonUtils.json2pojo(json, OrgLicenseOcrProvin.class);
//    JSONObject jsonObject = getImageBase64("orgLicense.txt");
//    provin.setImgKey(jsonObject.getString("imgKey"));
//    Provout provout = new OrgLicenceOcrAdaptors().baidu_ocr.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Ignore
//  @Test
//  public void testHuaweiOrgLicenceOcrAdaptors() {
//    String json =
//        "{\"availableProviderList\":[\"HUAWEI\",\"BAIDU\"],\"bizAppId\":\"**********\",\"bizId\":\"a1c5de19-d8dd-4dcc-8aa3-2a462439d697\",\"confirmProvider\":false,\"infoauthId\":\"a1c5de19-d8dd-4dcc-8aa3-2a462439d697\",\"providerName\":\"BAIDU\",\"serviceType\":\"BUSINESS_LICENSE_OCR\",\"shuntTag\":true,\"transactionId\":\"192168650T226T16171983495650000\",\"userCenterCertType\":0}\n";
//    OrgLicenseOcrProvin provin = JsonUtils.json2pojo(json, OrgLicenseOcrProvin.class);
//    JSONObject jsonObject = getImageBase64("orgLicense.txt");
//    provin.setImgKey(jsonObject.getString("imgKey"));
//    Provout provout = new OrgLicenceOcrAdaptors().huawei_ocr.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testBaiduDriversLicenceOcrAdaptors() {
//    String json =
//        "{\"availableProviderList\":[\"HUAWEI\",\"BAIDU\"],\"bizAppId\":\"**********\",\"bizId\":\"536e7d08-0f25-4c44-ad4d-84ef9b3db6da\",\"confirmProvider\":false,\"infoauthId\":\"536e7d08-0f25-4c44-ad4d-84ef9b3db6da\",\"providerName\":\"HUAWEI\",\"serviceType\":\"DRIVER_LICENSE_OCR\",\"shuntTag\":true,\"transactionId\":\"192168650T227T16171983999880000\",\"userCenterCertType\":0}\n";
//    DrivingLicenceOcrProvin provin = JsonUtils.json2pojo(json, DrivingLicenceOcrProvin.class);
//    JSONObject jsonObject = getImageBase64("drivinglicence.txt");
//    provin.setFaceImgData(jsonObject.getString("image"));
//    Provout provout = new DriversLicenceOcrAdaptors().baidu_ocr.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testHuaweiDriversLicenceOcrAdaptors() {
//    String json =
//        "{\"availableProviderList\":[\"HUAWEI\",\"BAIDU\"],\"bizAppId\":\"**********\",\"bizId\":\"536e7d08-0f25-4c44-ad4d-84ef9b3db6da\",\"confirmProvider\":false,\"infoauthId\":\"536e7d08-0f25-4c44-ad4d-84ef9b3db6da\",\"providerName\":\"HUAWEI\",\"serviceType\":\"DRIVER_LICENSE_OCR\",\"shuntTag\":true,\"transactionId\":\"192168650T227T16171983999880000\",\"userCenterCertType\":0}\n";
//    DrivingLicenceOcrProvin provin = JsonUtils.json2pojo(json, DrivingLicenceOcrProvin.class);
//    JSONObject jsonObject = getImageBase64("drivinglicence.txt");
//    provin.setFaceImgData(jsonObject.getString("faceImgData"));
//    provin.setEmblemImgData(jsonObject.getString("emblemImgData"));
//    Provout provout = new DriversLicenceOcrAdaptors().huawei_ocr.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

  @Test
  public void testTencentDriversLicenceOcrAdaptors() {
    String json =
            "{\"availableProviderList\":[\"TENCENT\"],\"bizAppId\":\"**********\",\"bizId\":\"536e7d08-0f25-4c44-ad4d-84ef9b3db6da\",\"confirmProvider\":false,\"infoauthId\":\"536e7d08-0f25-4c44-ad4d-84ef9b3db6da\",\"providerName\":\"TENCENT\",\"serviceType\":\"BUSINESS_LICENSE_OCR\",\"shuntTag\":true,\"transactionId\":\"192168650T227T16171983999880000\",\"userCenterCertType\":0}\n";
    DrivingLicenceOcrProvin provin = JsonUtils.json2pojo(json, DrivingLicenceOcrProvin.class);
    JSONObject jsonObject = getImageBase64("drivinglicence.txt");
    provin.setFaceImgData(jsonObject.getString("image"));
    provin.setEmblemImgData(jsonObject.getString("emblemImgData"));
    Provout provout = new DriversLicenceOcrAdaptors().tencent_ocr.apply(provin);
    Assert.assertTrue(true);
    Assert.assertNotNull(provout);
  }


//  @Ignore
//  @Test
//  public void testBaiduDriversPermitOcrAdaptors() {
//    String json =
//        "{\"availableProviderList\":[\"HUAWEI\",\"BAIDU\"],\"bizAppId\":\"**********\",\"bizId\":\"8d0bf01f-dc76-44e3-b35e-e25e68191017\",\"confirmProvider\":false,\"infoauthId\":\"8d0bf01f-dc76-44e3-b35e-e25e68191017\",\"providerName\":\"BAIDU\",\"serviceType\":\"DRIVING_LICENSE_OCR\",\"shuntTag\":true,\"transactionId\":\"192168650T228T16171984476330000\",\"userCenterCertType\":0}\n";
//    DrivingPermitOcrProvin provin = JsonUtils.json2pojo(json, DrivingPermitOcrProvin.class);
//    JSONObject jsonObject = getImageBase64("drivingPermit.txt");
//    provin.setFaceImgData(jsonObject.getString("image"));
//    Provout provout = new DriversPermitOcrAdaptors().baidu_ocr.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

  //TODO 华为没钱。临时注销
//  @Test
//  @Ignore
//  public void testHuaweiDriversPermitOcrAdaptors() {
//    String json =
//        "{\"availableProviderList\":[\"HUAWEI\",\"BAIDU\"],\"bizAppId\":\"**********\",\"bizId\":\"8d0bf01f-dc76-44e3-b35e-e25e68191017\",\"confirmProvider\":false,\"infoauthId\":\"8d0bf01f-dc76-44e3-b35e-e25e68191017\",\"providerName\":\"BAIDU\",\"serviceType\":\"DRIVING_LICENSE_OCR\",\"shuntTag\":true,\"transactionId\":\"192168650T228T16171984476330000\",\"userCenterCertType\":0}\n";
//    DrivingPermitOcrProvin provin = JsonUtils.json2pojo(json, DrivingPermitOcrProvin.class);
//    JSONObject jsonObject = getImageBase64("drivingPermit.txt");
//    provin.setFaceImgData(jsonObject.getString("image"));
//    Provout provout = new DriversPermitOcrAdaptors().huawei_ocr.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

  @Test
  public void testTencentDriversPermitOcrAdaptors() {
    String json =
            "{\"availableProviderList\":[\"TENCENT\"],\"bizAppId\":\"**********\",\"bizId\":\"8d0bf01f-dc76-44e3-b35e-e25e68191017\",\"confirmProvider\":false,\"infoauthId\":\"8d0bf01f-dc76-44e3-b35e-e25e68191017\",\"providerName\":\"TENCENT\",\"serviceType\":\"BUSINESS_LICENSE_OCR\",\"shuntTag\":true,\"transactionId\":\"192168650T228T16171984476330000\",\"userCenterCertType\":0}\n";
    DrivingPermitOcrProvin provin = JsonUtils.json2pojo(json, DrivingPermitOcrProvin.class);
    JSONObject jsonObject = getImageBase64("drivingPermit.txt");
    provin.setFaceImgData(jsonObject.getString("image"));
    Provout provout = new DriversPermitOcrAdaptors().tencent_ocr.apply(provin);
    Assert.assertTrue(true);
    Assert.assertNotNull(provout);
  }


  @Test
  public void testTencentOrgLicenceOcrAdaptors() {
    String json =
        "{\"availableProviderList\":[\"TENCENT\"],\"bizAppId\":\"**********\",\"bizId\":\"a1c5de19-d8dd-4dcc-8aa3-2a462439d697\",\"confirmProvider\":false,\"infoauthId\":\"a1c5de19-d8dd-4dcc-8aa3-2a462439d697\",\"providerName\":\"TENCENT\",\"serviceType\":\"BUSINESS_LICENSE_OCR\",\"shuntTag\":true,\"transactionId\":\"192168650T226T16171983495650000\",\"userCenterCertType\":0}\n";
    OrgLicenseOcrProvin provin = JsonUtils.json2pojo(json, OrgLicenseOcrProvin.class);
    JSONObject jsonObject = getImageBase64("orgLicense.txt");
    provin.setImgKey(jsonObject.getString("imgKey2"));
    Provout provout = new OrgLicenceOcrAdaptors().tencent_ocr.apply(provin);
    Assert.assertTrue(true);
    Assert.assertNotNull(provout);
  }

//  @Test
//  public void testMayiOrg2Adaptors() {
//    String json =
//        "{\"availableProviderList\":[\"MAYI\"],\"bizAppId\":\"**********\",\"bizId\":\"0e9fc735-79ae-4fb5-8933-f98bc4f0e809\",\"certNumber\":\"913301087458306077\",\"codeUSC\":\"913301087458306077\",\"confirmProvider\":false,\"name\":\"913301087458306077\",\"nameType\":2,\"orgCertNo\":\"913301087458306077\",\"orgName\":\"杭州天谷信息科技有限公司18\",\"organSelectType\":\"TwoElement\",\"providerName\":\"QIXINBAO\",\"serviceType\":\"ENTERPRISE_2_ELEMENT\",\"shuntTag\":false,\"transactionId\":\"101001469T165T16387977847280003\",\"userCenterCertType\":0}";
//    OrganProvin provin = JsonUtils.json2pojo(json, OrganProvin.class);
//    Provout provout = new Org2Adaptors().Mayi_twoElement.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testMayiOrg3Adaptors() {
//    String json =
//        "{\"availableProviderList\":[\"MAYI\",\"SHUJUBAO\",\"QIXINBAO\"],\"bizAppId\":\"**********\",\"bizId\":\"3ab1a087-2f2d-4eee-b3c7-58594f3e7ff6\",\"certNumber\":\"913301087458306077\",\"codeUSC\":\"913301087458306077\",\"confirmProvider\":false,\"legalRepName\":\"金宏洲\",\"name\":\"913301087458306077\",\"nameType\":2,\"orgCertNo\":\"913301087458306077\",\"orgName\":\"杭州天谷信息科技有限公司19\",\"organSelectType\":\"ByCertNo\",\"providerName\":\"QIXINBAO\",\"serviceType\":\"ENTERPRISE_3_ELEMENT\",\"shuntTag\":false,\"transactionId\":\"10100910T163T16388615959780005\",\"userCenterCertType\":0}";
//    OrganProvin provin = JsonUtils.json2pojo(json, OrganProvin.class);
//    Provout provout = new Org3Adaptors().MaYi_organThreeElement.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testZszhOrg3Adaptors() {
//    String json =
//        "{\"availableProviderList\":[\"ZSZH\"],\"bizAppId\":\"**********\",\"bizId\":\"3ab1a087-2f2d-4eee-b3c7-58594f3e7ff6\",\"certNumber\":\"913301087458306077\",\"codeUSC\":\"913301087458306077\",\"confirmProvider\":false,\"legalRepName\":\"金宏洲\",\"name\":\"913301087458306077\",\"nameType\":2,\"orgCertNo\":\"913301087458306077\",\"orgName\":\"杭州天谷信息科技有限公司19\",\"organSelectType\":\"ByCertNo\",\"providerName\":\"ZSZH\",\"serviceType\":\"ENTERPRISE_3_ELEMENT\",\"shuntTag\":false,\"transactionId\":\"10100910T163T16388615959780005\",\"userCenterCertType\":0}";
//    OrganProvin provin = JsonUtils.json2pojo(json, OrganProvin.class);
//    Provout provout = new Org3Adaptors().ZSZH_organThreeElement.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

//  @Test
//  public void testQxbCommunityAdaptors() {
//    String json = "{\"availableProviderList\":[\"QIXINBAO\"],\"bizAppId\":\"**********\",\"bizId\":\"4d685f0b-4f93-4263-b4b8-ef08c33c0497\",\"certNumber\":\"1243000044880463XP\",\"codeUSC\":\"1243000044880463XP\",\"confirmProvider\":false,\"legalRepName\":\"郭谷斌\",\"name\":\"1243000044880463XP\",\"nameType\":2,\"orgCertNo\":\"1243000044880463XP\",\"orgName\":\"潇湘晨报社\",\"organSelectType\":\"SOCIALORGANIZATION\",\"providerName\":\"QIXINBAO_HA\",\"serviceType\":\"SOCIALORG_3_ELEMENT\",\"shuntTag\":false,\"transactionId\":\"1921687177T125T16749736974470000\",\"userCenterCertType\":0}";
//    OrganProvin provin = JsonUtils.json2pojo(json, OrganProvin.class);
//    Provout provout = new CommunityAdaptors().QXB_socialOrganizationQuery_Ha.apply(provin);
//    Assert.assertTrue(true);
//    Assert.assertNotNull(provout);
//  }

  @Test
  public void tencentUtil_convertocr() {
    HttpEntity httpEntity = new HttpEntity() {
      @Override
      public boolean isRepeatable() {
        return false;
      }

      @Override
      public boolean isChunked() {
        return false;
      }

      @Override
      public long getContentLength() {
        return 0;
      }

      @Override
      public Header getContentType() {
        return null;
      }

      @Override
      public Header getContentEncoding() {
        return null;
      }

      @Override
      public InputStream getContent() throws IOException, UnsupportedOperationException {
        return null;
      }

      @Override
      public void writeTo(OutputStream outputStream) throws IOException {

      }

      @Override
      public boolean isStreaming() {
        return false;
      }

      @Override
      public void consumeContent() throws IOException {

      }
    };

    HttpResponse httpResponse = new HttpResponse() {
      @Override
      public ProtocolVersion getProtocolVersion() {
        return null;
      }

      @Override
      public boolean containsHeader(String s) {
        return false;
      }

      @Override
      public Header[] getHeaders(String s) {
        return new Header[0];
      }

      @Override
      public Header getFirstHeader(String s) {
        return null;
      }

      @Override
      public Header getLastHeader(String s) {
        return null;
      }

      @Override
      public Header[] getAllHeaders() {
        return new Header[0];
      }

      @Override
      public void addHeader(Header header) {

      }

      @Override
      public void addHeader(String s, String s1) {

      }

      @Override
      public void setHeader(Header header) {

      }

      @Override
      public void setHeader(String s, String s1) {

      }

      @Override
      public void setHeaders(Header[] headers) {

      }

      @Override
      public void removeHeader(Header header) {

      }

      @Override
      public void removeHeaders(String s) {

      }

      @Override
      public HeaderIterator headerIterator() {
        return null;
      }

      @Override
      public HeaderIterator headerIterator(String s) {
        return null;
      }

      @Override
      public HttpParams getParams() {
        return null;
      }

      @Override
      public void setParams(HttpParams httpParams) {

      }

      @Override
      public StatusLine getStatusLine() {
        return null;
      }

      @Override
      public void setStatusLine(StatusLine statusLine) {

      }

      @Override
      public void setStatusLine(ProtocolVersion protocolVersion, int i) {

      }

      @Override
      public void setStatusLine(ProtocolVersion protocolVersion, int i, String s) {

      }

      @Override
      public void setStatusCode(int i) throws IllegalStateException {

      }

      @Override
      public void setReasonPhrase(String s) throws IllegalStateException {

      }

      @Override
      public HttpEntity getEntity() {
        return httpEntity;
      }

      @Override
      public void setEntity(HttpEntity httpEntity) {

      }

      @Override
      public Locale getLocale() {
        return null;
      }

      @Override
      public void setLocale(Locale locale) {

      }
    };

    String content = "{\"Response\":{\"Address\":\"\",\"AdvancedInfo\":\"{\\\"WarnInfos\\\":[-9101]}\",\"Authority\":\"东乡县公安局\",\"Birth\":\"\",\"IdNum\":\"\",\"Name\":\"\",\"Nation\":\"\",\"RequestId\":\"34ea6ed1-db60-4f86-907b-a616398994cd\",\"Sex\":\"\",\"ValidDate\":\"2010.09.03-2020.09.03\"}}";
    Response response = new Response(httpResponse);
    response.setContent(content.getBytes());
    Assert.assertNotNull(TencentUtil.convertocr(response));

    content = "{\"Response\":{\"BankInfo\":\"杭州商业银行(********)\",\"BorderCutImage\":null,\"CardName\":\"借记IC卡\",\"CardNo\":\"623061571013051979\",\"CardNoImage\":null,\"CardType\":\"借记卡\",\"QualityValue\":null,\"RequestId\":\"a73efc70-3fe2-4031-b33b-f4e0ddf85a97\",\"ValidDate\":\"12/2025\",\"WarningCode\":[-9111]}}";
    response.setContent(content.getBytes());
    Assert.assertNotNull(TencentUtil.convertBankCardProvout(response));
  }

  @SneakyThrows
  private static JSONObject getImageBase64(String path) {

    InputStream input;
    input = ProviderClientTest.class.getClass().getResourceAsStream(path);
    if (input == null) {
      String classPathPath = "classpath:" + path;
      try {
        input = ResourceUtils.getURL(classPathPath).openStream();
      } catch (IOException e) {
        e.printStackTrace();
      }
    }
    if (input == null) {
      throw new RuntimeException("can not load file:" + path);
    }

    byte[] data = IOUtil.readStreamAsByteArray(input);

    String text = new String(data);
    return JSON.parseObject(text);
  }
}
