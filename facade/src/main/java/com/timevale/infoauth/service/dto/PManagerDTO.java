package com.timevale.infoauth.service.dto;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PManagerDTO extends ToString {

  private Integer id;

  private String name;

  private Integer type;

  private Integer providerStatus;

  private Boolean onOffMock;

  private String extendJson;

  private String qualityTag;
}
