package com.timevale.infoauth.dal.infoauth.dataobject.agreement;

import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 通用协议管理-协议信息主表
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
//@TableName("common_agreement_info")
public class CommonAgreementInfoDO extends BaseDO{
//  /** id 主键. */
//  @TableId(
//          type = IdType.AUTO
//  )
  private Long id;
  /** 协议状态。0=待生效 1=生效中 2=作废. */
  private Integer status;

  /**
   * 删除状态。0=未删除 1=已删除.
   */
  private Integer deleted  = 0;
  /** 协议ID. */
  private String agreementId;
  /** 协议标题. */
  private String agreementTitle;

  /** 协议类型.ca_cert: ca证书场景不允许编辑和删除. */
  private String agreementType;
  /** 协议备注. */
  private String agreementMark;
  /** 最新协议版本. */
  private String releaseVersion;
  /** 业务分类， */
  private String categoryRef;

  /** 操作人. */
  private String operator;
  /** 创建人. */
  private String creator;


}
