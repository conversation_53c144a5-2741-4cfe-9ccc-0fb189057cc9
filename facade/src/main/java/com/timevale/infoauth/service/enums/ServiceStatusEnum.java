package com.timevale.infoauth.service.enums;

import java.util.Objects;

/** @Author: <PERSON> @Description: 认证状态枚举 @Date: Created in 2018/7/17 @Modified By: */
public enum ServiceStatusEnum {

  /** 认证中 */
  PROCESSING(1),

  /** 认证成功 */
  FINISHED(2),

  /** 认证失败 */
  FAILAED(3),

  /** 认证异常 */
  EXCEPTION(4),
  ;

  public int dbValue() {
    return dbValue;
  }

  ServiceStatusEnum(int dbValue) {
    this.dbValue = dbValue;
  }

  private int dbValue;


  public static boolean isSuccess(Integer status){
     return Objects.equals(FINISHED.dbValue, status);
  }
}
