package com.timevale.infoauth.service.api;

import com.alibaba.fastjson.JSON;
import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.enums.InfoAuthServiceType;
import com.timevale.infoauth.service.enums.ProvResult;
import com.timevale.infoauth.service.enums.UserCenterCertTypeEnum;
import com.timevale.infoauth.service.impl.common.dto.Provout;
import com.timevale.infoauth.service.impl.provider.biz.adapter.bankfour.BankAuthUnionPayInternational;
import com.timevale.infoauth.service.impl.dto.BankauthProvin;
import com.timevale.infoauth.service.impl.utils.UUIDUtil;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2020/4/16 下午5:02
 */
@JsonDataIgnore
public class BankAuthUnionPayTest extends ApplicationTest {

  @Resource BankAuthUnionPayInternational bankAuthUnionPayInternational;

  @Test
  public void authSuccess() {
    BankauthProvin input = new BankauthProvin();
    input.setCardno("810000200404240113");
    input.setName("何晗旭");
    input.setCertNumber("6217896400001027195");
    input.setMobile("***********");
    input.setProviderName("UNIONPAY_INTERNATIONAL");
    input.setServiceType(InfoAuthServiceType.BANKCARD_4_ELEMENT_INTERNATIONAL);
    input.setUserCenterCertType(UserCenterCertTypeEnum.CRED_PSN_CH_TWCARD.dbValue());
    input.setBizId(UUID.randomUUID().toString());
    Provout out = bankAuthUnionPayInternational.auth(input);
    Assert.assertNotNull(out);
    Assert.assertEquals(ProvResult.FAILURE, out.getProvResult());
  }

  @Test
  public void authFail() {
    BankauthProvin input = new BankauthProvin();
    input.setCardno("6217856200026018460");
    input.setName("邱静谊");
    input.setCertNumber("********");
    input.setMobile("***********");
    input.setProviderName("UNIONPAY_INTERNATIONAL");
    input.setServiceType(InfoAuthServiceType.BANKCARD_4_ELEMENT_INTERNATIONAL);
    input.setUserCenterCertType(UserCenterCertTypeEnum.CRED_PSN_CH_TWCARD.dbValue());
    input.setBizId(UUID.randomUUID().toString());
    Provout out = bankAuthUnionPayInternational.auth(input);
    Assert.assertNotNull(out);
    Assert.assertEquals(ProvResult.FAILURE, out.getProvResult());
  }
}
