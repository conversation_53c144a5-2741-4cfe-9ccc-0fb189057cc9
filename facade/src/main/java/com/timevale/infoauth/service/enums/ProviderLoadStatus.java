package com.timevale.infoauth.service.enums;

public enum ProviderLoadStatus {
    ONLINE(1,"上线"),
    OFFLINE(2,"下线"),
    PRE(3,"新增"),
    ERROR(4,"故障");

    ProviderLoadStatus(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    private int value;
    private String desc;

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 供应商是否在线
     */
    public static boolean isRunning(int status){
        return status == ONLINE.value;
    }
}
