package com.timevale.infoauth.dal.infoauth.dataobject.domain;

import lombok.Data;

/**
 * 客户端子域（DO）- 目前没有设计独立库表存储，未来规划
 *
 * <AUTHOR> href="mailto:<EMAIL>">jiuchen</a>
 */
@Data
public class DomainClientDO {

  /** metaDesc 服务商描述. */
  private String metaDesc;
  /** metaUrl 供应商地址. */
  private String metaUrl;
  /** metaAccount 供应商账号. */
  private String metaAccount;
  /** metaPassword 密码. */
  private String metaPassword;
  /** metaSignKey 签名密钥. */
  private String metaSignKey;
  /** metaVerifyKey 验签密钥. */
  private String metaVerifyKey;

  private Integer connectTimeout;

  private Integer readTimeout;

  private Integer tokenTtl;

  private String mockUrl;

  private String extendJson;
}
