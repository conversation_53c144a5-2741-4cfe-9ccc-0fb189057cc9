package com.timevale.infoauth.dal.infoauth.dataobject.agreement;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 通用协议管理-协议内容发布记录表
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
//@TableName("common_agreement_release_record")
public class CommonAgreementReleaseRecordDO extends BaseDO{

  /** id 主键. */
//  @TableId(
//          type = IdType.AUTO
//  )
  private Long id;
  /** 发布状态。0=待发布 1=发布成功 2=作废. */
  private Integer enableStatus;

  /** 协议ID. */
  private String agreementId;
  /** 协议发布时间. */
  private Date releaseTime;

  /** 协议语言. */
  private String lang;

  /** 协议简称. */
  private String shortName;

  /** 协议全称. */
  private String fullName;
  /** 协议发布记录ID. */
  private String releaseRecordId;
  /** 协议发布备注. */
  private String releaseRemark;
  /** 协议发布版本. */
  private String releaseVersion;
  /** 协议内容. */
  private String content;
  /** 协议内容存储类型 htmlContent=html文本 htmlUrl=http地址 pdfUrl=pdf地址 zeyuanUrl=泽元系统Url. */
  private String contentType;

  /** 是否需要填充，0=不需要，1=需要 */
  private int contentFill;
  private String contentFillKey;

  /** 操作人. */
  private String operator;
  /** 创建人. */
  private String creator;

}
