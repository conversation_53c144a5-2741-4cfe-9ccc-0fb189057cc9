package com.timevale.infoauth.dal.infoauth.dataobject;

import com.timevale.infoauth.dal.infoauth.utils.StringUtil;
import lombok.Getter;

@Getter
public class InfoServiceOrgLicenseOcrDO {
  private Integer id;
  private String infoauthId;
  private String providerId;
  private String imgKey;
  private Integer result;
  private Integer serviceStatus;
  private String providerResult;
  private String ocrName;
  private String ocrCertNo;
  private String ocrType;
  private String ocrAddress;
  private String ocrLegalRepName;
  private String ocrCapital;
  private String ocrEstablishDate;
  private String ocrValidTerm;
  private String ocrScope;
  private String createTime;
  private String modifyTime;

  public void setId(Integer id) {
    this.id = id;
  }

  public void setInfoauthId(String infoauthId) {
    this.infoauthId = infoauthId;
  }

  public void setProviderId(String providerId) {
    this.providerId = providerId;
  }

  public void setImgKey(String imgKey) {
    this.imgKey = StringUtil.truncation(imgKey,300);
  }

  public void setResult(Integer result) {
    this.result = result;
  }

  public void setServiceStatus(Integer serviceStatus) {
    this.serviceStatus = serviceStatus;
  }

  public void setProviderResult(String providerResult) {
    this.providerResult = providerResult;
  }

  public void setOcrName(String ocrName) {
    this.ocrName = ocrName;
  }

  public void setOcrCertNo(String ocrCertNo) {
    this.ocrCertNo = ocrCertNo;
  }

  public void setOcrType(String ocrType) {
    this.ocrType = ocrType;
  }

  public void setOcrAddress(String ocrAddress) {
    this.ocrAddress = ocrAddress;
  }

  public void setOcrLegalRepName(String ocrLegalRepName) {
    this.ocrLegalRepName = ocrLegalRepName;
  }

  public void setOcrCapital(String ocrCapital) {
    this.ocrCapital = ocrCapital;
  }

  public void setOcrEstablishDate(String ocrEstablishDate) {
    this.ocrEstablishDate = ocrEstablishDate;
  }

  public void setOcrValidTerm(String ocrValidTerm) {
    this.ocrValidTerm = ocrValidTerm;
  }

  public void setOcrScope(String ocrScope) {
    this.ocrScope = ocrScope;
  }

  public void setCreateTime(String createTime) {
    this.createTime = createTime;
  }

  public void setModifyTime(String modifyTime) {
    this.modifyTime = modifyTime;
  }
}
