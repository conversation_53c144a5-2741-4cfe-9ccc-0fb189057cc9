package com.timevale.infoauth.service.exception;

import com.timevale.infoauth.service.enums.ProvResult;
import com.timevale.infoauth.service.model.IProviderEnumeration;

/** @Author: <PERSON> @Description: 供应商异常 @Date: Created in 2018/7/18 @Modified By: */
public class ProviderException extends RuntimeException {

  /** 字段： serialVersionUID[long] :  */
  private static final long serialVersionUID = 3408998418783834650L;

  private String rawErrorCode;

  private ProvResult provResult;

  private IProviderEnumeration provider;

  public ProviderException(String rawErrorCode, ProvResult provResult) {
    this.rawErrorCode = rawErrorCode;
    this.provResult = provResult;
  }

  public ProviderException(
      IProviderEnumeration provider, String rawErrorCode, ProvResult provResult) {
    this.rawErrorCode = rawErrorCode;
    this.provResult = provResult;
    this.provider = provider;
  }

  public String getRawErrorCode() {
    return rawErrorCode;
  }

  public ProvResult getProvResult() {
    return provResult;
  }

  public IProviderEnumeration getProvider() {
    return provider;
  }

  public void setProvider(IProviderEnumeration provider) {
    this.provider = provider;
  }
}
