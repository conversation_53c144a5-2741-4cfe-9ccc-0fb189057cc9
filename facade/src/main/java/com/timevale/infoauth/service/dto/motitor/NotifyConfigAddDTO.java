package com.timevale.infoauth.service.dto.motitor;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/7/17 21:17
 */
@Data
public class NotifyConfigAddDTO extends ToString {

    /** 通知配置ID. */
    private String configId;
    /** 一级空间ID. */
    private String alertId;

    //是否启动
    private Integer switchEnabled;
    /** 电话通知开关[0关闭,1开启]. */
    private Integer callEnabled;
    /** 短信通知开关[0关闭,1开启]. */
    private Integer smsEnabled;
    /** 钉钉工作通知开关[0关闭,1开启]. */
    private Integer dingCropEnabled;
    /** 钉钉机器人通知开关[0关闭,1开启]. */
    private Integer dingRobotEnabled;
    /** 钉钉机器人token. */
    private String dingRobotToken;
    /** 推送用户. */
    private String notifyUsernames;
}
