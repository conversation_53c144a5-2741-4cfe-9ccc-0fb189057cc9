package com.timevale.infoauth.service.api;

import com.timevale.infoauth.service.dto.PDomainDTO;
import com.timevale.infoauth.service.dto.PManagerDTO;
import com.timevale.mandarin.common.annotation.RestClient;

import java.util.List;

/**
 * 应用层服务（管理子域 - 核心域）
 *
 * <AUTHOR> href="mailto:<EMAIL>">jiuchen</a>
 */
@RestClient(serviceId = "infoauth-service")
public interface ProviderManagerService {

  /**
   * 创建/重建 领域
   *
   * @param pDomainDTO
   */
  void createDomain(PDomainDTO pDomainDTO);

  /**
   * 删除领域
   *
   * @param managerId
   */
  void removeDomain(Integer managerId);

  /**
   * 保存管理子域
   *
   * @param pManagerDTO
   */
  void save(PManagerDTO pManagerDTO);

  /**
   * 上下架
   *
   * @param managerId
   */
  void onOffline(Integer managerId);

  /**
   * 开关mock
   *
   * @param managerId
   */
  void onOffMock(Integer managerId);

  /**
   * 查询一条管理子域
   *
   * @param managerId
   * @return
   */
  PManagerDTO getManagerById(Integer managerId);

  /**
   * 查询所有管理子域
   *
   * @return
   */
  List<PManagerDTO> queryManagerAll();

  /**
   * 刷新token
   *
   * @param managerId
   */
  void refreshToken(Integer managerId);
}
