package com.timevale.infoauth.dal.infoauth.dao.monitor;

import com.timevale.infoauth.dal.infoauth.dataobject.monitor.ForwardConfigDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 * @since 2024/12/2
 */
public interface ForwardConfigDAO {

    @Insert({
            "<script>",
            "INSERT INTO `monitor_provider_forward_config` (`config_id`,`alert_id`, `switch_enabled`, `topic_name`, `topic_tag` ,`desc`) VALUES ",
            "(#{configId},#{alertId}, #{switchEnabled}, #{topicName}, #{topicTag}, #{desc})",
            "</script>"
    })
    int insert(ForwardConfigDO entity);

    @Update({
            "<script>",
            "UPDATE `monitor_provider_forward_config` ",
            "<set>",
            "<if test=\"alertId != null\">",
            "alert_id = #{alertId},",
            "</if>",
            "<if test=\"switchEnabled != null\">",
            "switch_enabled = #{switchEnabled},",
            "</if>",
            "<if test=\"topicName != null\">",
            "topic_name = #{topicName},",
            "</if>",
            "<if test=\"topicTag != null\">",
            "topic_tag = #{topicTag},",
            "</if>",
            "<if test=\"desc != null\">",
            "`desc`  = #{desc},",
            "</if>",
            "</set>",
            " WHERE `config_id` = #{configId}",
            "</script>",
    })
    void updateById(ForwardConfigDO entity);


    @Select({
            "<script>",
            "SELECT * FROM `monitor_provider_forward_config`",
            " WHERE `alert_id`=#{alertId}  order by id desc  LIMIT 1",
            "</script>",
    })
    ForwardConfigDO getByAlertId(String alertId);
}
