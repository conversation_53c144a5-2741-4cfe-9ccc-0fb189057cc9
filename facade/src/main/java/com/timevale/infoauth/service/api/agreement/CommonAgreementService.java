package com.timevale.infoauth.service.api.agreement;

import com.timevale.infoauth.service.enums.agreement.CommonAgreementErrorCodeEnum;
import com.timevale.infoauth.service.model.CommonResultEnum;
import com.timevale.infoauth.service.model.SupportResult;
import com.timevale.infoauth.service.request.agreement.*;
import com.timevale.infoauth.service.response.agreement.*;
import com.timevale.mandarin.common.annotation.RestClient;

/**
 * 通用协议 api
 * <AUTHOR>
 * @since 2024/12/3 10:57
 */
@RestClient(serviceId = "infoauth-service")
public interface CommonAgreementService {

    /**
     * 通用协议服务- 获取协议指定版本详情信息(本接口会判断当前协议是否可用，不能查询历史协议)
     * @see CommonResultEnum 基础错误码
     * @see CommonAgreementErrorCodeEnum  协议自有错误码
     * @param request
     * @return
     */
    SupportResult<CommonAgreementExtendResponse>
    queryAgreementExtendInfo(CommonAgreementQueryRequest request);


    /**
     * 通用协议服务- 批量获取协议最新版本详情信息(本接口会过滤没发布的协议)
     * @see CommonResultEnum 基础错误码
     * @see CommonAgreementErrorCodeEnum  协议自有错误码
     * @param request
     * @return
     */
    SupportResult<CommonAgreementBatchExtendResponse>
    queryBatchAgreementExtendInfo(CommonAgreementBatchQueryRequest request);

    /**
     * 通用协议服务- 获取协议详情信息+包括协议文件(本接口会判断当前协议是否可用，不能查询历史协议)
     * @param request
     * @return
     */
    SupportResult<CommonAgreementInfoDetailResponse>
    queryAgreementInfoDetail(CommonAgreementQueryRequest request);



    /**
     * 通用协议服务- 获取协议发布记录基本信息
     * @param request
     * @return
     */
    SupportResult<CommonAgreementReleaseRecordBaseResponse>
    queryAgreementReleaseRecordBase(CommonAgreementReleaseRecordQueryRequest request);


    /**
     * 通用协议服务- 获取协议发布记录详细信息+包括协议文件
     * @param request
     * @return
     */
    SupportResult<CommonAgreementReleaseRecordResponse>
    queryAgreementReleaseRecordDetail(CommonAgreementReleaseRecordQueryRequest request);


    /**
     * 通用协议服务- 查询协议实例详情
     * @param request
     * @return
     */
    SupportResult<CommonAgreementGenerateRecordDetailResponse>
    queryCommonAgreementGenerateRecordDetail(CommonAgreementGenerateRecordQueryRequest request);



    /**
     * 通用协议服务- 构建协议记录
     * @param request
     * @return
     */
    SupportResult<CommonAgreementGenerateRecordResponse>
    generateCommonAgreementRecord(CommonAgreementGenerateRecordRequest request);

    /**
     * 通用协议服务- 构建协议记录并返回详情
     * @param request
     * @return
     */
    SupportResult<CommonAgreementGenerateRecordDetailResponse>
    generateCommonAgreementRecordDetail(CommonAgreementGenerateRecordRequest request);


}
