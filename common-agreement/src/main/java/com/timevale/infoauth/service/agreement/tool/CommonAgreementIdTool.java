package com.timevale.infoauth.service.agreement.tool;

import com.timevale.framework.sands.Sahara;

/**
 * 协议ID 工具类
 * <AUTHOR>
 * @since 2022-03-23
 */
public class CommonAgreementIdTool {

    private static final String AGREEMENT_GENERATE_ID = "AT-GEN-RECORD-";


    public static String buildGenerateRecordId(){
        return AGREEMENT_GENERATE_ID + Sahara.instance.getHexSand();
    }










}
