package com.timevale.infoauth.service.api;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.impl.biz.checker.OrgAuthResultChecker;
import com.timevale.infoauth.service.enums.EntStatus;
import com.timevale.infoauth.service.impl.common.dto.OrgProvout;
import com.timevale.infoauth.service.impl.common.dto.OrganProvin;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
@JsonDataIgnore
public class OrgAuthResultCheckerTest extends ApplicationTest {

  @Test
  public void compareBaseInfoTest() {
    OrganProvin provin = new OrganProvin();
    provin.setOrgName("杭州天谷信息科技有限公司");
    provin.setCodeUSC("913301087458306078");
    OrgProvout provout = new OrgProvout();
    provout.setName("杭州天谷信息科技有限公司");
    provout.setCodeORG("913301087458306078");
    Assert.assertFalse(OrgAuthResultChecker.compareBaseInfo(provin, provout));

    provin.setCodeUSC(null);
    provin.setCodeREG("330108000003512");
    Assert.assertFalse(OrgAuthResultChecker.compareBaseInfo(provin, provout));

    provin.setCodeREG(null);
    provin.setCodeORG("330108000003512");
    provout.setCodeORG(null);
    provout.setCodeUSC("913301087458306078");
    Assert.assertFalse(OrgAuthResultChecker.compareBaseInfo(provin, provout));

    provin.setCodeORG(null);
    provin.setCodeUSC("913301087458306078");
    provout.setCodeUSC(null);
    provout.setCodeREG("330108000003512");
    Assert.assertFalse(OrgAuthResultChecker.compareBaseInfo(provin, provout));

    provout.setCodeREG(null);
    provout.setCodeUSC("913301087458306078");
    Assert.assertFalse(OrgAuthResultChecker.compareBaseInfo(provin, provout));

    provin.setLegalRepName("何一兵");
    Assert.assertFalse(OrgAuthResultChecker.compareBaseInfo(provin, provout));

  }

  @Test
  public void compareBaseInfoLegalNameIfNotNullTest() throws ParseException {
    OrganProvin provin = new OrganProvin();
    provin.setOrgName("杭州天谷信息科技有限公司");
    provin.setCodeUSC("913301087458306078");
    OrgProvout provout = new OrgProvout();
    provout.setName("杭州天谷信息科技有限公司");
    provout.setCodeUSC("913301087458306078");
    provin.setLegalRepName("何一兵");
    Assert.assertFalse(OrgAuthResultChecker.compareBaseInfoLegalNameIfNotNull(provin, provout));

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    Date startTime = new Date();
    Date endTime = sdf.parse("2121-01-01");
    provout.setBsStartTime(startTime);
    provout.setBsEndTime(endTime);
    provout.setEntStatus(EntStatus.OTHER);
    provout.setLegalName("何一兵");
    Assert.assertFalse(OrgAuthResultChecker.compareBaseInfoLegalNameIfNotNull(provin, provout));
  }
}
