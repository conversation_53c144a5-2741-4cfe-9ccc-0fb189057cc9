<?xml version="1.0" encoding="UTF-8"?>
<Appenders>
		<!-- 默认 Appender -->
		<RollingFile name="DEFAULT-APPENDER" fileName="./logs/default.log"
			filePattern="./logs/default-%d{yyyy-MM-dd}-%i.log"><!-- 定义了默认日志文件名和滚动时文件名规则 。“%i”是从1开始的正整数。-->
			<PatternLayout charset="${log.encoding}" pattern="%d %-5p [%t] %c{2}(%L) - %m %X{TrackId} %X{AppId} %X{LoginId}%n" />
			<Policies>
				<TimeBasedTriggeringPolicy modulate="true" interval="1" /><!-- 基于时间的触发滚动，这里是每天00:00进行日志滚动。 -->
				<SizeBasedTriggeringPolicy size="250MB" /><!-- 基于日志大小进行日志滚动 -->
			</Policies>
			<DefaultRolloverStrategy max="10"><!-- 定于日志滚动策略。 max参数指定了计数器的最大值。一旦计数器达到了最大值，过旧的文件将被删除。注意：max参数并不等于需要保留的日志文件的最大数目。 -->
				<Delete basePath="./logs" maxDepth="1">
					<IfFileName glob="default-*.log" />
					<IfLastModified age="7d" />
				</Delete><!-- 定义了根据文件名和修改时间删除文件测策略 -->
			</DefaultRolloverStrategy>
		</RollingFile>
		<!-- 以上定义了一个每天凌晨滚动，历史日志保留7天，超过250M就进行分割并且分割文件不超过10个的default*.log日志 -->

		<!-- 异常日志 Appender -->
		<RollingFile name="SYS-ERROR-APPENDER" fileName="./logs/error.log"
			filePattern="./logs/error-%d{yyyy-MM-dd}-%i.log">
			<PatternLayout charset="${log.encoding}" pattern="%d %-5p [%t] %c{2}(%L) - %m %X{TrackId} %X{AppId} %X{LoginId}%n" />
			<ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
			<Policies>
				<TimeBasedTriggeringPolicy modulate="true" interval="1" />
				<SizeBasedTriggeringPolicy size="250MB" />
			</Policies>
			<DefaultRolloverStrategy max="10">
				<Delete basePath="./logs" maxDepth="1">
					<IfFileName glob="error-*.log" />
					<IfLastModified age="7d" />
				</Delete>
			</DefaultRolloverStrategy>
		</RollingFile>
		
		<!-- 服务 Appender -->
		<RollingFile name="SYS-BIZ-SERVICE-APPENDER" fileName="./logs/service.log"
			filePattern="./logs/service-%d{yyyy-MM-dd}-%i.log">
			<PatternLayout charset="${log.encoding}" pattern="%d %-5p [%t] %c{2}(%L) - %m %X{TrackId} %X{AppId} %X{LoginId}%n" />
			<Policies>
				<TimeBasedTriggeringPolicy modulate="true" interval="1" />
				<SizeBasedTriggeringPolicy size="250MB" />
			</Policies>
			<DefaultRolloverStrategy max="10">
				<Delete basePath="./logs" maxDepth="1">
					<IfFileName glob="service-*.log" />
					<IfLastModified age="7d" />
				</Delete>
			</DefaultRolloverStrategy>
		</RollingFile>

		<!-- DAL Appender -->
		<RollingFile name="DAL-APPENDER" fileName="./logs/dal.log"
			filePattern="./logs/dal-%d{yyyy-MM-dd}-%i.log">
			<PatternLayout charset="${log.encoding}" pattern="%d %-5p [%t] %c{2}(%L) - %m %X{TrackId} %X{AppId} %X{LoginId}%n" />
			<Policies>
				<TimeBasedTriggeringPolicy modulate="true" interval="1" />
				<SizeBasedTriggeringPolicy size="250MB" />
			</Policies>
			<DefaultRolloverStrategy max="10">
				<Delete basePath="./logs" maxDepth="1">
					<IfFileName glob="dal-*.log" />
					<IfLastModified age="7d" />
				</Delete>
			</DefaultRolloverStrategy>
		</RollingFile>

		<!-- 定时任务  Appender -->
		<RollingFile name="SYS-BIZ-TASK-APPENDER" fileName="./logs/task.log"
			filePattern="./logs/task-%d{yyyy-MM-dd}-%i.log">
			<PatternLayout charset="${log.encoding}" pattern="%d %-5p [%t] %c{2}(%L) - %m %X{TrackId} %X{AppId} %X{LoginId}%n" />
			<Policies>
				<TimeBasedTriggeringPolicy modulate="true" interval="1" />
				<SizeBasedTriggeringPolicy size="250MB" />
			</Policies>
			<DefaultRolloverStrategy max="10">
				<Delete basePath="./logs" maxDepth="1">
					<IfFileName glob="task-*.log" />
					<IfLastModified age="7d" />
				</Delete>
			</DefaultRolloverStrategy>
		</RollingFile>

		<!-- 供应商入参出参 Appender -->
		<RollingFile name="SYS-BIZ-PROVIDER-APPENDER" fileName="${log.path}/provider.log"
					 filePattern="${log.path}/provider-%d{yyyy-MM-dd}.log">
			<PatternLayout charset="${log.encoding}" pattern="%d %-5p [%t] %c{2}(%L) - %m %X{TrackId} %X{AppId} %X{LoginId}%n" />
			<Policies>
				<TimeBasedTriggeringPolicy modulate="true" interval="1" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="${log.path}" maxDepth="1">
					<IfFileName glob="provider-*.log" />
					<IfLastModified age="7d" />
				</Delete>
			</DefaultRolloverStrategy>
		</RollingFile>

	<!-- 供应商监控入参出参 Appender -->
	<RollingFile name="SYS-BIZ-MONITOR-APPENDER" fileName="${log.path}/monitor.log"
				 filePattern="${log.path}/monitor-%d{yyyy-MM-dd}.log">
		<PatternLayout charset="${log.encoding}" pattern="%d %-5p [%t] %c{2}(%L) - %m %X{TrackId} %X{AppId} %X{LoginId}%n" />
		<Policies>
			<TimeBasedTriggeringPolicy modulate="true" interval="1" />
		</Policies>
		<DefaultRolloverStrategy>
			<Delete basePath="${log.path}" maxDepth="1">
				<IfFileName glob="monitor-*.log" />
				<IfLastModified age="7d" />
			</Delete>
		</DefaultRolloverStrategy>
	</RollingFile>

	<!-- 风控监控入参出参 Appender -->
	<RollingFile name="SYS-BIZ-RISK-APPENDER" fileName="${log.path}/risk.log"
				 filePattern="${log.path}/monitor-%d{yyyy-MM-dd}.log">
		<PatternLayout charset="${log.encoding}" pattern="%d %-5p [%t] %c{2}(%L) - %m %X{TrackId} %X{AppId} %X{LoginId}%n" />
		<Policies>
			<TimeBasedTriggeringPolicy modulate="true" interval="1" />
		</Policies>
		<DefaultRolloverStrategy>
			<Delete basePath="${log.path}" maxDepth="1">
				<IfFileName glob="risk-*.log" />
				<IfLastModified age="7d" />
			</Delete>
		</DefaultRolloverStrategy>
	</RollingFile>


	<!-- 风控SDK 内部日志 Appender -->
	<RollingFile name="SYS-RISK-SERVICE-APPENDER" fileName="${log.path}/risksdk.log"
				 filePattern="${log.path}/risk-%d{yyyy-MM-dd}-%i.log">
		<PatternLayout charset="${log.encoding}" pattern="%d %-5p %c{2} - %m %X{AppId} %X{LoginId} %X{TrackId}%n"/>
		<Policies>
			<TimeBasedTriggeringPolicy modulate="true" interval="1"/>
			<SizeBasedTriggeringPolicy size="250MB"/>
		</Policies>
		<DefaultRolloverStrategy max="10">
			<Delete basePath="${log.path}" maxDepth="1">
				<IfFileName glob="risksdk-*.log"/>
				<IfLastModified age="1d"/>
			</Delete>
		</DefaultRolloverStrategy>
	</RollingFile>

		<!-- 控制台输出 -->
		<console name="Console" target="SYSTEM_OUT">
			<!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
			<ThresholdFilter level="ALL" onMatch="ACCEPT" onMismatch="DENY"/>
			<!--输出日志的格式-->
			<PatternLayout
					pattern="%highlight{%d [%t] %-5level: %msg%n%throwable}"/>
		</console>
</Appenders>

