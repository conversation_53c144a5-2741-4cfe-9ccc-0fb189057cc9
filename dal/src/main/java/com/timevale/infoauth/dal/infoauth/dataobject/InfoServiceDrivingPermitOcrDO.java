package com.timevale.infoauth.dal.infoauth.dataobject;

import com.timevale.infoauth.dal.infoauth.utils.StringUtil;
import lombok.Getter;

import java.util.Date;

@Getter
public class InfoServiceDrivingPermitOcrDO {
  private long id;
  private String infoauthId;
  private String requestId;
  // 车辆品牌型号
  private String mainModel;
  // 车辆类型
  private String carType;
  // 注册日期
  private String mainRegisterDate;
  // 住址
  private String address;
  // 所有者
  private String owner;
  // 使用性质
  private String mainUserCharacter;
  // 发证时间
  private String issueDate;
  // 发动机号码
  private String mainEngineNo;
  // 车辆识别代码
  private String mainVin;
  // 车牌号码
  private String mainPlateNum;

  private String providerId;

  private String providerReqId;

  private Date createTime;

  private Date modifyTime;

  private Integer result;

  private Integer serviceStatus;

  public void setId(long id) {
    this.id = id;
  }

  public void setInfoauthId(String infoauthId) {
    this.infoauthId = infoauthId;
  }

  public void setRequestId(String requestId) {
    this.requestId = requestId;
  }

  public void setMainModel(String mainModel) {
    this.mainModel = StringUtil.truncation(mainModel, 64);
  }

  public void setCarType(String carType) {
    this.carType = StringUtil.truncation(carType, 32);
  }

  public void setMainRegisterDate(String mainRegisterDate) {
    this.mainRegisterDate = StringUtil.truncation(mainRegisterDate, 32);
  }

  public void setAddress(String address) {
    this.address = StringUtil.truncation(address, 64);
  }

  public void setOwner(String owner) {
    this.owner = StringUtil.truncation(owner, 32);
  }

  public void setMainUserCharacter(String mainUserCharacter) {
    this.mainUserCharacter = StringUtil.truncation(mainUserCharacter, 32);
  }

  public void setIssueDate(String issueDate) {
    this.issueDate = StringUtil.truncation(issueDate, 32);
  }

  public void setMainEngineNo(String mainEngineNo) {
    this.mainEngineNo = StringUtil.truncation(mainEngineNo, 64);
  }

  public void setMainVin(String mainVin) {
    this.mainVin = StringUtil.truncation(mainVin, 64);
  }

  public void setMainPlateNum(String mainPlateNum) {
    this.mainPlateNum = StringUtil.truncation(mainPlateNum, 64);
  }

  public void setProviderId(String providerId) {
    this.providerId = providerId;
  }

  public void setProviderReqId(String providerReqId) {
    this.providerReqId = StringUtil.truncation(providerReqId, 32);
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public void setModifyTime(Date modifyTime) {
    this.modifyTime = modifyTime;
  }

  public void setResult(Integer result) {
    this.result = result;
  }

  public void setServiceStatus(Integer serviceStatus) {
    this.serviceStatus = serviceStatus;
  }
}
