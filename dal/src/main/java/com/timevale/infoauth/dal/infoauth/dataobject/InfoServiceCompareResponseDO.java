package com.timevale.infoauth.dal.infoauth.dataobject;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InfoServiceCompareResponseDO  extends InfoServiceAuthDO {

    private List<InfoServiceCompareDO> infoServiceCompareDOList;

    private int total;
}
