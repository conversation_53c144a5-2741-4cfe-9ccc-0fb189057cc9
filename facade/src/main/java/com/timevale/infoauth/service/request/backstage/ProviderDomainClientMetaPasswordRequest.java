package com.timevale.infoauth.service.request.backstage;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE 2024/4/18 14:26
 */
@Data
public class ProviderDomainClientMetaPasswordRequest extends ToString {

    /**
     * info_provider_domain 表主键
     */
    private Long providerDomainId;

    /**
     * 账号
     */
    private String conditionalMetaAccount;

    /**
     * 密码
     */
    private String metaPassword;

}
