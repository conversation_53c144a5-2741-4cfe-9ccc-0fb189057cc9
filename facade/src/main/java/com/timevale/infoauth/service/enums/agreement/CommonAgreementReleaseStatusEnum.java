package com.timevale.infoauth.service.enums.agreement;

import java.util.Objects;

/**
 * 通用协议内容发布状态
 * 发布状态。0=待发布 1=发布成功 2=作废
 * <AUTHOR>
 * @since 2024/12/4 21:04
 */
public enum CommonAgreementReleaseStatusEnum {

    RELEASE_NOT_ENABLE(0, "待发布"),
    RELEASE_ENABLE(1, "发布成功"),
    RELEASE_DISABLE(2, "作废");

    private final int code;
    private final String message;

    CommonAgreementReleaseStatusEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode(){
        return code;
    }

    public String getMessage(){
        return message;
    }

    public static boolean notEnable(Integer status) {
        return !enable(status);
    }

    public static boolean enable(Integer status) {
        return Objects.equals(status, RELEASE_ENABLE.getCode());
    }


    public static CommonAgreementReleaseStatusEnum getByStatus(Integer status){
        for (CommonAgreementReleaseStatusEnum value : CommonAgreementReleaseStatusEnum.values()) {
            if(Objects.equals(status,value.code)){
                return value;
            }
        }
        return RELEASE_NOT_ENABLE;
    }
}
