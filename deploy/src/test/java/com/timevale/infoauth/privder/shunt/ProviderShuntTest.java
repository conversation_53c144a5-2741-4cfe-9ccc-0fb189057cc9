package com.timevale.infoauth.privder.shunt;

import com.timevale.infoauth.service.enums.InfoAuthServiceType;
import com.timevale.infoauth.service.impl.provider.manager.select.providerFilter.ProviderFilterManager;
import com.timevale.infoauth.service.impl.provider.manager.select.providerFilter.ProviderInf;
import com.timevale.infoauth.service.impl.utils.UUIDUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/10/12 12:58
 */
public class ProviderShuntTest {

    private static final Map<String, SelectableHeader> selectableLinkedMap  = new HashMap<>();

      public static void main(String[] args) {
        //

          String serviceType = InfoAuthServiceType.BANKCARD_OCR.name();
          List<ProviderInf> providers = new ArrayList<>();
          ProviderInf providerInf1 = new ProviderInf(serviceType,"腾讯", 900000);
          providerInf1.setShuntTag(true);

          ProviderInf providerInf2 = new ProviderInf(serviceType,"百度", 10);
          providerInf2.setShuntTag(true);

          ProviderInf providerInf3 = new ProviderInf(serviceType,"华为", 5);
          providerInf3.setShuntTag(true);


          providers.add(providerInf1);
          providers.add(providerInf2);
          providers.add(providerInf3);


          ProviderShuntTest shuntTest = new ProviderShuntTest();
          SelectableHeader header = shuntTest.calcRange(providers);
          selectableLinkedMap.put(serviceType, header);

          Map<String, Integer> countM  = new HashMap<>();
          for(int i =0;i< 100000;i++){
              String key = UUIDUtil.generateUUID();
              int v = key.hashCode();
              ProviderInf targetP =  shuntTest.select(serviceType, v);

              Integer count = countM.get(targetP.getName());
              if(count == null){
                  count =0;
              }
              count ++;
              countM.put(targetP.getName(), count);
          }

          for(Map.Entry<String,Integer> entry :  countM.entrySet()){
             System.out.println(entry.getKey() +":"+ entry.getValue());
          }

      }

    public ProviderInf select(String serviceType, int value) {
        SelectableHeader header;
        header = selectableLinkedMap.get(serviceType);

        int width;
        if (0 == (width = header.width)) {
            return null;
        }
        int v = (0 > value ? ((~value) + 1) : value);
        int w = (v % width);
        return header.select(w);
    }

    private SelectableHeader calcRange(List<ProviderInf> providers) {
        List<ProviderInf> zeros = new ArrayList<>();
        SelectableHeader header = new SelectableHeader();
        SelectableNode next = header.next, tail;
        int minw, maxw = 0, wp;
        for (ProviderInf p : providers) {
            if (0 == (wp = p.getWeight() * 100)) {
                zeros.add(p);
            } else {
                minw = maxw;
                maxw = (maxw + (0 > wp ? ((~wp) + 1) : wp));
                tail = (new BorderlineNode(minw - 1, maxw, p));
                next = (null == next ? (header.next = tail) : (next.next = tail));
            }
        }
        if (!zeros.isEmpty()) {
            tail = (new MultiProviderNode(zeros));
            (null == next ? header : next).next = tail;
            maxw = (maxw + ((MultiProviderNode) tail).count);
        }
        header.width = maxw;
        return header;
    }

    private abstract static class SelectableNode {

        protected  SelectableNode next;

        ProviderInf select(final int weight) {
            return (null == next ? null : next.select(weight));
        }
    }

    private static class SelectableHeader extends  SelectableNode {

        // total weight for all nodes
        private int width;

        @Override
        ProviderInf select(final int weight) {
            return super.select(weight);
        }
    }

    private static class BorderlineNode extends  SelectableNode {

        private final int min;
        private final int max;
        private final ProviderInf provider;

        public BorderlineNode(int min, int max, ProviderInf provider) {
            this.min = min;
            this.max = max;
            this.provider = provider;
        }

        @Override
        ProviderInf select(final int weight) {
            return ((weight > min && weight <= max) ? provider : super.select(weight));
        }
    }

    private static class MultiProviderNode extends  SelectableNode {

        private final int count;
        private final ProviderInf[] providers;

        public MultiProviderNode(List<ProviderInf> providers) {
            this.count = providers.size();
            this.providers = providers.toArray(new ProviderInf[0]);
        }

        @Override
        ProviderInf select(final int weight) {
            int idx = (weight % count);
            return providers[idx];
        }
    }
}
