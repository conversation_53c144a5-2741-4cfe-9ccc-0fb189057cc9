<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="tgtest" parallel="classes" thread-count="1">
    <!--<listeners>
        <listener class-name="com.timevale.realnameauth.tgconfig.ExtentTestNGListener"/>
    </listeners>-->
    <test verbose="2" name="Test Demo">
        <classes>
            <class name="com.timevale.infoauth.service.api.agreement.CommonAgreementManagerServiceTest"/>
            <class name="com.timevale.infoauth.service.api.QinxinbaoTest"/>
            <class name="com.timevale.infoauth.service.SendOptRecordUtilTest"/>
            <class name="com.timevale.infoauth.service.enumn.OcrProviderEnumTest"/>
            <class name="com.timevale.infoauth.service.api.InfoauthServiceTest"/>
            <class name="com.timevale.infoauth.service.api.InfoServiceCompareServiceTest"/>
            <class name="com.timevale.infoauth.service.api.ProvideMonitorServiceTest"/>
            <class name="com.timevale.infoauth.service.api.InfoauthBackServiceTest"/>
            <class name="com.timevale.infoauth.service.api.QuickSignQueryServiceTest"/>
            <class name="com.timevale.infoauth.service.api.ProviderTest"/>
            <class name="com.timevale.infoauth.service.api.BankThreeUnionPayTest"/>
            <class name="com.timevale.infoauth.service.api.BankAuthUnionPayTest"/>
            <class name="com.timevale.infoauth.service.api.IdnoAuthServiceImplTest"/>
            <class name="com.timevale.infoauth.service.api.JzUtilsTest"/>
            <class name="com.timevale.infoauth.service.QueryServiceTest"/>
            <class name="com.timevale.infoauth.service.OrgInfoAuthServiceImplTest"/>
            <class name="com.timevale.infoauth.service.api.OcrParseTest"/>
            <class name="com.timevale.infoauth.service.api.BankfourJzClientTest"/>
            <class name="com.timevale.infoauth.service.api.ProviderClientTest"/>
            <class name="com.timevale.infoauth.service.api.CircuitBreakerTest"/>
            <class name="com.timevale.infoauth.service.api.OrgAuthResultCheckerTest"/>
            <class name="com.timevale.infoauth.service.api.OrgInputCheckerTest"/>
            <class name="com.timevale.infoauth.service.api.QxbClientTest"/>
            <class name="com.timevale.infoauth.service.api.ProviderManagerServiceTest"/>
            <class name="com.timevale.infoauth.service.api.ProviderClientServiceTest"/>
            <class name="com.timevale.infoauth.service.api.ProviderSelectServiceTest"/>
            <class name="com.timevale.infoauth.service.api.ProviderSelectRuleServiceTest"/>
            <class name="com.timevale.infoauth.service.api.ProviderRetryServiceTest"/>
            <class name="com.timevale.infoauth.service.api.MonitorExtensionLoaderTest"/>
            <class name="com.timevale.infoauth.service.AutoConfigTest"/>
            <class name="com.timevale.infoauth.privder.client.zhima.ZhimaClientTest"/>
            <class name="com.timevale.infoauth.adaptors.OrgFuzzyQueryAdaptorsTest"/>
            <class name="com.timevale.infoauth.adaptors.Md5Test"/>
            <class name="com.timevale.infoauth.service.api.InnerInfoAuthServiceTest"/>
            <class name="com.timevale.infoauth.service.api.ProvSceneTest"/>
        </classes>
    </test>
</suite>