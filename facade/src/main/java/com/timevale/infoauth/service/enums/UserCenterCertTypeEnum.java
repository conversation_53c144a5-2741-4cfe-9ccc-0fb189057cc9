package com.timevale.infoauth.service.enums;

/** @Author: <PERSON> @Description: @Date: Created in 2018/3/19 @Modified By: */
public enum UserCenterCertTypeEnum {
  /**大陆身份证*/
  CRED_PSN_CH_IDCARD("大陆身份证", 1, "大陆身份证", "01"),
  /**台湾来往大陆通行证*/
  CRED_PSN_CH_TWCARD("台湾来往大陆通行证", 4, "台胞证", "04"),
  /**澳门来往大陆通行证*/
  CRED_PSN_CH_MACAO("澳门来往大陆通行证", 11, "回乡证", "11"),
  /**香港来往大陆通行证*/
  CRED_PSN_CH_HONGKONG("香港来往大陆通行证", 12, "回乡证", "11"),
  /**港澳居民居住证*/
  CRED_PSN_CH_RP_HKMACAO("港澳居民居住证", 13, "港澳居民居住证", "12"),
  /**台湾居民居住证*/
  CRED_PSN_CH_RP_TW("台湾居民居住证", 14, "台湾居民居住证", "13"),
  /**护照*/
  CRED_PSN_PASSPORT("护照", 2, "护照", "02"),
  /**外国人永久居留身份证*/
  CRED_PSN_CH_GREEN_CARD("外国人永久居留身份证", 10, "外国人永久居留身份证", "10"),

  CRED_PSN_CH_SOLDIER_IDCARD("军官证", 5, "军官证", "05"),

  CRED_PSN_POLICE_ID_CARD("警官证", 6, "警官证", "06"),

  CRED_PSN_PRIVATES_ID_CARD("士兵证", 7, "士兵证", "07"),
  /**未知*/
  UNKNOWN("未知", 0, "未知", "0");

  private String name;

  private int dbValue;

  private String idType;

  private String idTypeCode;

  UserCenterCertTypeEnum(String name, int dbValue, String idType, String idTypeCode) {
    this.name = name;
    this.dbValue = dbValue;
    this.idType = idType;
    this.idTypeCode = idTypeCode;
  }

  public String getName() {
    return name;
  }

  public int dbValue() {
    return dbValue;
  }

  public String getIdType() {
    return idType;
  }

  public String getIdTypeCode() {
    return idTypeCode;
  }

  public static UserCenterCertTypeEnum getByDbValue(int dbValue) {

    for (UserCenterCertTypeEnum certType : UserCenterCertTypeEnum.values()) {
      if (certType.dbValue() == dbValue) {
        return certType;
      }
    }
    return UNKNOWN;
  }
}
