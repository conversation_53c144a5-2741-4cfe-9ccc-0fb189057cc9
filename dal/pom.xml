<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.timevale.infoauth</groupId>
		<artifactId>infoauth-parent</artifactId>
		<version>1.0.0</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<artifactId>infoauth-dal</artifactId>
	<name>infoauth/dal</name>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>com.timevale</groupId>
			<artifactId>mandarin-common</artifactId>
		</dependency>


<!--		<dependency>-->
<!--			<groupId>com.baomidou</groupId>-->
<!--			<artifactId>mybatis-plus-boot-starter</artifactId>-->
<!--			<version>3.1.1</version>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>com.mybatis-flex</groupId>-->
<!--			<artifactId>mybatis-flex-spring</artifactId>-->
<!--			<version>1.7.2</version>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>com.mybatis-flex</groupId>-->
<!--			<artifactId>mybatis-flex-processor</artifactId>-->
<!--			<version>1.7.2</version>-->
<!--			<scope>provided</scope>-->
<!--		</dependency>-->

		<dependency>
			<groupId>com.timevale</groupId>
			<artifactId>mandarin-microservice</artifactId>
		</dependency>
		<dependency>
			<groupId>com.timevale.infoauth</groupId>
			<artifactId>infoauth-facade</artifactId>
			<version>${infoauth-facade.vsersion}</version>
			<scope>compile</scope>
		</dependency>


	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>cn.dalgen.plugins</groupId>
				<artifactId>mybatis-maven-plugin</artifactId>
				<version>1.0.0-SNAPSHOT</version>
				<configuration>
					<copyTemplate>false</copyTemplate>
					<outputDirectory>.</outputDirectory>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
