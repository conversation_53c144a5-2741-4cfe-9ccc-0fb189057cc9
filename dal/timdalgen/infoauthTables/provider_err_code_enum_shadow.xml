<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="provider_err_code_enum_shadow" physicalName="provider_err_code_enum_shadow" remark="供应商错误码枚举影子表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,P_MSG,P_CODE,BIZ_MD5,EXTEND,P_CODE_ID,PROVIDER,BIZ_CODE_ID,BIZ_TYPE,CREATE_TIME,MODIFY_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.P_MSG,sf.P_CODE,sf.BIZ_MD5,sf.EXTEND,sf.P_CODE_ID,sf.PROVIDER,sf.BIZ_CODE_ID,sf.BIZ_TYPE,sf.CREATE_TIME,sf.MODIFY_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:provider_err_code_enum_shadow">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO provider_err_code_enum_shadow(
            ID
            ,P_MSG
            ,P_CODE
            ,BIZ_MD5
            ,EXTEND
            ,P_CODE_ID
            ,PROVIDER
            ,BIZ_CODE_ID
            ,BIZ_TYPE
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES(
             null
            , #{pMsg,jdbcType=VARCHAR}
            , #{pCode,jdbcType=VARCHAR}
            , #{bizMd5,jdbcType=VARCHAR}
            , #{extend,jdbcType=VARCHAR}
            , #{pCodeId,jdbcType=VARCHAR}
            , #{provider,jdbcType=VARCHAR}
            , #{bizCodeId,jdbcType=VARCHAR}
            , #{bizType,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{modifyTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:provider_err_code_enum_shadow">
        INSERT INTO provider_err_code_enum_shadow(
            ID
            ,P_MSG
            ,P_CODE
            ,BIZ_MD5
            ,EXTEND
            ,P_CODE_ID
            ,PROVIDER
            ,BIZ_CODE_ID
            ,BIZ_TYPE
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.pMsg,jdbcType=VARCHAR}
                , #{item.pCode,jdbcType=VARCHAR}
                , #{item.bizMd5,jdbcType=VARCHAR}
                , #{item.extend,jdbcType=VARCHAR}
                , #{item.pCodeId,jdbcType=VARCHAR}
                , #{item.provider,jdbcType=VARCHAR}
                , #{item.bizCodeId,jdbcType=VARCHAR}
                , #{item.bizType,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.modifyTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:provider_err_code_enum_shadow">
        <![CDATA[
        UPDATE provider_err_code_enum_shadow
        SET
            P_MSG           = #{pMsg,jdbcType=VARCHAR}
            ,P_CODE          = #{pCode,jdbcType=VARCHAR}
            ,BIZ_MD5         = #{bizMd5,jdbcType=VARCHAR}
            ,EXTEND          = #{extend,jdbcType=VARCHAR}
            ,P_CODE_ID       = #{pCodeId,jdbcType=VARCHAR}
            ,PROVIDER        = #{provider,jdbcType=VARCHAR}
            ,BIZ_CODE_ID     = #{bizCodeId,jdbcType=VARCHAR}
            ,BIZ_TYPE        = #{bizType,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,MODIFY_TIME     = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:provider_err_code_enum_shadow">
        <![CDATA[
        DELETE FROM provider_err_code_enum_shadow
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:provider_err_code_enum_shadow">
        SELECT *
        FROM provider_err_code_enum_shadow
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <!-- 根据唯一约束操作数据 -->
    <operation name="updateByUniqBizMd5" paramtype="object" remark="根据唯一约束UniqBizMd5更新表:provider_err_code_enum_shadow">
        <![CDATA[
        UPDATE provider_err_code_enum_shadow
        SET
            P_MSG           = #{pMsg,jdbcType=VARCHAR}
            ,P_CODE          = #{pCode,jdbcType=VARCHAR}
            ,EXTEND          = #{extend,jdbcType=VARCHAR}
            ,P_CODE_ID       = #{pCodeId,jdbcType=VARCHAR}
            ,PROVIDER        = #{provider,jdbcType=VARCHAR}
            ,BIZ_CODE_ID     = #{bizCodeId,jdbcType=VARCHAR}
            ,BIZ_TYPE        = #{bizType,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,MODIFY_TIME     = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE
            BIZ_MD5         = #{bizMd5,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="deleteByUniqBizMd5" remark="根据唯一约束UniqBizMd5删除数据:provider_err_code_enum_shadow">
        <![CDATA[
        DELETE FROM provider_err_code_enum_shadow
        WHERE
            BIZ_MD5         = #{bizMd5,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="getByUniqBizMd5" multiplicity="one" remark="根据唯一约束UniqBizMd5获取数据:provider_err_code_enum_shadow">
        SELECT *
        FROM provider_err_code_enum_shadow
        WHERE
        <![CDATA[
            BIZ_MD5         = #{bizMd5,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxBizCodeId" multiplicity="many" remark="根据普通索引IdxBizCodeId获取数据:provider_err_code_enum_shadow">
        SELECT *
        FROM provider_err_code_enum_shadow
        WHERE
        <![CDATA[
            BIZ_CODE_ID     = #{bizCodeId,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxBizType" multiplicity="many" remark="根据普通索引IdxBizType获取数据:provider_err_code_enum_shadow">
        SELECT *
        FROM provider_err_code_enum_shadow
        WHERE
        <![CDATA[
            BIZ_TYPE        = #{bizType,jdbcType=TINYINT}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxPCode" multiplicity="many" remark="根据普通索引IdxPCode获取数据:provider_err_code_enum_shadow">
        SELECT *
        FROM provider_err_code_enum_shadow
        WHERE
        <![CDATA[
            P_CODE          = #{pCode,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxPCodeId" multiplicity="many" remark="根据普通索引IdxPCodeId获取数据:provider_err_code_enum_shadow">
        SELECT *
        FROM provider_err_code_enum_shadow
        WHERE
        <![CDATA[
            P_CODE_ID       = #{pCodeId,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxPMsg" multiplicity="many" remark="根据普通索引IdxPMsg获取数据:provider_err_code_enum_shadow">
        SELECT *
        FROM provider_err_code_enum_shadow
        WHERE
        <![CDATA[
            P_MSG           = #{pMsg,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxProvider" multiplicity="many" remark="根据普通索引IdxProvider获取数据:provider_err_code_enum_shadow">
        SELECT *
        FROM provider_err_code_enum_shadow
        WHERE
        <![CDATA[
            PROVIDER        = #{provider,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>
