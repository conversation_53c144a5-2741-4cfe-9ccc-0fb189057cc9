package com.timevale.infoauth.service.request.agreement;

import com.timevale.infoauth.service.enums.agreement.CommonAgreementLangEnum;
import com.timevale.infoauth.service.model.BaseModel;
import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;


/**
 * 通用协议信息查询
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CommonAgreementQueryRequest extends ToString {


  /**
   * 协议编号
   */
  @NotBlank
  private String commonAgreementId;

  /** 通用协议当前版本,默认显示最新版本 */
  private String releaseVersion;

  /**
   * 通用协议语言,默认显示中文
   * @see  CommonAgreementLangEnum
   * */
  private String lang;

}
