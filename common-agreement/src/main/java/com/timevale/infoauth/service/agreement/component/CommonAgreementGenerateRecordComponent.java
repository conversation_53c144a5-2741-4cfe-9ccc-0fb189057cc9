package com.timevale.infoauth.service.agreement.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.timevale.filesystem.common.service.api.FileSystemService;
import com.timevale.infoauth.dal.infoauth.dao.agreement.CommonAgreementGenerateRecordDAO;
import com.timevale.infoauth.dal.infoauth.dao.agreement.CommonAgreementReleaseRecordDAO;
import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementGenerateRecordDO;
import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementReleaseRecordDO;
import com.timevale.infoauth.service.agreement.config.CommonAgreementConfigurableProperties;
import com.timevale.infoauth.service.agreement.config.CommonAgreementConstant;
import com.timevale.infoauth.service.agreement.tool.CommonAgreementIdTool;
import com.timevale.infoauth.service.agreement.tool.ZeyuanUrlBean;
import com.timevale.infoauth.service.enums.agreement.CommonAgreementContentTypeEnum;
import com.timevale.infoauth.service.exception.SuperException;
import com.timevale.infoauth.service.request.agreement.CommonAgreementGenerateRecordRequest;
import com.timevale.infoauth.service.response.agreement.CommonAgreementGenerateRecordDetailResponse;
import com.timevale.mandarin.base.util.HttpUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/4 20:27
 */
@Slf4j
@Component
public class CommonAgreementGenerateRecordComponent {

    @Autowired
    private CommonAgreementGenerateRecordDAO generateRecordDAO;

    @Autowired
    private CommonAgreementReleaseRecordDAO releaseRecordDAO;

    @Autowired
    private FileSystemService fileSystemService;

    private String generateRecordCachePre = "common_agreement_generate_content_";

    public String saveGenerateRecord(CommonAgreementGenerateRecordRequest recordRequest, CommonAgreementReleaseRecordDO releaseRecordDO){

        String generateRecordId = CommonAgreementIdTool.buildGenerateRecordId();
        CommonAgreementGenerateRecordDO generateRecordDO  = new CommonAgreementGenerateRecordDO();
        generateRecordDO.setGenerateRecordId(generateRecordId);
        generateRecordDO.setReleaseRecordId(releaseRecordDO.getReleaseRecordId());

        Map<String,String> contentFillParam = recordRequest.getContentFillParam();
        if(MapUtils.isNotEmpty(contentFillParam)){
            generateRecordDO.setContentFillParam(JsonUtils.mapToJson(contentFillParam));
        }
        generateRecordDAO.save(generateRecordDO);
        return generateRecordId;
    }



    public CommonAgreementGenerateRecordDetailResponse getByGenerateRecordId(String generateRecordId){
        CommonAgreementGenerateRecordDO generateRecordDO = generateRecordDAO.getOne(generateRecordId);
        if(generateRecordDO == null){
             return null;
        }

        String releaseRecordId =  generateRecordDO.getReleaseRecordId();
        CommonAgreementReleaseRecordDO releaseRecordDO = releaseRecordDAO.getOne(releaseRecordId);
        if(releaseRecordDO == null){
            return null;
        }


        CommonAgreementGenerateRecordDetailResponse recordDetailResponse =
                new CommonAgreementGenerateRecordDetailResponse();

        recordDetailResponse.setCommonAgreementId(releaseRecordDO.getAgreementId());
        recordDetailResponse.setReleaseRecordId(releaseRecordId);
        recordDetailResponse.setGenerateTime(generateRecordDO.getCreateTime().getTime());
        recordDetailResponse.setContent(releaseRecordDO.getContent());
        recordDetailResponse.setContentType(releaseRecordDO.getContentType());
        if (Objects.equals(CommonAgreementConstant.YES, releaseRecordDO.getContentFill())
                && Objects.equals(CommonAgreementContentTypeEnum.ZEYUAN_URL.getCode(),
                releaseRecordDO.getContentType())) {

            String contentFillKey = releaseRecordDO.getContentFillKey();
            try{

                String context = downloadZeyuanUrlString(releaseRecordDO.getContent());
                String contentFillParam = generateRecordDO.getContentFillParam();

                String targetContext = context;
                if(StringUtils.isNotBlank(contentFillParam)){
                    Map<String,String> fillParamMap =
                            JSON.parseObject(contentFillParam, new TypeReference<Map<String,String>>() {});
                    for(Map.Entry<String,String> entry : fillParamMap.entrySet()){
                        String fillKey = entry.getKey();
                        if(StringUtils.isNotBlank(contentFillKey) && !contentFillKey.contains(fillKey)){
                             continue;
                        }
                        String contentKey =
                                CommonAgreementConfigurableProperties.getInstance().fillParamKeyConvertToContentKey(fillKey);
                        targetContext = targetContext.replace(contentKey,entry.getValue());
                    }
                }
                recordDetailResponse.setContent(targetContext);
                recordDetailResponse.setContentType(CommonAgreementContentTypeEnum.HTML_ZEYUAN_CONTENT.getCode());
            }catch (Exception e){
                log.error("下载文件出错", e);
            }

        }
        return recordDetailResponse;
    }


    private String downloadZeyuanUrlString(String downUrl) {
        try {
            String  zeyuanUrlJson = HttpUtils.executeGetMethod(downUrl, "utf-8");
            ZeyuanUrlBean urlBean = JsonUtils.json2pojo(zeyuanUrlJson, ZeyuanUrlBean.class);
            return urlBean.getContent();
        } catch (Exception e) {
            log.error("下载文件出错", e);
            throw new SuperException("下载文件出错");
        }
    }

}
