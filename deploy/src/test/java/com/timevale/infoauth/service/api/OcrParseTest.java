package com.timevale.infoauth.service.api;

import com.alibaba.fastjson.JSON;
import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.HeaderUtils;
import com.timevale.infoauth.service.request.DrivingPermitRequest;
import com.timevale.infoauth.service.response.DrivingLicenceResponse;
import com.timevale.infoauth.service.response.DrivingPermitOcrResponse;
import com.timevale.infoauth.utils.ResourceUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import esign.utils.IOUtil;
import lombok.SneakyThrows;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @since 2020-07-03 11:07
 */
@JsonDataIgnore
public class OcrParseTest extends ApplicationTest {

  @Resource InfoauthService infoauthService;

  @Test
  public void drivingPermitTest() {
    HeaderUtils.setHeader();
    String imageBase64 = OcrParseTest.getImageBase64("drivingPermit.txt");
    DrivingPermitRequest request = new DrivingPermitRequest();
    request.setBizAppId("1111111");
    request.setRequestId("111111");
    request.setImage(imageBase64);
    DrivingPermitOcrResponse response = infoauthService.drivingPermitOcr(request).getData();
    Assert.assertNotNull(response);
    Assert.assertEquals(response.getMainModel(), "东风牌EQ4250GD5N1");
  }

  @Test
  public void drivingLicenceTest() {
    HeaderUtils.setHeader();
    String imageBase64 = OcrParseTest.getImageBase64("drivinglicence.txt");
    DrivingPermitRequest request = new DrivingPermitRequest();
    request.setBizAppId("22222");
    request.setRequestId("22222");
    request.setImage(imageBase64);
    DrivingLicenceResponse response = infoauthService.drivingLicenceOcr(request).getData();
    Assert.assertNotNull(response);
    Assert.assertEquals(response.getSex(), "男");
  }

//  @Test
  //TODO 临时注销
  public void drivingLicenceWithFileKeyTest() {

//    "imageKey": "$4e039c8e-35d1-4d18-8eb7-51ea6b5aaad8$291332731",
//            "backImageKey": "$fe3feb53-e9ed-4b5a-b8ab-04966cafbbc5$1018443551"
    HeaderUtils.setHeader();
    DrivingPermitRequest request = new DrivingPermitRequest();
    request.setBizAppId("3438757422");
    request.setRequestId("222222222222");
    request.setImageKey("$eac7a273-6060-41e5-9c74-9171b29a89d6$2494428014");
    DrivingLicenceResponse response = infoauthService.drivingLicenceOcr(request).getData();
    Assert.assertNotNull(response);
    Assert.assertEquals(response.getSex(), "男");
  }

  @SneakyThrows
  private static String getImageBase64(String path) {

    InputStream input;
    input = OcrParseTest.class.getClass().getResourceAsStream(path);
    if (input == null) {
      String classPathPath = "classpath:" + path;
      try {

        input = ResourceUtils.getURL(classPathPath).openStream();
      } catch (IOException e) {
        e.printStackTrace();
      }
    }
    if (input == null) {
      throw new RuntimeException("can not load file:" + path);
    }

    byte[] data = IOUtil.readStreamAsByteArray(input);

    String text = new String(data);
    return JSON.parseObject(text).getString("image");
  }
}
