package com.timevale.infoauth.service.api;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.dto.PClientDTO;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
@JsonDataIgnore
public class ProviderClientServiceTest extends ApplicationTest {

  @Resource private ProviderClientService providerClientService;

  @Test
  public void saveTest() {
    List<PClientDTO> list = providerClientService.queryClientAll();
    Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    PClientDTO pClientDTO = new PClientDTO();
    pClientDTO.setId(list.get(0).getId());
    pClientDTO.setDesc("华为");
    pClientDTO.setUrl("cn-north-4.myhuaweicloud.com");
    pClientDTO.setAccount("yinjiaqi");
    pClientDTO.setConnectTimeout(2000);
    pClientDTO.setReadTimeout(5000);
    pClientDTO.setTokenTtl(84000);
    pClientDTO.setMockUrl("mock://url");
    providerClientService.save(pClientDTO);
    Assert.assertTrue(true);
  }

  @Test
  public void getClientByIdTest() {
    List<PClientDTO> list = providerClientService.queryClientAll();
    Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    Assert.assertNotNull(providerClientService.getClientById(list.get(0).getId()));
  }

  @Test
  public void queryClientAllTest() {
    List<PClientDTO> list = providerClientService.queryClientAll();
    Assert.assertTrue(true);
    Assert.assertTrue(CollectionUtils.isNotEmpty(list));
  }
}
