package com.timevale.infoauth.dal.infoauth.dataobject.agreement;

import lombok.Data;

import java.util.Date;

/**
 * BaseDO类是所有数据对象的基类，提供了创建时间和修改时间的通用字段
 * 这些字段在大多数数据对象中都是通用的，因此通过继承这个基类可以避免在每个数据对象中重复定义这些字段
 *
 * <AUTHOR>
 * @since 2021-09-03 17:09
 */
@Data
public class BaseDO {

    /**
     * createTime字段用于存储数据对象的创建时间
     * 这个字段在数据对象被创建时自动设置，用于记录对象创建的时间点
     */
    public Date createTime;

    /**
     * modifyTime字段用于存储数据对象的最后修改时间
     * 这个字段在数据对象被修改时自动更新，用于记录对象最后一次修改的时间点
     */
    public Date modifyTime;
}
