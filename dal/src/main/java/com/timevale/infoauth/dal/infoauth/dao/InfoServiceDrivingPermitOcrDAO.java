package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceDrivingPermitOcrDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Update;

public interface InfoServiceDrivingPermitOcrDAO {
    @Insert(
            "INSERT INTO `info_service_driving_permit_ocr` " +
                    "(`infoauth_id`, `request_id` ,`create_time`, `modify_time`) " +
                    "VALUES (#{infoauthId}, #{requestId} , now(), now())")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insert(InfoServiceDrivingPermitOcrDO entity);

    @Update("UPDATE info_service_driving_permit_ocr set " +
            "`main_model` = #{mainModel}, `car_type` = #{carType}, `main_register_date` = #{mainRegisterDate}, `address` = #{address}, `owner` = #{owner} , `main_user_character` = #{mainUserCharacter},`issue_date` = #{issueDate},`main_engine_no` = #{mainEngineNo},`main_vin`=#{mainVin}," +
            "`main_plate_num`=#{mainPlateNum},`provider_id`=#{providerId},`provider_req_id`=#{providerReqId},`modify_time`=now(),`result`=#{result},`service_status`= #{serviceStatus} WHERE id =#{id}")
    int updateById(InfoServiceDrivingPermitOcrDO entity);
}
