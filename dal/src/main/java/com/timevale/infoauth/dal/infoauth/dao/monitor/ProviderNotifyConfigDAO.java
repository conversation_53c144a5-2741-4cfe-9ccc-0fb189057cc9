package com.timevale.infoauth.dal.infoauth.dao.monitor;

import com.timevale.infoauth.dal.infoauth.dataobject.monitor.ProviderNotifyConfigDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/** 供应商通知配置 */
public interface ProviderNotifyConfigDAO {

  @Insert({
          "<script>",
          "INSERT INTO `monitor_provider_notify_config` (`config_id`,`alert_id`, `switch_enabled`, `call_enabled`, `sms_enabled`, ",
          "`ding_crop_enabled`, `ding_robot_enabled`, `ding_robot_token`, `notify_usernames`) VALUES ",
          "(#{configId},#{alertId}, #{switchEnabled}, #{callEnabled}, #{smsEnabled},",
          "#{dingCropEnabled}, #{dingRobotEnabled}, #{dingRobotToken}, #{notifyUsernames})",
          "</script>"
  })
  int insert(ProviderNotifyConfigDO entity);

  @Update({
          "<script>",
          "UPDATE `monitor_provider_notify_config` ",
          "<set>",
          "<if test=\"alertId != null\">",
          "alert_id = #{alertId},",
          "</if>",
          "<if test=\"switchEnabled != null\">",
          "switch_enabled = #{switchEnabled},",
          "</if>",
          "<if test=\"callEnabled != null\">",
          "call_enabled = #{callEnabled},",
          "</if>",
          "<if test=\"smsEnabled != null\">",
          "sms_enabled = #{smsEnabled},",
          "</if>",
          "<if test=\"dingCropEnabled != null\">",
          "ding_crop_enabled = #{dingCropEnabled},",
          "</if>",
          "<if test=\"dingRobotEnabled != null\">",
          "ding_robot_enabled = #{dingRobotEnabled},",
          "</if>",
          "<if test=\"dingRobotToken != null\">",
          "ding_robot_token = #{dingRobotToken},",
          "</if>",
          "<if test=\"notifyUsernames != null\">",
          "notify_usernames = #{notifyUsernames},",
          "</if>",
          "</set>",
          " WHERE `config_id` = #{configId}",
          "</script>",
  })
  void updateById(ProviderNotifyConfigDO entity);

  @Select({
    "<script>",
    "SELECT `id`,`config_id`,`alert_id`,`switch_enabled`,`call_enabled`,`sms_enabled`,`ding_crop_enabled`,`ding_robot_enabled`,`ding_robot_token`,`notify_usernames`,`create_time`,`modify_time` FROM `monitor_provider_notify_config`",
    " WHERE `alert_id`=#{alertId} LIMIT 1",
    "</script>",
  })
  ProviderNotifyConfigDO getByAlertId(String alertId);
}
