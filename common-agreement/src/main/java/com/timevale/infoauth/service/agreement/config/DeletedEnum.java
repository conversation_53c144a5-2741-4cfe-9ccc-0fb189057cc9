package com.timevale.infoauth.service.agreement.config;

/**
 * <AUTHOR>
 * @since 2024/12/13 14:32
 */
public enum DeletedEnum {

    DELETED(1, "已删除"),
    NOT_DELETED(0, "未删除");

    private final Integer code;

    private final String message;

    DeletedEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode(){
        return code;
    }
    public String getMessage(){
        return message;
    }


}


