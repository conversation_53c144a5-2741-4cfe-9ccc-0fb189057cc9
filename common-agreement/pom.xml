<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>infoauth-parent</artifactId>
        <groupId>com.timevale.infoauth</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>common-agreement</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.timevale</groupId>
            <artifactId>mandarin-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.timevale</groupId>
            <artifactId>mandarin-microservice</artifactId>
        </dependency>

        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>infoauth-dal</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.timevale.infoauth</groupId>
            <artifactId>infoauth-facade</artifactId>
            <version>${infoauth-facade.vsersion}</version>
        </dependency>
        <dependency>
            <groupId>com.timevale.filesystem</groupId>
            <artifactId>filesystem-client</artifactId>
            <version>3.1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.timevale.filesystem</groupId>
            <artifactId>filesystem-facade</artifactId>
            <version>3.1.0-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>