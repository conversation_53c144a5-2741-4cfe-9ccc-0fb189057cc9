package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceDrivinglicenceOcrDO;
import org.apache.ibatis.annotations.*;

public interface InfoServiceDrivinglicenceOcrDAO {

  @Insert(
      "INSERT INTO `info_service_drivinglicence_ocr` " +
              "(`infoauth_id`, `request_id` , `name`, `number`, `sex`, `address`, `nationality`, `valid_time`, `cert_type`, `drive_type` ,`create_time`, `modify_time`) " +
              "VALUES (#{infoauthId}, #{requestId} , #{name}, #{number}, #{sex}, #{address}, #{nationality}, #{validTime}, #{certType}, #{driveType} , now(), now())")
  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  int insert(InfoServiceDrivinglicenceOcrDO entity);

  @Select("SELECT * FROM info_service_drivinglicence_ocr WHERE id = #{id}")
  InfoServiceDrivinglicenceOcrDO getById(@Param("id") int id);

  @Update("UPDATE info_service_drivinglicence_ocr set " +
          "`name` = #{name}, `number` = #{number}, `sex` = #{sex}, `address` = #{address}, `nationality`=#{nationality} ,`drive_type`= #{driveType},`valid_time`= #{validTime},`birth`=#{birth}," +
          "`cert_type`=#{certType},`provider_id`=#{providerId},`provider_seq_no`=#{providerSeqNo},`modify_time`=now(),`result`=#{result},`service_status`= #{serviceStatus} WHERE id =#{id}")
  int updateById(InfoServiceDrivinglicenceOcrDO entity);

}
