<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="info_service_org" physicalName="info_service_org" remark="企业实名认证入参表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,NAME,CODEORG,CODEREG,CODEUSC,LEGAL_IDNO,LEGAL_NAME,INFOAUTH_ID,BILLORDER_ID,TIMESTAMP_ID,RESULT,LEGAL_AREA,SERVICE_STATUS,CREATE_TIME,MODIFY_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.NAME,sf.CODEORG,sf.CODEREG,sf.CODEUSC,sf.LEGAL_IDNO,sf.LEGAL_NAME,sf.INFOAUTH_ID,sf.BILLORDER_ID,sf.TIMESTAMP_ID,sf.RESULT,sf.LEGAL_AREA,sf.SERVICE_STATUS,sf.CREATE_TIME,sf.MODIFY_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:info_service_org">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO info_service_org(
            ID
            ,NAME
            ,CODEORG
            ,CODEREG
            ,CODEUSC
            ,LEGAL_IDNO
            ,LEGAL_NAME
            ,INFOAUTH_ID
            ,BILLORDER_ID
            ,TIMESTAMP_ID
            ,RESULT
            ,LEGAL_AREA
            ,SERVICE_STATUS
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES(
             null
            , #{name,jdbcType=VARCHAR}
            , #{codeorg,jdbcType=VARCHAR}
            , #{codereg,jdbcType=VARCHAR}
            , #{codeusc,jdbcType=VARCHAR}
            , #{legalIdno,jdbcType=VARCHAR}
            , #{legalName,jdbcType=VARCHAR}
            , #{infoauthId,jdbcType=VARCHAR}
            , #{billorderId,jdbcType=VARCHAR}
            , #{timestampId,jdbcType=VARCHAR}
            , #{result,jdbcType=INTEGER}
            , #{legalArea,jdbcType=TINYINT}
            , #{serviceStatus,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{modifyTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:info_service_org">
        INSERT INTO info_service_org(
            ID
            ,NAME
            ,CODEORG
            ,CODEREG
            ,CODEUSC
            ,LEGAL_IDNO
            ,LEGAL_NAME
            ,INFOAUTH_ID
            ,BILLORDER_ID
            ,TIMESTAMP_ID
            ,RESULT
            ,LEGAL_AREA
            ,SERVICE_STATUS
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.name,jdbcType=VARCHAR}
                , #{item.codeorg,jdbcType=VARCHAR}
                , #{item.codereg,jdbcType=VARCHAR}
                , #{item.codeusc,jdbcType=VARCHAR}
                , #{item.legalIdno,jdbcType=VARCHAR}
                , #{item.legalName,jdbcType=VARCHAR}
                , #{item.infoauthId,jdbcType=VARCHAR}
                , #{item.billorderId,jdbcType=VARCHAR}
                , #{item.timestampId,jdbcType=VARCHAR}
                , #{item.result,jdbcType=INTEGER}
                , #{item.legalArea,jdbcType=TINYINT}
                , #{item.serviceStatus,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.modifyTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:info_service_org">
        <![CDATA[
        UPDATE info_service_org
        SET
            NAME            = #{name,jdbcType=VARCHAR}
            ,CODEORG         = #{codeorg,jdbcType=VARCHAR}
            ,CODEREG         = #{codereg,jdbcType=VARCHAR}
            ,CODEUSC         = #{codeusc,jdbcType=VARCHAR}
            ,LEGAL_IDNO      = #{legalIdno,jdbcType=VARCHAR}
            ,LEGAL_NAME      = #{legalName,jdbcType=VARCHAR}
            ,INFOAUTH_ID     = #{infoauthId,jdbcType=VARCHAR}
            ,BILLORDER_ID    = #{billorderId,jdbcType=VARCHAR}
            ,TIMESTAMP_ID    = #{timestampId,jdbcType=VARCHAR}
            ,RESULT          = #{result,jdbcType=INTEGER}
            ,LEGAL_AREA      = #{legalArea,jdbcType=TINYINT}
            ,SERVICE_STATUS  = #{serviceStatus,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,MODIFY_TIME     = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=INTEGER}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:info_service_org">
        <![CDATA[
        DELETE FROM info_service_org
        WHERE
            ID = #{id,jdbcType=INTEGER}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:info_service_org">
        SELECT *
        FROM info_service_org
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=INTEGER}
        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByInfoauthId" multiplicity="many" remark="根据普通索引InfoauthId获取数据:info_service_org">
        SELECT *
        FROM info_service_org
        WHERE
        <![CDATA[
            INFOAUTH_ID     = #{infoauthId,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByName" multiplicity="many" remark="根据普通索引Name获取数据:info_service_org">
        SELECT *
        FROM info_service_org
        WHERE
        <![CDATA[
            NAME            = #{name,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>
