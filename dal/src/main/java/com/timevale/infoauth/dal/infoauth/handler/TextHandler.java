//package com.timevale.infoauth.dal.infoauth.handler;
//
//import com.timevale.infoauth.dal.infoauth.utils.StringUtil;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.ibatis.type.BaseTypeHandler;
//import org.apache.ibatis.type.JdbcType;
//
//import java.sql.CallableStatement;
//import java.sql.PreparedStatement;
//import java.sql.ResultSet;
//import java.sql.SQLException;
//
///** 自定义压缩Handler */
//public class TextHandler extends BaseTypeHandler<String> {
//
//  @Override
//  public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
//    return unzip(cs.getString(columnIndex));
//  }
//
//  @Override
//  public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
//    return unzip(rs.getString(columnName));
//  }
//
//  private String unzip(String t) {
//    if (StringUtils.isNotBlank(t)) {
//      return StringUtil.gunzip(t);
//    }
//    return t;
//  }
//
//  @Override
//  public String getNullableResult(ResultSet resultSet, int i) throws SQLException {
//    return unzip(resultSet.getString(i));
//  }
//
//  @Override
//  public void setNonNullParameter(
//      PreparedStatement preparedStatement, int i, String str, JdbcType jdbcType)
//      throws SQLException {
//    preparedStatement.setString(i, StringUtil.gzip(str));
//  }
//}
