//package com.timevale.infoauth.deploy;
//
//import com.alibaba.druid.pool.DruidDataSource;
//
//import org.apache.ibatis.plugin.Interceptor;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//
//import java.util.Collections;
//
//import javax.sql.DataSource;
//
//import esign.cat.integration.CatMybatisInterceptor;
//
//@Configuration
//@MapperScan(
//    basePackages = "com.timevale.infoauth.dal.infoauth.dao",
//    sqlSessionFactoryRef = "mysqlSqlSessionFactory")
//public class MysqlDataSourceConfig {
//
//  @Value("${spring.datasource.url}")
//  private String url;
//
//  @Value("${spring.datasource.username}")
//  private String username;
//
//  @Value("${spring.datasource.password}")
//  private String password;
//
//  @Value("${spring.datasource.initialSize : 4}")
//  private int initialSize;
//
//  @Value("${spring.datasource.maxActive : 100}")
//  private int maxActive;
//
//  @Value("${spring.datasource.minIdle : 0}")
//  private int minIdle;
//
//  @Value("${spring.datasource.maxWait : 5000}")
//  private long maxWait;
//
//  @Value("${spring.datasource.queryTimeout : 10}")
//  private int queryTimeout;
//
//  @Value("${spring.datasource.transactionQueryTimeout : 5}")
//  private int transactionQueryTimeout;
//
//  @Bean(name = "mysqlDataSource")
//  public DataSource mysqlDataSource() {
//    DruidDataSource dataSource = new DruidDataSource();
//    dataSource.setDriverClassName("com.mysql.jdbc.Driver");
//    dataSource.setUrl(url);
//    dataSource.setUsername(username);
//    dataSource.setPassword(password);
//    dataSource.setInitialSize(initialSize);
//    dataSource.setMaxActive(maxActive);
//    dataSource.setMinIdle(minIdle);
//    dataSource.setMaxWait(maxWait);
//    dataSource.setQueryTimeout(queryTimeout);
//    dataSource.setTransactionQueryTimeout(transactionQueryTimeout);
//
//    // 以下设置是要保证连接的可用，因为这里存在一个三层连接嵌套（hbase、phoenix、druid）使用带来的连接是否已经关闭的问题。
//    dataSource.setTimeBetweenEvictionRunsMillis(1000 * 60);
//    dataSource.setMinEvictableIdleTimeMillis(1000 * 60 * 10);
//    dataSource.setMaxEvictableIdleTimeMillis(1000 * 60 * 30);
//    dataSource.setValidationQuery("SELECT 'shisanyue'");
//    dataSource.setTestWhileIdle(true);
//    dataSource.setKeepAlive(false);
//    dataSource.setConnectionInitSqls(Collections.singleton("set names utf8mb4;"));
//    // dataSource.setRemoveAbandoned(true);//对于建立连接过长的连接强制关闭
//    // dataSource.setRemoveAbandonedTimeout(1800);//如果连接建立时间超过了30分钟，则强制将其关闭
//    // dataSource.setLogAbandoned(true);
//    return dataSource;
//  }
//
//  @Bean(name = "mysqlTransactionManager")
//  public DataSourceTransactionManager mysqlTransactionManager(
//      @Qualifier("mysqlDataSource") DataSource mysqlDataSource) {
//    return new DataSourceTransactionManager(mysqlDataSource);
//  }
//
//  @Bean(name = "mysqlSqlSessionFactory")
//  public SqlSessionFactory mysqlSqlSessionFactory(
//      @Qualifier("mysqlDataSource") DataSource mysqlDataSource) throws Exception {
//    final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
//    //        Resource[] resources = new
//    // PathMatchingResourcePatternResolver().getResources("classpath*:common/mapper/mysql/*.xml");
//    CatMybatisInterceptor catMybatisInterceptor = new CatMybatisInterceptor(url);
//    sessionFactory.setPlugins(new Interceptor[] {catMybatisInterceptor});
//    //        sessionFactory.setMapperLocations(resources);
//    sessionFactory.setDataSource(mysqlDataSource);
//    SqlSessionFactory factory = sessionFactory.getObject();
//    factory.getConfiguration().setMapUnderscoreToCamelCase(true);
//    return factory;
//  }
//}
