package com.timevale.infoauth.service.request.query;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InfoServiceCompareRequest extends ToString {

    private String infoauthId;

    private Integer type;

    private Integer objectType;

    private String appId;

    private String name;

    private String idno;

    private String mobile;

    private String bank;

    private String cardno;

    private String codeORG;

    private String codeUSC;

    private String codeREG;

    private String legalName;

    private String legalIdno;

    private String legalArea;

    private String businessStartTime;

    private String businessEndTime;

    private String businessEndTimeDesc;

    private String businessStatus;

    private String providerId;

    private String providerResult;

    private String baseProviderId;

    private String baseProviderResult;

    private String result;

    private String serviceStatus;

    private Integer status;

    private String timestampId;

    private String billorderId;

    private String errId;

    private String userCenterCertType;

    private String createTime;

    private String modifyTime;

    private String startTime;

    private String endTime;

    private Boolean isDelete;

    private int size;

    private int from;
}
