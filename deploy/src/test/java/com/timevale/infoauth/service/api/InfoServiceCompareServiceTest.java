package com.timevale.infoauth.service.api;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.dal.infoauth.dao.*;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.springframework.beans.factory.annotation.Autowired;

@JsonDataIgnore
public class InfoServiceCompareServiceTest extends ApplicationTest {



    @Autowired
    InfoServiceBankauthDAO infoServiceBankauthDAO;

    @Autowired
    InfoServiceDAO infoServiceDAO;

    @Autowired
    InfoQueryService infoQueryService;

    @Autowired
    InfoServiceTelecomauthDAO infoServiceTelecomauthDAO;

    @Autowired
    InfoServiceBankthreeDAO infoServiceBankthreeDAO;

    @Autowired
    InfoServiceIdnoauthDAO infoServiceIdnoauthDAO;

    @Autowired
    InfoServiceOrgDAO infoServiceOrgDAO;

    @Autowired
    InfoServiceOrgcheckDAO infoServiceOrgcheckDAO;
    @Autowired
    OutlierDataRecordDAO outlierDataRecordDAO;



//
//    @Test
//    public void addInfoServiceCompareTest() {
//        String infoauthId = "8de4abd7-56d7-4c42-8725-17dd181a44aa";
//
//        try {
//            InfoServiceBankauthDO infoauthDO = infoServiceBankauthDAO.getByInfoAuthId(infoauthId);
//            InfoServiceDO infoServiceDO = infoServiceDAO.getByInfoAuthId(infoauthId);
//            infoauthDO.setName("test123");
//            infoServiceCompareService.addInfoServiceCompare(NotifyServiceUtil.convert(infoauthDO, infoServiceDO));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//
//
////        AuthInfoRecordQuery request = new AuthInfoRecordQuery();
////        request.setAppId("**********");
////        request.setCertName("郭民意");
////        request.setCertNo("411502199212277713");
////        request.setInfoAuthServiceType(InfoAuthServiceType.PSN_TELECOM3.dbValue());
////        request.setStartTime(new Date(1591007567000L));
////        request.setEndTime(new Date());
////        request.setPageIndex(0);
////        request.setPageSize(5);
////        Long startTime = System.currentTimeMillis();
////        RpcOutput<PsnInfoQueryResponse> result = infoQueryService.psnQueryInfoForRPC(request);
////        List<PsnInfoRecord> psnInfoRecordList = result.getData().getPsnInfoRecordList();
////        for (PsnInfoRecord  psnInfoRecord : psnInfoRecordList) {
////            InfoServiceTelecomauthDO infoServiceTelecomauthDO = infoServiceTelecomauthDAO.getByInfoAuthId(psnInfoRecord.getInfoAuthId());
////
////            OutlierDataRecordDO outlierDataRecordDO = OutlierDataRecordDO.builder()
////                    .infoauthId(infoServiceTelecomauthDO.getInfoauthId())
////                    .type(InfoAuthServiceType.PSN_TELECOM3.dbValue())
////                    .eventType("A")
////                    .requestDate(JsonUtils.obj2json(infoServiceTelecomauthDO))
////                    .status(0)
////                    .createTime(infoServiceTelecomauthDO.getCreateTime())
////                    .modifyTime(infoServiceTelecomauthDO.getModifyTime())
////                    .build();
////            outlierDataRecordDAO.saveOutlierDataRecord(outlierDataRecordDO);
////
////        }
//
//    }
//    @Test
//    public void addInfoServiceCompareTest1() {
//        String infoauthId = "8de4abd7-56d7-4c42-8725-17dd181a44aa";
//        try {
//            InfoServiceBankauthDO entity = infoServiceBankauthDAO.getByInfoAuthId(infoauthId);
//            int infoauthDO = infoServiceBankauthDAO.saveApply(entity);
//        } catch (DuplicateKeyException e) {
//            logger.warn("addInfoServiceCompareTest1----------------------");
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void addInfoServiceCompareTel3Test() {
//        AuthInfoRecordQuery request = new AuthInfoRecordQuery();
//        request.setAppId("**********");
//        request.setCertName("郭民意");
//        request.setCertNo("411502199212277713");
//        request.setInfoAuthServiceType(InfoAuthServiceType.PSN_TELECOM3.dbValue());
//        request.setStartTime(new Date(1591007567000L));
//        request.setEndTime(new Date());
//        request.setPageIndex(0);
//        request.setPageSize(5);
//        Long startTime = System.currentTimeMillis();
//        RpcOutput<PsnInfoQueryResponse> result = infoQueryService.psnQueryInfoForRPC(request);
//        List<PsnInfoRecord> psnInfoRecordList = result.getData().getPsnInfoRecordList();
//        for (PsnInfoRecord  psnInfoRecord : psnInfoRecordList) {
//            InfoServiceTelecomauthDO infoServiceTelecomauthDO = infoServiceTelecomauthDAO.getByInfoAuthId(psnInfoRecord.getInfoAuthId());
//            InfoServiceDO infoServiceDO = infoServiceDAO.getByInfoAuthId(psnInfoRecord.getInfoAuthId());
//            infoServiceCompareService.addInfoServiceCompare(NotifyServiceUtil.convert(infoServiceTelecomauthDO, infoServiceDO));
//        }
//
//    }

//    @Test
//    public void addInfoServiceCompareIdNo2Test() {
//        AuthInfoRecordQuery request = new AuthInfoRecordQuery();
//        request.setAppId("**********");
//        request.setCertName("郭民意");
//        request.setCertNo("411502199212277713");
//        request.setInfoAuthServiceType(InfoAuthServiceType.PSN_ID2.dbValue());
//        request.setServiceStatus(ServiceStatus.FINISHED.dbValue());
//        request.setStartTime(new Date(1591007567000L));
//        request.setEndTime(new Date());
//        request.setPageIndex(0);
//        request.setPageSize(5);
//        Long startTime = System.currentTimeMillis();
//        RpcOutput<PsnInfoQueryResponse> result = infoQueryService.psnQueryInfoForRPC(request);
//        List<PsnInfoRecord> psnInfoRecordList = result.getData().getPsnInfoRecordList();
//        for (PsnInfoRecord  psnInfoRecord : psnInfoRecordList) {
//            InfoServiceIdnoauthDO infoServiceIdnoauthDO = infoServiceIdnoauthDAO.getByInfoAuthId(psnInfoRecord.getInfoAuthId());
//            InfoServiceDO infoServiceDO = infoServiceDAO.getByInfoAuthId(psnInfoRecord.getInfoAuthId());
//            infoServiceCompareService.addInfoServiceCompare(NotifyServiceUtil.convert(infoServiceIdnoauthDO, infoServiceDO));
//        }
//
//    }
//
//    @Test
//    public void addInfoServiceCompareBank4Test() {
//        AuthInfoRecordQuery request = new AuthInfoRecordQuery();
//        request.setAppId("**********");
//        request.setCertName("陈威宇");
//        request.setCertNo("362302198609175011");
//        request.setInfoAuthServiceType(InfoAuthServiceType.PSN_BANK4.dbValue());
//        request.setServiceStatus(ServiceStatus.FINISHED.dbValue());
//        request.setStartTime(new Date(1591266767000L));
//        request.setEndTime(new Date());
//        request.setPageIndex(0);
//        request.setPageSize(5);
//        Long startTime = System.currentTimeMillis();
//        RpcOutput<PsnInfoQueryResponse> result = infoQueryService.psnQueryInfoForRPC(request);
//        List<PsnInfoRecord> psnInfoRecordList = result.getData().getPsnInfoRecordList();
//        for (PsnInfoRecord  psnInfoRecord : psnInfoRecordList) {
//            InfoServiceBankauthDO infoServiceBankauthDO = infoServiceBankauthDAO.getByInfoAuthId(psnInfoRecord.getInfoAuthId());
//            InfoServiceDO infoServiceDO = infoServiceDAO.getByInfoAuthId(psnInfoRecord.getInfoAuthId());
//            infoServiceCompareService.addInfoServiceCompare(NotifyServiceUtil.convert(infoServiceBankauthDO, infoServiceDO));
//        }
//
//    }
//
//    @Test
//    public void addInfoServiceCompareOrgTest() {
//        AuthInfoRecordQuery request = new AuthInfoRecordQuery();
//
//        /**
//         *
//         * {"appId":"**********","startTime":*************,"endTime":*************,"pageSize":25,"pageIndex":0,"queryType":"org","serviceStatus":2}
//         * */
//        request.setAppId("**********");
//        request.setStartTime(new Date(*************L));
//        request.setEndTime(new Date(*************L));
//        request.setPageIndex(0);
//        request.setPageSize(25);
//        request.setQueryType("org");
//        request.setServiceStatus(2);
//        RpcOutput<OrgInfoQueryResponse> result = infoQueryService.orgInfoQueryForRPC(request);
//        List<OrgInfoRecord> orgInfoRecordList = result.getData().getOrgInfoRecordList();
//        for (OrgInfoRecord  orgInfoRecord : orgInfoRecordList) {
//            InfoServiceOrgDO infoServiceOrgDO = infoServiceOrgDAO.getByInfoAuthId(orgInfoRecord.getInfoAuthId());
//            InfoServiceOrgcheckDO infoServiceOrgcheckDO = infoServiceOrgcheckDAO.getByInfoAuthId(orgInfoRecord.getInfoAuthId());
//            InfoServiceDO infoServiceDO = infoServiceDAO.getByInfoAuthId(orgInfoRecord.getInfoAuthId());
//            infoServiceCompareService.addInfoServiceCompare(NotifyServiceUtil.convert(infoServiceOrgDO, infoServiceOrgcheckDO, infoServiceDO));
//        }
//
//    }
//
//    @Test
//    public void queryInfoServiceCompareTest() {
//        String infoauthId = "8de4abd7-56d7-4c42-8725-17dd181a44aa";
//        InfoServiceBankauthDO infoauthDO = infoServiceBankauthDAO.getByInfoAuthId(infoauthId);
//        InfoServiceDO infoServiceDO = infoServiceDAO.getByInfoAuthId(infoauthId);
//        InfoServiceCompareDO infoServiceCompareDO = NotifyServiceUtil.convert(infoauthDO, infoServiceDO);
//        InfoServiceCompareRequest infoServiceCompareRequest = new InfoServiceCompareRequest();
//        BeanUtils.copyProperties(infoServiceCompareDO, infoServiceCompareRequest);
//        InfoServiceCompareResponseDO result = infoServiceCompareService.queryInfoServiceCompareForList(infoServiceCompareRequest);
//        System.out.println("----------" + JSON.toJSONString(result));
//        Assert.assertNotNull(result);
//
//    }


}
