package com.timevale.infoauth.service.api;

import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.infoauth.service.request.query.AuthInfoQueryRequest;
import com.timevale.infoauth.service.response.QuickSignAuthInfoQueryResponse;
import com.timevale.mandarin.common.annotation.RestClient;

/**
 * <AUTHOR>
 */
@RestClient(serviceId = "infoauth-service")
public interface InfoAuthQuickSignQueryService {

  /**
   * 查询个人认证信息
   *
   * @param authInfoQueryRequest
   * @return
   */
  RpcOutput<QuickSignAuthInfoQueryResponse> quickSignQueryPsnInfo(AuthInfoQueryRequest authInfoQueryRequest);

  /**
   * 查询企业认证信息
   *
   * @param authInfoQueryRequest
   * @return
   */
  RpcOutput<QuickSignAuthInfoQueryResponse> quickSignQueryOrgInfo(AuthInfoQueryRequest authInfoQueryRequest);


    /**
     * 快捷签查询个人认证记录
     * @param authInfoQueryRequest 查询参数
     * @return 个人认证记录
     */
  RpcOutput<QuickSignAuthInfoQueryResponse> quickSignQueryPsnListInfo(AuthInfoQueryRequest authInfoQueryRequest);

  /**
   * 快捷签查询企业认证记录
   * @param authInfoQueryRequest 请求参数
   * @return 企业认证记录
   */
  RpcOutput<QuickSignAuthInfoQueryResponse> quickSignQueryOrgListInfo(AuthInfoQueryRequest authInfoQueryRequest);

  /**
   * 查询个人认证信息
   *
   * @param authInfoQueryRequest
   * @return
   */
  @Deprecated
  RpcOutput<QuickSignAuthInfoQueryResponse> authInfoRecordQueryPsnInfo(AuthInfoQueryRequest authInfoQueryRequest);

  /**
   * 查询企业认证信息
   *
   * @param authInfoQueryRequest
   * @return
   */
  @Deprecated
  RpcOutput<QuickSignAuthInfoQueryResponse> authInfoRecordQueryOrgInfo(AuthInfoQueryRequest authInfoQueryRequest);
}
