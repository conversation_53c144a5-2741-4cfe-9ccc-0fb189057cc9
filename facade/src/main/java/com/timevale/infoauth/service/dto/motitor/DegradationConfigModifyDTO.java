package com.timevale.infoauth.service.dto.motitor;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/7/17 21:16
 */
@Data
public class DegradationConfigModifyDTO extends ToString {

    /** 降级配置ID. */
    private String configId;
    /** 告警配置ID */
    private String alertId;

    /** 配置生效开关[0关闭,1开启] */
    private Integer switchEnabled;

    /** 处理策略 */
    private String changeAction;
    //降级供应商
    private String changeDegradeProvider;
    /** 执行时间[秒] */
    private Integer changeTime;

    /**
     * 使用范围
     */
    private String scope;
    private String scopeAppId;
    private String desc;
}
