<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="info_err_adapter" physicalName="info_err_adapter" remark="供应商错误码映射表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,MD5,EXTEND,PROVIDER,ADAPTER_ERR_MSG,ADAPTER_ERR_CODE,PROVIDER_ERR_MSG,PROVIDER_RESULT,PROVIDER_ERR_CODE,TYPE,CREATE_TIME,MODIFY_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.MD5,sf.EXTEND,sf.PROVIDER,sf.ADAPTER_ERR_MSG,sf.ADAPTER_ERR_CODE,sf.PROVIDER_ERR_MSG,sf.PROVIDER_RESULT,sf.PROVIDER_ERR_CODE,sf.TYPE,sf.CREATE_TIME,sf.MODIFY_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:info_err_adapter">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO info_err_adapter(
            ID
            ,MD5
            ,EXTEND
            ,PROVIDER
            ,ADAPTER_ERR_MSG
            ,ADAPTER_ERR_CODE
            ,PROVIDER_ERR_MSG
            ,PROVIDER_RESULT
            ,PROVIDER_ERR_CODE
            ,TYPE
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES(
             null
            , #{md5,jdbcType=VARCHAR}
            , #{extend,jdbcType=VARCHAR}
            , #{provider,jdbcType=VARCHAR}
            , #{adapterErrMsg,jdbcType=VARCHAR}
            , #{adapterErrCode,jdbcType=VARCHAR}
            , #{providerErrMsg,jdbcType=VARCHAR}
            , #{providerResult,jdbcType=VARCHAR}
            , #{providerErrCode,jdbcType=VARCHAR}
            , #{type,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{modifyTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:info_err_adapter">
        INSERT INTO info_err_adapter(
            ID
            ,MD5
            ,EXTEND
            ,PROVIDER
            ,ADAPTER_ERR_MSG
            ,ADAPTER_ERR_CODE
            ,PROVIDER_ERR_MSG
            ,PROVIDER_RESULT
            ,PROVIDER_ERR_CODE
            ,TYPE
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.md5,jdbcType=VARCHAR}
                , #{item.extend,jdbcType=VARCHAR}
                , #{item.provider,jdbcType=VARCHAR}
                , #{item.adapterErrMsg,jdbcType=VARCHAR}
                , #{item.adapterErrCode,jdbcType=VARCHAR}
                , #{item.providerErrMsg,jdbcType=VARCHAR}
                , #{item.providerResult,jdbcType=VARCHAR}
                , #{item.providerErrCode,jdbcType=VARCHAR}
                , #{item.type,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.modifyTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:info_err_adapter">
        <![CDATA[
        UPDATE info_err_adapter
        SET
            MD5             = #{md5,jdbcType=VARCHAR}
            ,EXTEND          = #{extend,jdbcType=VARCHAR}
            ,PROVIDER        = #{provider,jdbcType=VARCHAR}
            ,ADAPTER_ERR_MSG = #{adapterErrMsg,jdbcType=VARCHAR}
            ,ADAPTER_ERR_CODE = #{adapterErrCode,jdbcType=VARCHAR}
            ,PROVIDER_ERR_MSG = #{providerErrMsg,jdbcType=VARCHAR}
            ,PROVIDER_RESULT = #{providerResult,jdbcType=VARCHAR}
            ,PROVIDER_ERR_CODE = #{providerErrCode,jdbcType=VARCHAR}
            ,TYPE            = #{type,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,MODIFY_TIME     = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:info_err_adapter">
        <![CDATA[
        DELETE FROM info_err_adapter
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:info_err_adapter">
        SELECT *
        FROM info_err_adapter
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <!-- 根据唯一约束操作数据 -->
    <operation name="updateByIdxMd5" paramtype="object" remark="根据唯一约束IdxMd5更新表:info_err_adapter">
        <![CDATA[
        UPDATE info_err_adapter
        SET
            EXTEND          = #{extend,jdbcType=VARCHAR}
            ,PROVIDER        = #{provider,jdbcType=VARCHAR}
            ,ADAPTER_ERR_MSG = #{adapterErrMsg,jdbcType=VARCHAR}
            ,ADAPTER_ERR_CODE = #{adapterErrCode,jdbcType=VARCHAR}
            ,PROVIDER_ERR_MSG = #{providerErrMsg,jdbcType=VARCHAR}
            ,PROVIDER_RESULT = #{providerResult,jdbcType=VARCHAR}
            ,PROVIDER_ERR_CODE = #{providerErrCode,jdbcType=VARCHAR}
            ,TYPE            = #{type,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,MODIFY_TIME     = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE
            MD5             = #{md5,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="deleteByIdxMd5" remark="根据唯一约束IdxMd5删除数据:info_err_adapter">
        <![CDATA[
        DELETE FROM info_err_adapter
        WHERE
            MD5             = #{md5,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="getByIdxMd5" multiplicity="one" remark="根据唯一约束IdxMd5获取数据:info_err_adapter">
        SELECT *
        FROM info_err_adapter
        WHERE
        <![CDATA[
            MD5             = #{md5,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>
