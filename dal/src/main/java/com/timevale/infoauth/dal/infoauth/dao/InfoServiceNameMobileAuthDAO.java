package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceNameMobileAuthDO;
import org.apache.ibatis.annotations.*;

/**
 * The Table info_service_name_mobile_auth
 */
public interface InfoServiceNameMobileAuthDAO {

  @Insert(
      "INSERT INTO `info_service_name_mobile_auth` "
          + "(`infoauth_id`, `name`, `mobile`, `provider_id`, `provider_result`, `result`, `service_status`, `timestamp_id`, `create_time`, `modify_time`) "
          + "VALUES (#{infoauthId}, #{name}, #{mobile}, #{providerId}, #{providerResult}, #{result}, #{serviceStatus}, #{timestampId}, now(), now())")
  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  int insert(InfoServiceNameMobileAuthDO entity);
}