package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceOcrDO;
import org.apache.ibatis.annotations.*;

public interface InfoServiceOcrDAO {
  /**
   * desc:插入表:info_service_org.<br>
   *
   * @param entity entity
   * @return int
   */
  @Insert(
      "INSERT INTO `info_service_ocr` "
          + "(`id`, `infoauth_id`, `photo_idcard_face`, `photo_idcard_emblem`, `create_time`, `modify_time`) "
          + "VALUES "
          + "(#{id}, #{infoauthId}, #{photoIdcardFace}, #{photoIdcardEmblem}, now(), now())")
  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  int insert(InfoServiceOcrDO entity);

  /**
   * desc:插入表:info_service_org.<br>
   *
   * @param entity entity
   * @return int
   */
  @Insert(
      "INSERT INTO `info_service_ocr` "
          + "(`id`, `infoauth_id`, `photo_idcard_face`, `photo_idcard_emblem`,"
          + "`ocr_name`,`ocr_idno`,`ocr_birth`,`ocr_address`,`ocr_nation`,`ocr_sex`,provider_id"
          + " `create_time`, `modify_time`) "
          + "VALUES "
          + "(#{id}, #{infoauthId}, #{photoIdcardFace}, #{photoIdcardEmblem},#{providerId}"
          + "#{ocrName},#{ocrIdno},#{ocrBirth},#{ocrAddress},#{ocrNation},#{ocrSex}, now(), now())")
  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  int insertOcrMessage(InfoServiceOcrDO entity);

  @Update("update info_service_ocr set service_status=#{status} where id = #{id}")
  void updateStatus(@Param("id") Integer id, @Param("status") Integer status);

  @Update(
      "update info_service_ocr set "
          + "ocr_name=#{ocrName},ocr_idno=#{ocrIdno},ocr_birth=#{ocrBirth},ocr_address=#{ocrAddress},ocr_nation=#{ocrNation},ocr_sex=#{ocrSex},"
          + "ocr_start_time=#{ocrStartTime},ocr_end_time=#{ocrEndTime},ocr_organ=#{ocrOrgan},ocr_photo_idcard_result=#{ocrPhotoIdcardResult},provider_id=#{providerId},`result`=#{result},`service_status`= #{serviceStatus} "
          + "where id = #{id}")
  void updateOcrResult(InfoServiceOcrDO entity);

  @Select("select * from info_service_ocr where infoauth_id = #{infoauthId}")
  InfoServiceOcrDO queryByInfoAuthId(@Param("infoauthId") String infoauthId);
}
