package com.timevale.infoauth.dal.infoauth.dataobject;

import com.timevale.infoauth.dal.infoauth.utils.StringUtil;
import lombok.Getter;

@Getter
public class InfoServiceOcrDO {
  private Integer id;
  private String infoauthId;
  private String providerId;
  private String name;
  private String idno;
  private String photoIdcardFace;
  private String photoIdcardEmblem;
  private Integer result;
  private Integer serviceStatus;
  private String providerResult;
  private String ocrPhotoIdcardResult;
  private String ocrName;
  private String ocrIdno;
  private String ocrBirth;
  private String ocrAddress;
  private String ocrNation;
  private Integer ocrSex;
  private String ocrStartTime;
  private String ocrEndTime;
  private String ocrOrgan;
  private String createTime;
  private String modifyTime;

  public void setId(Integer id) {
    this.id = id;
  }

  public void setInfoauthId(String infoauthId) {
    this.infoauthId = infoauthId;
  }

  public void setProviderId(String providerId) {
    this.providerId = providerId;
  }

  public void setName(String name) {
    this.name = name;
  }

  public void setIdno(String idno) {
    this.idno = idno;
  }

  public void setPhotoIdcardFace(String photoIdcardFace) {
    this.photoIdcardFace = photoIdcardFace;
  }

  public void setPhotoIdcardEmblem(String photoIdcardEmblem) {
    this.photoIdcardEmblem = photoIdcardEmblem;
  }

  public void setResult(Integer result) {
    this.result = result;
  }

  public void setServiceStatus(Integer serviceStatus) {
    this.serviceStatus = serviceStatus;
  }

  public void setProviderResult(String providerResult) {
    this.providerResult = providerResult;
  }

  public void setOcrPhotoIdcardResult(String ocrPhotoIdcardResult) {
    this.ocrPhotoIdcardResult = StringUtil.truncation(ocrPhotoIdcardResult, 300);
  }

  public void setOcrName(String ocrName) {
    this.ocrName = StringUtil.truncation(ocrName, 50);
  }

  public void setOcrIdno(String ocrIdno) {
    this.ocrIdno = StringUtil.truncation(ocrIdno, 20);
  }

  public void setOcrBirth(String ocrBirth) {
    this.ocrBirth = StringUtil.truncation(ocrBirth, 20);
  }

  public void setOcrAddress(String ocrAddress) {
    this.ocrAddress = StringUtil.truncation(ocrAddress,200);
  }

  public void setOcrNation(String ocrNation) {
    this.ocrNation = StringUtil.truncation(ocrNation,10);
  }

  public void setOcrSex(Integer ocrSex) {
    this.ocrSex = ocrSex;
  }

  public void setOcrStartTime(String ocrStartTime) {
    this.ocrStartTime = StringUtil.truncation(ocrStartTime,20);
  }

  public void setOcrEndTime(String ocrEndTime) {
    this.ocrEndTime = StringUtil.truncation(ocrEndTime,20);
  }

  public void setOcrOrgan(String ocrOrgan) {
    this.ocrOrgan = StringUtil.truncation(ocrOrgan,200);
  }

  public void setCreateTime(String createTime) {
    this.createTime = createTime;
  }

  public void setModifyTime(String modifyTime) {
    this.modifyTime = modifyTime;
  }
}
