package com.timevale.infoauth.dal.infoauth.dataobject;

import java.util.Date;

/**
 * The table. info_service
 *
 * <AUTHOR> Kunpeng
 */
public class InfoServiceDO extends InfoServiceAuthDO {

  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;
  /** id 自增主键. */
  private Long id;
  /** infoauthId 业务主键. */
  private String infoauthId;
  /** type 信息比对类型. */
  private Integer type;
  /** status 比对状态. */
  private Integer status;
  /** objectType 对象类型,如个人/企业. */
  private Integer objectType;

  /** 应用Id */
  private String appId;

  /** Set createTime 记录创建时间. */
  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  /**
   * Get createTime 记录创建时间.
   *
   * @return the string
   */
  public Date getCreateTime() {
    return createTime;
  }

  /** Set modifyTime 记录修改时间. */
  public void setModifyTime(Date modifyTime) {
    this.modifyTime = modifyTime;
  }

  /**
   * Get modifyTime 记录修改时间.
   *
   * @return the string
   */
  public Date getModifyTime() {
    return modifyTime;
  }

  /** Set id 自增主键. */
  public void setId(Long id) {
    this.id = id;
  }

  /**
   * Get id 自增主键.
   *
   * @return the string
   */
  public Long getId() {
    return id;
  }

  /** Set infoauthId 业务主键. */
  public void setInfoauthId(String infoauthId) {
    this.infoauthId = infoauthId;
  }

  /**
   * Get infoauthId 业务主键.
   *
   * @return the string
   */
  public String getInfoauthId() {
    return infoauthId;
  }

  /** Set type 信息比对类型. */
  public void setType(Integer type) {
    this.type = type;
  }

  /**
   * Get type 信息比对类型.
   *
   * @return the string
   */
  public Integer getType() {
    return type;
  }

  /** Set status 比对状态. */
  public void setStatus(Integer status) {
    this.status = status;
  }

  /**
   * Get status 比对状态.
   *
   * @return the string
   */
  public Integer getStatus() {
    return status;
  }

  /** Set objectType 对象类型,如个人/企业. */
  public void setObjectType(Integer objectType) {
    this.objectType = objectType;
  }

  /**
   * Get objectType 对象类型,如个人/企业.
   *
   * @return the string
   */
  public Integer getObjectType() {
    return objectType;
  }

  public String getAppId() {
    return appId;
  }

  public void setAppId(String appId) {
    this.appId = appId;
  }
}
