package com.timevale.infoauth.service.request.backstage;

import com.timevale.mandarin.common.result.ToString;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> href="mailto:<EMAIL>">jiuchen</a>
 */
@Data
public class CreateOrgMockRequest extends ToString {

  /**
   * @see com.timevale.infoauth.service.enums.InfoAuthServiceType
   */
  private String authType;

  private List<OrgMockDataRequest> mockList;

  /**
   * 失效时间
   */
  private long lostTime;
}
