package com.timevale.infoauth.mayi;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.ZhimaCreditEpDossierInfoQueryRequest;
import com.alipay.api.response.ZhimaCreditEpDossierInfoQueryResponse;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * https://opendocs.alipay.com/pre-open/084uck?pathHash=ce8c0929#4%20%E7%AC%AC%E4%BA%8C%E6%AD%A5%EF%BC%9A%E7%94%B3%E8%AF%B7%E8%AE%A4%E8%AF%81%E6%9C%8D%E5%8A%A1URL
 * <AUTHOR>
 * @since 2023/8/28 21:09
 */
@Component
public class OrgZhimaV3Client {


  private static final String ALIPAY_APPID = "2021004114605075";
  private static final String ALIPAY_PRIVATE_KEY = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCxrY1quQ3S2HAaFPFhCCyvj6EXu1YRGoRF/3u6xmmdNeyOzYOCZIWrlsGQC19mgnZ+0ki3X1fyUiaettDyKchGet4sIs80UZ7pRpTojvnQgGhgXcIh0AVJrf68/P9nfRq+HM7EoI5OSCi6G5gCznTQQ31pM6IeoL5uxa1TDlhZSTLgFEjW15iq0jRa+gyATl+0YWktxktpNRZBBCBA84EtMUkeTH5ySnAKb6HcRk+k8yZY1ba2onJViG/DuoYEDGjJJUBZmrGHzJ5qH4gqrt2PeAyU0kiZXv+eAZjmkKAVriisftxjFp/+ztF7tV4tmv6Pg+mKPRJuOY/57PSyv81fAgMBAAECggEAZf9RbXCqRN3HDNH091TwAiCbw7nhYJQuVtihOMlqG7u2/wtvcU0vhx9WItDEhV5jk843vDzZidwrG3WrYTknJX6nSAL7m/c3GIdSOGbLdiwgvyFTroFyZ6eg0vER1WD2FykGcc4Ro1K9PolUrFfEq0HzHo9rg9WXPJPyOfnEMgxkbYZ7hPNdOOVlRKXWfzJtFVToh+RdsACk1SsxIlnAdwXAWZRdcwzwvaGgIxhYLFfQw7ul/qa/4fhqi7eFAYBuP8E6MSFDP5xqn5g+2j3vJP6D9eUCda742W/7vA1GsPXtFHYevNHLqi18csVMo/JNsk7XC0H2VuD/BTPQA76KAQKBgQDXYn7F1S1HXgWOoP1I7hQ4f5xazKIFqcPAM5ejNArMQB5wxf5LciWeZ2kAuKGNRSmfUd3mExthmnqNV8gdW+wZcl4CcDlBtiu5ICoqsfkWYE3Z+fEsyJ1NNLNkQmXIDDqjPpOqc2+I3BIlJgETqKyLkTxt4c2CD4VtlwuaYvgYgQKBgQDTLsuE3Hc6XfFPHkBaM+x4gh+qjTOj0yH9WZD46NpDEUI1ee23tNsZnHo581CGMLMv/JvVTf1Wik33TAv9Klrph0HfmTMi3gdUVrfEeaTMUngTaQXSz87P+yXqMxG3bZGjRXphpKLM243FqNSB+zcHWuYZ3NLmMlC/gGEJza/13wKBgEUj+yoqqqRUTFhi2awFsTwJyvli40gKHSqf5Tegxy6WDq+I/37cn7Lf41c17bZZ02ivcRogXBPS8TBaU4UJMxtgGUmTGO49On/tEVwvUZSbSaKFl6QWqlhUaJnwrpevRUuOFP0MnWtDqhd+wa274dfTwaBMchokPWLWsATZbGcBAoGBAJ9MwUCrfVSN4KUp0Kk6Wbpdjb19XeLKNO2DTpQ0MdA9iJAGNYdJ6axcGOb131waCFC+YF56wgsxsLZGqxSBiqp12P7t9WDr0uOPtneCuwlVfDZIFph7ISpJ7MPJWiffguiCVFqrchLN2xeqzwJtmYj2zHPd20jB059hG9HlUkJJAoGBAJqWJ2d1Mo747eQoNfcjrvSPuVChZKtomfLHHVBlOR2EqrdREn4NRefgKeYgerIxYXDVstLWvvcv+QLTWslJaPsOcO2K+UTrbl3Tq4anim+yapqat8NFi0C7PBV5lRwb5VR28z83icvslEFwewf+30fc1b31FM+4rHj1bx8oZzA7";
  private static final String ALIPAY_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgbk0md7wSp1lZZ0y6Y7lpB9iCoY0Ojh/oHZpfHevp/8kmE3hlVQRWkcYLSe2HfcxCpFtdfI4Ao9mkrkagbqSyL7OmO77CO2fQ/neFZ5KSEyA6ivw1rQXnXLVh8deYwTg2x4ED28OeoWXYdpkZz0LPXCMwpBloqFLktNfJn/QF+LdgqpPD74lCoBeMsChO3iKW17wxo2BkgNwzTzBvYqmalHLkasp+dwWNUtkPrGa3AI002LXK7JHnHK/RvENXslo1umc3VQSEFTmwYSflpS3L4YHikEBv2PQCCPeE5H1pCTgSkpSekQ0s+DbP34Yt3d0lZxVOjpxSCbxqrQ8f+a58wIDAQAB";

  public static final AlipayClient alipayClient;
  static {
    // 第一步：初始化认证信息
     alipayClient =
            new DefaultAlipayClient(
                    "https://openapi.alipay.com/gateway.do",
                    ALIPAY_APPID,
                    ALIPAY_PRIVATE_KEY,
                    "json",
                    "GBK",
                    ALIPAY_PUBLIC_KEY,
                    "RSA2");
  }






  public static void main(String[] args) throws AlipayApiException {

    AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do",ALIPAY_APPID,ALIPAY_PRIVATE_KEY,"json","GBK",ALIPAY_PUBLIC_KEY,"RSA2");


    List<String> list = new ArrayList<String>();

    //企业
    list.add("91331004092797378L");
    //企业（2015之前初建的，含有字母 ISOV）
    list.add("91350212M0000M4E0V");
    //企业（注销）
    list.add("91330104MA2CC8033L");
    //个体工商户
    list.add("92440300MA5DE4U14Y");
    //个体工商户（2023-10-30 新注册）
    list.add("92371312MAD14RBC5X");
    //律所
    list.add("311100007263734044");
    //合作社
    list.add("93340621MA2UGLMP7L");
    //社会组织
    list.add("12110000400567903B");
    //社会组织
    list.add("51640000MJX1502942");

    //工商注册号
    list.add("331004000131815");
    // 组织机构代码
    // {"msg":"Business Failed","code":"40004","sub_msg":"参数有误参数不正确","sub_code":"INVALID_PARAMETER"}
    list.add("09279737-8");
    //经济合作社
    //{"msg":"Business Failed","code":"40004","sub_msg":"企业信息异常","sub_code":"COMPANY_NOT_EXIST"}
    list.add("N2330703579327722U");
    //错误企业证件号
    //{"msg":"Business Failed","code":"40004","sub_msg":"企业信息异常","sub_code":"COMPANY_NOT_EXIST"}
    list.add("92440300MA5DE2U14Y");
    //格式错误企业证件号
    //"msg":"Business Failed","code":"40004","sub_msg":"参数有误参数不正确","sub_code":"INVALID_PARAMETER"
    list.add("92330382MA298Q5D");
     for(int i=0;i<list.size();i++){

       ZhimaCreditEpDossierInfoQueryResponse response = call(alipayClient, list.get(i));
       System.out.println(response.getEpCertNo()+" "+response.getEpStatus()+" ==== "+i);
     }

  }

  public static ZhimaCreditEpDossierInfoQueryResponse call(AlipayClient alipayClient, String ep_cert_no) throws AlipayApiException {
    long startTime = System.currentTimeMillis();
    ZhimaCreditEpDossierInfoQueryRequest request = new ZhimaCreditEpDossierInfoQueryRequest();
    request.setBizContent("{" +
            "  \"ep_cert_no\":\""+ep_cert_no+"\"" +
            "}");
    ZhimaCreditEpDossierInfoQueryResponse response = alipayClient.execute(request);
    System.out.println(response.getBody());
    long endTime = System.currentTimeMillis();
    if(response.isSuccess()){
      System.out.println("调用成功 ::" +(endTime - startTime));
    } else {
      System.out.println("调用失败 ::" +(endTime - startTime));
    }
    return response;
  }


}
