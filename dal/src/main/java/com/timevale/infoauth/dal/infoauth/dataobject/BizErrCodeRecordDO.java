package com.timevale.infoauth.dal.infoauth.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/** 错误原因流水表（固化错误原因） */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BizErrCodeRecordDO {

  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录更新时间. */
  private Date modifyTime;
  /** id 自增主键. */
  private Long id;
  /** msg 对外透出错误信息（固化）. */
  private String msg;
  /** code 对外透出错误码（固化）. */
  private String code;
  /** pMsg 供应商错误信息（固化）. */
  private String pMsg;
  /** pCode 供应商错误码（固化）. */
  private String pCode;
  /** extend 扩展字段. */
  private String extend;
  /** trackId trackId. */
  private String trackId;
  /** provider 供应商名称. */
  private String provider;
  /** infoauthId 业务主键. */
  private String infoauthId;
  /** bizType 信息比对类型. */
  private Integer bizType;
}
