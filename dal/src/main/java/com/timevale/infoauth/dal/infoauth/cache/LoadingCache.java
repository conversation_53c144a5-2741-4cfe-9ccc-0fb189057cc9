package com.timevale.infoauth.dal.infoauth.cache;

import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.support.SimpleCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Configuration
@EnableCaching
public class LoadingCache {
    @Bean
    @Resource
    public CacheManager cacheManager(){
        SimpleCacheManager cacheManager = new SimpleCacheManager();
        List<Cache> caches = new ArrayList<>();
        caches.add(new MyRedisCacheManager(false,false,CacheConstant.CACHE_NAME_PROVIDER,CacheConstant.expiration));
        caches.add(
                new MyRedisCacheManager(
                        false,
                        false,
                        CacheConstant.CACHE_INFO_PROVIDER_DAO,
                        CacheConstant.expiration));
        cacheManager.setCaches(caches);
        return cacheManager;
    }
}
