中数智汇

接口文档登录地址：https://testapi.bidata.com.cn/docs/login.html
用户名：HZTG1
登录密码：VHEHCUKK

企业工商信息查询
GET /entinfo

入参：
creditcode 统一信用代码
regno 企业注册号
orgcode 组织机构代码
name 企业名称

响应示例：
````
{
  "CODE": 200,
  "MSG": "OK",
  "ENT_INFO": {
    "BASIC": {
      "ABUITEM": "许可经营项目",
      "ORGCODES": "组织机构代码",
      "REGNO": "注册号",
      "CREDITCODE": "统一信用代码",
      "OPTO": "经营期限至",
      "OPFROM": "经营期限自",
      "ENTNAME": "企业名称",
      "REGCAP": "注册资本（企业:万元）",
      "ESDATE": "成立日期",
      "FRNAME": "法定代表人/负责人/执行事务合伙人",
      "REGORG": "登记机关",
      "ENTSTATUS": "经营状态",
      "ENTSTATUSCODE": "经营状态编码",
      "ENTTYPE": "企业类型",
      "REGORGPROVINCE": "所在省份",
      "REGORGCITY": "所在城市",
      "REGCITY": "所在城市编码",
      "REGORGDISTRICT": "所在区/县",
      "REGCAPCURCODE": "注册资本币种代码",
      "REGCAPCUR": "注册资本币种",
      "ANCHEYEAR": "最后年检年度",
      "POSTALCODE": "邮编",
      "DOM": "住址",
      "REVDATE": "吊销日期",
      "CANDATE": "注销日期",
      "REGORGCODE": "注册地址行政编号",
      "ZSOPSCOPE": "经营业务范围",
      "ENTITYTYPE": "实体类型",
      "ENTNAME_OLD": "曾用名",
      "APPRDATE": "核准日期",
      "ENTTYPECODE": "企业(机构)类型编码",
      "S_EXT_NODENUM": "所在省份编码",
      "ENTID": "企业ENTID"
    },
    "BASICLIST": {
      "ENTNAME": "企业名称",
      "ENTID": "企业ENTID",
      "ORGCODES": "组织机构代码",
      "CREDITCODE": "统一信用代码",
      "REGNO": "注册号",
      "FRNAME": "法定代表人/负责人/执行事务合伙人",
      "REGCAP": "注册资本（企业:万元）",
      "ESDATE": "成立日期",
      "ENTITYTYPE": "实体类型",
      "ENTTYPE": "企业类型",
      "ENTSTATUS": "经营状态",
      "ENTSTATUSCODE": "经营状态编码",
      "CANDATE": "注销日期",
      "REVDATE": "吊销日期",
      "APPRDATE": "核准日期",
      "REGORGPROVINCE": "所在省份"
    }
  }
}
````

组织机构信息查询
GET /orgs

入参：
creditcode 统一社会信用代码
regno 注册号
orgcode 组织机构代码
name 组织机构名称

响应示例：
````
{
  "CODE": 200,
  "MSG": "OK",
  "ORGS": [
    {
      "ID": "组织机构ID",
      "JGDM": "组织机构代码",
      "JGMC": "组织机构名称",
      "FDDBR": "法人代表姓名",
      "BZJG": "办证机构",
      "ZCRQ": "注册日期",
      "JGLX": "机构类型",
      "XZQH": "行政区划",
      "JGDZ": "机构地址",
      "BZRQ": "代码证办证日期",
      "ZFRQ": "代码证作废日期",
      "ZYBZ": "质疑标志",
      "CREDITCODE": "统一社会信用代码",
      "REGNO": "注册号",
      "JYFW": "经营范围",
      "JYZT": "经营状态",
      "ZCZJ": "注册资本",
      "JHBZ": "校核标志",
      "BGRQ": "发照日期",
      "JJLXDM": "经济类型名称",
      "JYDZ": "生产经营地址",
      "JJHYDM": "经济行业名称",
      "ZGMC": "上级主管部门",
      "EMAIL": "电子邮箱",
      "URL": "网址",
      "UPDATEDATE": "更新时间"
    }
  ]
}
````














