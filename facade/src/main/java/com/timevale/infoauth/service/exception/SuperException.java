package com.timevale.infoauth.service.exception;

import com.timevale.mandarin.base.exception.BaseRuntimeException;

public class SuperException extends BaseRuntimeException {
  private String formated;

  public SuperException(String message) {
    super(message);
    this.formated = message;
  }

  public String getFormated() {
    return formated;
  }

  public void setFormated(String formated) {
    this.formated = formated;
  }
}
