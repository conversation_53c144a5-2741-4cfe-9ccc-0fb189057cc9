package com.timevale.infoauth.dal.infoauth.dao.monitor;

import com.timevale.infoauth.dal.infoauth.dataobject.monitor.AlertConfigDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/** 供应商告警配置 */
public interface AlertConfigDAO {

  @Insert({
          "<script>",
          "INSERT INTO `monitor_provider_alert_config` (`alert_id`, `target_type`, `target_id`, `expression`, ",
          "`extend`, `desc`) VALUES ",
          "(#{alertId}, #{targetType}, #{targetId}, #{expression},",
          "#{extend}, #{desc})",
          "</script>"
  })
  int insert(AlertConfigDO entity);

  @Update({
          "<script>",
          "UPDATE `monitor_provider_alert_config` ",
          "<set>",
          "<if test=\"expression != null\">",
          "expression = #{expression},",
          "</if>",
          "<if test=\"extend != null\">",
          "extend = #{extend},",
          "</if>",
          "<if test=\"desc != null\">",
          " `desc` = #{desc},",
          "</if>",
          "</set>",
          " WHERE `alert_id` = #{alertId}",
          "</script>",
  })
  void updateById(AlertConfigDO entity);


  @Select({
          "<script>",
          "SELECT `id`,`alert_id`,`target_type`,`target_id`,`expression`,`extend`,`desc`,`create_time`,`modify_time` FROM `monitor_provider_alert_config`",
          " WHERE target_type = #{bizType} and`target_id`='Default' LIMIT 20",
          "</script>",
  })
  List<AlertConfigDO> getByBizTypeDefault(String bizType);

  @Select({
          "<script>",
          "SELECT `id`,`alert_id`,`target_type`,`target_id`,`expression`,`extend`,`desc`,`create_time`,`modify_time` FROM `monitor_provider_alert_config`",
          " WHERE target_type = #{bizType}  and`target_id`=#{providerId} LIMIT 20",
          "</script>",
  })
  List<AlertConfigDO> getByProviderId(@Param("bizType") String bizType,@Param("providerId")  String providerId);

  @Select({
          "<script>",
          "SELECT `id`,`alert_id`,`target_type`,`target_id`,`expression`,`extend`,`desc`,`create_time`,`modify_time` FROM `monitor_provider_alert_config`",
          " WHERE `alert_id`=#{alertId} LIMIT 20",
          "</script>",
  })
  AlertConfigDO getByAlertId(String alertId);
}
