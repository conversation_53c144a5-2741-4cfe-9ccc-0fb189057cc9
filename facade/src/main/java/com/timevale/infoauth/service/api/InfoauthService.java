package com.timevale.infoauth.service.api;

import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.infoauth.service.exception.ParameterException;
import com.timevale.infoauth.service.exception.SuperException;
import com.timevale.infoauth.service.request.*;
import com.timevale.infoauth.service.request.query.AuthResultRequest;
import com.timevale.infoauth.service.response.AuthResultResponse;
import com.timevale.infoauth.service.response.BankCardOcrResponse;
import com.timevale.infoauth.service.response.BankFourDetailResponse;
import com.timevale.infoauth.service.response.BankFourResponse;
import com.timevale.infoauth.service.response.BankThreeResponse;
import com.timevale.infoauth.service.response.CompareWithSourceResponse;
import com.timevale.infoauth.service.response.CompareWithoutSourceResponse;
import com.timevale.infoauth.service.response.DrivingLicenceResponse;
import com.timevale.infoauth.service.response.DrivingPermitOcrResponse;
import com.timevale.infoauth.service.response.ForeignPermanentOcrResponse;
import com.timevale.infoauth.service.response.GeneralCharactersOcrResponse;
import com.timevale.infoauth.service.response.IdnoAuthResponse;
import com.timevale.infoauth.service.response.LawFirmTwoResponse;
import com.timevale.infoauth.service.response.OcrResponse;
import com.timevale.infoauth.service.response.OrgInfoAuthResponse;
import com.timevale.infoauth.service.response.OrgLicenseOcrResponse;
import com.timevale.infoauth.service.response.OrgSummaryResponse;
import com.timevale.infoauth.service.response.OrganTwoResponse;
import com.timevale.infoauth.service.response.PsnBankFourDetailResponse;
import com.timevale.infoauth.service.response.PsnBankThreeDetailResponse;
import com.timevale.infoauth.service.response.PsnTelecomDetailResponse;
import com.timevale.infoauth.service.response.SocialOrganTwoResponse;
import com.timevale.infoauth.service.response.TelecomDetailResponse;
import com.timevale.infoauth.service.response.TelecomResponse;
import com.timevale.infoauth.service.response.VehicleRegCertOcrResponse;
import com.timevale.infoauth.service.response.query.OrgInfoResponse;
import com.timevale.mandarin.common.annotation.RestClient;

/** 方法名、入参、出参等 不是 我定义的，我只是调整下顺序和格式化喔（改造计划需要一步步来喔） */
@RestClient(serviceId = "infoauth-service")
public interface InfoauthService {

  /** 企业四要素 */
  OrgInfoAuthResponse orgInfoFour(OrgInfoAuthRequest auth);

  /** 企业三要素 */
  OrgInfoAuthResponse orgInfoAuth(OrgInfoAuthRequest auth);


  /** 企业二要素 */
  RpcOutput<OrganTwoResponse> organTwo(OrganTwoRequest auth);

  /** 企业实名认证接口---供应商接口查询byName */
  OrgInfoAuthResponse orgInfoAuthByName(OrgInfoAuthRequest auth);

  /** 社会组织二要素 */
  SocialOrganTwoResponse socialOrganTwo(SocialOrganTwoRequest auth);

  /** 社会组织三要素 */
  OrgInfoAuthResponse socialOrganizationByName(OrgInfoAuthRequest auth);

  /** 律所二要素 */
  LawFirmTwoResponse lawFirmTwo(LawFirmTwoRequest auth);

  /** 律所三要素 */
  OrgInfoAuthResponse lawFirmByName(OrgInfoAuthRequest auth);

  /** 组织注册号三要素 */
  OrgInfoAuthResponse registrationNoThree(OrgInfoAuthRequest auth);

  /** 个人银行卡四要素 */
  BankFourResponse psnBankFour(BankFourRequest auth) throws ParameterException, SuperException;

  /** 个人银行卡四要素详情版（内部使用，未来废弃） */
  BankFourDetailResponse bankFourDetail(BankFourRequest auth);

  /** 个人银行卡四要素详情版 */
  PsnBankFourDetailResponse psnBankFourDetail(BankFourRequest auth);

  /** 个人银行卡三要素 */
  BankThreeResponse bankThree(BankThreeRequest auth) throws ParameterException, SuperException;

  /** 个人银行卡三要素详情版 */
  PsnBankThreeDetailResponse bankThreeDetail(BankThreeRequest auth);

  /** 个人运营商三要素 */
  TelecomResponse telecomAuth(TelecomRequest auth) throws ParameterException, SuperException;

  /** 个人运营商三要素详情版（内部使用，未来废弃） */
  TelecomDetailResponse telecomDetail(TelecomRequest auth)
      throws ParameterException, SuperException;

  /** 个人运营商三要素详情版 */
  PsnTelecomDetailResponse telecomAuthDetail(TelecomRequest auth);

  /** 个人身份证二要素 */
  IdnoAuthResponse idnoAuth(IdnoAuthRequest auth) throws ParameterException, SuperException;

  /**
   * 个人姓名手机号二要素
   */
  IdnoAuthResponse psnNameMobileAuth(NameMobileRequest auth);

  /** 身份证OCR */
  RpcOutput<OcrResponse> ocr(OcrRequest auth);

  /** 外国人永久居住证OCR */
  RpcOutput<ForeignPermanentOcrResponse> foreignPermanentOcr(ForeignPermanentOcrRequest auth);

  /**
   * 通用文字识别OCR
   */
  RpcOutput<GeneralCharactersOcrResponse> generalCharactersOcr(GeneralCharactersOcrRequest auth);

  /**
   * 机动车登记证书OCR
   */
  RpcOutput<VehicleRegCertOcrResponse> vehicleRegCertOcr(VehicleRegCertOcrRequest auth);

  /** 银行卡OCR */
  RpcOutput<BankCardOcrResponse> bankCardOcr(BankCardOcrRequest auth);

  /** 营业执照OCR */
  RpcOutput<OrgLicenseOcrResponse> orgLicenseOcr(OrgLicenseOcrRequest auth);

  /** 驾驶证OCR */
  RpcOutput<DrivingLicenceResponse> drivingLicenceOcr(DrivingLicenceRequest input);

  /** 行驶证OCR */
  RpcOutput<DrivingPermitOcrResponse> drivingPermitOcr(DrivingPermitRequest input);

  /** 人脸无源库照比对 */
  RpcOutput<CompareWithoutSourceResponse> facePhotoCompareWithoutSource(
      CompareWithoutSourceRequest request);

  /** 人脸有源两照比对 */
  RpcOutput<CompareWithSourceResponse> facePhotoCompareWithSource(CompareWithSourceRequest request);


  /**
   * 企业全貌
   *
   * @see http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********
   */
  RpcOutput<OrgSummaryResponse> getOrgSummary(OrgSummaryRequest request);

  /** 实名结果 */
  AuthResultResponse authResult(AuthResultRequest request);

  /** 查询企业信息 */
  RpcOutput<OrgInfoResponse> getOrgInfo(AuthResultRequest request);
}
