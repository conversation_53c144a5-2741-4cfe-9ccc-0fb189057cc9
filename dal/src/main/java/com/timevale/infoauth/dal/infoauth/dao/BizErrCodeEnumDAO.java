package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.BizErrCodeEnumDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/** 信息比对业务错误码枚举 */
public interface BizErrCodeEnumDAO {

  @Select({
          "<script>",
          "SELECT `biz_code_id`, `code`, `msg` FROM `biz_err_code_enum` WHERE `biz_code_id` = #{codeId} LIMIT 1",
          "</script>",
  })
  BizErrCodeEnumDO getByBizCodeId(@Param("codeId") String codeId);
}
