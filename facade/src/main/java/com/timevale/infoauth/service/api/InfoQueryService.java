package com.timevale.infoauth.service.api;

import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.infoauth.service.model.SupportResult;
import com.timevale.infoauth.service.request.query.AccurateQueryEnterpriseInformationRequest;
import com.timevale.infoauth.service.request.query.AuthInfoRecordQuery;
import com.timevale.infoauth.service.request.query.AuthResultRequest;
import com.timevale.infoauth.service.request.query.ErrMsgRequest;
import com.timevale.infoauth.service.request.query.FuzzyQueryEnterpriseInformationRequest;
import com.timevale.infoauth.service.request.query.OrgAuthInfoRecordQuery;
import com.timevale.infoauth.service.request.query.PsnAuthCacheQueryRequest;
import com.timevale.infoauth.service.response.query.AccurateQueryEnterpriseInformationResponse;
import com.timevale.infoauth.service.response.query.ErrMsgResponse;
import com.timevale.infoauth.service.response.query.FuzzyQueryEnterpriseInformationResponse;
import com.timevale.infoauth.service.response.query.OrgAuthInfoQueryRecordResponse;
import com.timevale.infoauth.service.response.query.OrgInfoQueryResponse;
import com.timevale.infoauth.service.response.query.OrgInfoResponse;
import com.timevale.infoauth.service.response.query.PsnAuthCacheQueryResponse;
import com.timevale.infoauth.service.response.query.PsnInfoQueryResponse;
import com.timevale.infoauth.service.response.query.PsnInfoResponse;
import com.timevale.mandarin.common.annotation.RestClient;

/** @Author: Chen Weiyu @Description: @Date: Created in 2018/12/10 @Modified By: */
@RestClient(serviceId = "infoauth-service")
public interface InfoQueryService {

  /**
   * 查询个人认证信息
   *
   * @param request
   * @return
   */
  RpcOutput<PsnInfoResponse> queryPsnInfo(AuthResultRequest request);

  /**
   * 查询企业认证信息
   *
   * @param request
   * @return
   */
  RpcOutput<OrgInfoResponse> queryOrgInfo(AuthResultRequest request);

  /**
   * 查询个人认证信息
   *
   * @param request
   * @return
   */
  RpcOutput<PsnInfoQueryResponse> psnQueryInfoForRPC(AuthInfoRecordQuery request);

  /**
   * 查询企业认证信息
   *
   * @param request
   * @return
   */
  RpcOutput<OrgInfoQueryResponse> orgInfoQueryForRPC(AuthInfoRecordQuery request);


  /**
   * 查询企业信息比对最新一笔成功记录
   *
   * @param request
   * @return
   */
  SupportResult<OrgAuthInfoQueryRecordResponse> orgAuthLastSuccessInfoQuery(OrgAuthInfoRecordQuery request);

  /**
   * 查询错误原因信息
   *
   * @param request
   * @return
   */
  RpcOutput<ErrMsgResponse> queryErrMsg(ErrMsgRequest request);

  /**
   * 企业名称模糊查询
   *
   * @param request
   * @return
   */
  RpcOutput<FuzzyQueryEnterpriseInformationResponse> fuzzyQueryEnterpriseInformation(
      FuzzyQueryEnterpriseInformationRequest request);


  /**
   * 企业精确查询
   *
   * @param request
   * @return
   */
  RpcOutput<AccurateQueryEnterpriseInformationResponse> accurateQueryEnterpriseInformation(
      AccurateQueryEnterpriseInformationRequest request);

  /**
   * 个人实名认证成功类型缓存查询
   * @return 认证成功的缓存
   */
  RpcOutput<PsnAuthCacheQueryResponse> psnInfoCacheQueryForRPC(PsnAuthCacheQueryRequest request);

}
