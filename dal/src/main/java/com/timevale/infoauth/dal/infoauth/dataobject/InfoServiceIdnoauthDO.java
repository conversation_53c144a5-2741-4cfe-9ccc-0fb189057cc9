package com.timevale.infoauth.dal.infoauth.dataobject;

import java.util.Date;

/**
 * The table. info_service_idnoAuth
 *
 * <AUTHOR> Kunpeng
 */
public class InfoServiceIdnoauthDO extends InfoServiceAuthDO {

  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;
  /** id 主键. */
  private long id;
  /** idno 待检查身份证号码. */
  private String idno;
  /** name 待检查个人姓名. */
  private String name;
  /** infoauthId 信息比对服务ID. */
  private String infoauthId;
  /** providerId 企业查询服务提供商. */
  private String providerId;
  /** timestampId 时间戳. */
  private String timestampId;
  /** providerResult 服务提供商返回的代码. */
  private String providerResult;
  /** result 比对结果. */
  private Integer result;
  /** serviceStatus SERVICE_STATUS. */
  private Integer serviceStatus;
  private Long errId;

  /** Set createTime 记录创建时间. */
  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  /**
   * Get createTime 记录创建时间.
   *
   * @return the string
   */
  public Date getCreateTime() {
    return createTime;
  }

  /** Set modifyTime 记录修改时间. */
  public void setModifyTime(Date modifyTime) {
    this.modifyTime = modifyTime;
  }

  /**
   * Get modifyTime 记录修改时间.
   *
   * @return the string
   */
  public Date getModifyTime() {
    return modifyTime;
  }

  public long getId() {
    return id;
  }

  public void setId(long id) {
    this.id = id;
  }

  /** Set idno 待检查身份证号码. */
  public void setIdno(String idno) {
    this.idno = idno;
  }

  /**
   * Get idno 待检查身份证号码.
   *
   * @return the string
   */
  public String getIdno() {
    return idno;
  }

  /** Set name 待检查个人姓名. */
  public void setName(String name) {
    this.name = name;
  }

  /**
   * Get name 待检查个人姓名.
   *
   * @return the string
   */
  public String getName() {
    return name;
  }

  /** Set infoauthId 信息比对服务ID. */
  public void setInfoauthId(String infoauthId) {
    this.infoauthId = infoauthId;
  }

  /**
   * Get infoauthId 信息比对服务ID.
   *
   * @return the string
   */
  public String getInfoauthId() {
    return infoauthId;
  }

  /** Set providerId 企业查询服务提供商. */
  public void setProviderId(String providerId) {
    this.providerId = providerId;
  }

  /**
   * Get providerId 企业查询服务提供商.
   *
   * @return the string
   */
  public String getProviderId() {
    return providerId;
  }

  /** Set timestampId 时间戳. */
  public void setTimestampId(String timestampId) {
    this.timestampId = timestampId;
  }

  /**
   * Get timestampId 时间戳.
   *
   * @return the string
   */
  public String getTimestampId() {
    return timestampId;
  }

  /** Set providerResult 服务提供商返回的代码. */
  public void setProviderResult(String providerResult) {
    this.providerResult = providerResult;
  }

  /**
   * Get providerResult 服务提供商返回的代码.
   *
   * @return the string
   */
  public String getProviderResult() {
    return providerResult;
  }

  /** Set result 比对结果. */
  public void setResult(Integer result) {
    this.result = result;
  }

  /**
   * Get result 比对结果.
   *
   * @return the string
   */
  public Integer getResult() {
    return result;
  }

  /** Set serviceStatus SERVICE_STATUS. */
  public void setServiceStatus(Integer serviceStatus) {
    this.serviceStatus = serviceStatus;
  }

  /**
   * Get serviceStatus SERVICE_STATUS.
   *
   * @return the string
   */
  public Integer getServiceStatus() {
    return serviceStatus;
  }

  public Long getErrId() {
    return errId;
  }

  public void setErrId(Long errId) {
    this.errId = errId;
  }
}
