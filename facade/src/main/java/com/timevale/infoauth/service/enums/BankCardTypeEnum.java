package com.timevale.infoauth.service.enums;

import com.timevale.mandarin.base.util.StringUtils;

/** @Author: <PERSON> @Description: @Date: Created in 2019/8/19 @Modified By: */
public enum BankCardTypeEnum {

  /** 不能识别 */
  UNKNOWN(0, "不能识别", "不能识别"),

  /** 借记卡 */
  DEBIT_CARD(1, "借记卡", "借记卡"),

  /** 信用卡 */
  CREDIT_CARD(2, "信用卡", "信用卡"),
  ;

  private int value;

  private String strValue;

  private String desc;

  BankCardTypeEnum(int value, String strValue, String desc) {
    this.value = value;
    this.strValue = strValue;
    this.desc = desc;
  }

  public int getValue() {
    return value;
  }

  public String getStrValue() {
    return strValue;
  }

  public String getDesc() {
    return desc;
  }

  public static BankCardTypeEnum byIntValue(Integer value) {

    if (null == value) {
      return null;
    }

    for (BankCardTypeEnum bankCardType : BankCardTypeEnum.values()) {
      if (bankCardType.value == value) {
        return bankCardType;
      }
    }

    return null;
  }

  public static BankCardTypeEnum getByStrValue(String strValue) {
    if (StringUtils.isBlank(strValue)) return UNKNOWN;
    if (StringUtils.equals(strValue, "贷记卡")) return CREDIT_CARD;
    for (BankCardTypeEnum bankCardType : BankCardTypeEnum.values())
      if (StringUtils.equals(strValue, bankCardType.strValue)) return bankCardType;
    return UNKNOWN;
  }
}
