package com.timevale.infoauth.service.api;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.dal.infoauth.dataobject.InfoProviderDomainDO;
import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceOrgcheckDO;
import com.timevale.infoauth.model.enums.OrganSelectTypeEnum;
import com.timevale.infoauth.model.output.OrgInfoAuthOutput;
import com.timevale.infoauth.service.HeaderUtils;
import com.timevale.infoauth.service.enums.EntStatus;
import com.timevale.infoauth.service.enums.InfoAuthServiceType;
import com.timevale.infoauth.service.enums.Nation;
import com.timevale.infoauth.service.enums.ProvResult;
import com.timevale.infoauth.service.enums.UserCenterCertTypeEnum;
import com.timevale.infoauth.service.impl.biz.checker.OrgAuthResultChecker;
import com.timevale.infoauth.service.impl.biz.common.cache.EnterpriseRegistrationNo3CacheHandler;
import com.timevale.infoauth.service.impl.boost.MonitorAlertResultSupport;
import com.timevale.infoauth.service.impl.common.dto.AbstractProviderInput;
import com.timevale.infoauth.service.impl.common.dto.OrgProvout;
import com.timevale.infoauth.service.impl.common.dto.OrganProvin;
import com.timevale.infoauth.service.impl.common.dto.Provout;
import com.timevale.infoauth.service.impl.constants.FeeConstant;
import com.timevale.infoauth.service.impl.constants.ProviderEnum;
import com.timevale.infoauth.service.impl.dto.BaiduOrgLicenseOcrProvout;
import com.timevale.infoauth.service.impl.dto.BankauthProvin;
import com.timevale.infoauth.service.impl.dto.HuaweiOrgLicenceOcrProvout;
import com.timevale.infoauth.service.impl.dto.PersonProvin;
import com.timevale.infoauth.service.impl.dto.QiChaChaFuzzySearchProvout;
import com.timevale.infoauth.service.impl.provider.biz.adapter.bankfour.BankAuthUnionPayInternational;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.Psn2Adaptors;
import com.timevale.infoauth.service.impl.provider.client.baidu.BaiduOrgLicenseOcrClient;
import com.timevale.infoauth.service.impl.provider.client.huawei.HuaweiOrgLicenceOcrClient;
import com.timevale.infoauth.service.impl.provider.client.qichacha.QiChaChaClient;
import com.timevale.infoauth.service.impl.provider.client.shucifang.ShuCiFangClient;
import com.timevale.infoauth.service.impl.provider.client.shucifang.ShuCiFangConstant;
import com.timevale.infoauth.service.impl.provider.client.shucifang.domain.ShuCiFangFourElementResponse;
import com.timevale.infoauth.service.impl.provider.constants.IdCard2ProviderEnum;
import com.timevale.infoauth.service.impl.utils.UUIDUtil;
import com.timevale.infoauth.service.request.*;
import com.timevale.infoauth.service.request.query.AuthResultRequest;
import com.timevale.infoauth.service.response.AuthResultResponse;
import com.timevale.infoauth.service.response.BankCardOcrResponse;
import com.timevale.infoauth.service.response.BankFourResponse;
import com.timevale.infoauth.service.response.BankThreeResponse;
import com.timevale.infoauth.service.response.CompareWithSourceResponse;
import com.timevale.infoauth.service.response.CompareWithoutSourceResponse;
import com.timevale.infoauth.service.response.ForeignPermanentOcrResponse;
import com.timevale.infoauth.service.response.GeneralCharactersOcrResponse;
import com.timevale.infoauth.service.response.IdnoAuthResponse;
import com.timevale.infoauth.service.response.OcrResponse;
import com.timevale.infoauth.service.response.OrgInfoAuthResponse;
import com.timevale.infoauth.service.response.OrgLicenseOcrResponse;
import com.timevale.infoauth.service.response.OrganTwoResponse;
import com.timevale.infoauth.service.response.PsnBankFourDetailResponse;
import com.timevale.infoauth.service.response.PsnBankThreeDetailResponse;
import com.timevale.infoauth.service.response.PsnTelecomDetailResponse;
import com.timevale.infoauth.service.response.TelecomDetailResponse;
import com.timevale.infoauth.service.response.TelecomResponse;
import com.timevale.infoauth.service.response.VehicleRegCertOcrResponse;
import com.timevale.infoauth.utils.ResourceUtils;
import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.mandarin.common.idgenerator.GlobalIdGenerator;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import esign.utils.IOUtil;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Ignore;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@JsonDataIgnore
public class InfoauthServiceTest extends ApplicationTest {

  @Resource InfoauthService infoauthService;

  @Resource BankAuthUnionPayInternational bankAuthUnionPayInternational;


    @Autowired
    private EnterpriseRegistrationNo3CacheHandler enterpriseRegistrationNo3CacheHandler;

  @Test
  public void testenterpriseRegistrationNo3CacheHandler() {
    final String generateUUID = UUIDUtil.generateUUID();
    OrganProvin input = new OrganProvin();
    input.setName(generateUUID);
    input.setOrgName(generateUUID);
    input.setOrgCertNo(generateUUID);
    input.setNameType(0);
    input.setLegalRepName(generateUUID);
    input.setLegalRepCertNo(generateUUID);
    input.setLegalRepCertType(0);
    input.setOrganSelectType(OrganSelectTypeEnum.ByCertNo);
    input.setCodeORG(generateUUID);
    input.setCodeUSC(generateUUID);
    input.setCodeREG(generateUUID);
    input.setServiceType(InfoAuthServiceType.ENTERPRISE_3_ELEMENT);
    input.setBizAppId(generateUUID);
    input.setBizId(generateUUID);
    input.setTransactionId(generateUUID);
    input.setName(generateUUID);
    input.setCertNumber(generateUUID);
    input.setUserCenterCertType(0);
    input.setProviderName(generateUUID);
    input.setConfirmProvider(false);
    input.setShuntTag(false);
    input.setAvailableProviderList(Lists.newArrayList());

    final Provout provout = new Provout();
    provout.setProvider(ProviderEnum.QICHACHA);
    provout.setProvResult(ProvResult.SUCCESS);
    provout.setProviderErrorMessage(generateUUID);
    provout.setProviderResultString(generateUUID);

    enterpriseRegistrationNo3CacheHandler.setCache(input, provout);

    Assert.assertNotNull(input);
  }
  @Test
  public void bankfourTest() {
    HeaderUtils.setHeader();

    BankFourRequest request =
        BankFourRequest.builder()
            .name("高美君")
            .idno("14273019911003072X")
            .mobile("***********")
            .cardno("6222021202046493541")
            .build();

    BankFourResponse response = infoauthService.psnBankFour(request);

    Assert.assertFalse(response.isPass());
    Assert.assertEquals(request.getIdno(), "14273019911003072X");

    request =
            BankFourRequest.builder()
                    .name("高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君君高美君高美君高美君高美君高美君君高美君高美君高美君高美君高美君")
                    .idno("14273019911003072X")
                    .mobile("***********")
                    .cardno("6222021202046493541")
                    .build();

    try{
      infoauthService.psnBankFour(request);
      Assert.assertTrue(Boolean.FALSE);
    } catch (BaseBizRuntimeException e){
      Assert.assertTrue(e.getMessage().contains("长度超过"));
    }catch (Exception e){
      Assert.assertTrue(Boolean.FALSE);
    }
  }

  @Test
  public void bankthreeTest() {
    HeaderUtils.setHeader();

    BankThreeRequest request =
        BankThreeRequest.builder()
            .name("高美君")
            .idno("14273019911003072X")
            .cardno("6222021202046493541")
            .build();

    BankThreeResponse response = infoauthService.bankThree(request);

    Assert.assertFalse(response.isPass());
    Assert.assertEquals(request.getIdno(), "14273019911003072X");

    try{
      request =
              BankThreeRequest.builder()
                      .name("高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君君高美君高美君高美君高美君高美君君高美君高美君高美君高美君高美君")
                      .idno("14273019911003072X")
                      .cardno("6222021202046493541")
                      .build();
      infoauthService.bankThree(request);
      Assert.assertTrue(Boolean.FALSE);
    } catch (BaseBizRuntimeException e){
      Assert.assertTrue(e.getMessage().contains("长度超过"));
    }catch (Exception e){
      Assert.assertTrue(Boolean.FALSE);
    }
  }

  @Test
  public void telecomTest() {
    HeaderUtils.setHeader();

    TelecomRequest request =
        TelecomRequest.builder()
            .name("高美君")
            .idno("14273019911003072X")
            .mobile("***********")
            .build();

    TelecomResponse response = infoauthService.telecomAuth(request);

    Assert.assertFalse(response.isPass());
    Assert.assertEquals(request.getIdno(), "14273019911003072X");

    try{
      request =
              TelecomRequest.builder()
                      .name("高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君君高美君高美君高美君高美君高美君君高美君高美君高美君高美君高美君")
                      .idno("14273019911003072X")
                      .mobile("***********")
                      .build();
      infoauthService.telecomAuth(request);
      Assert.assertTrue(Boolean.FALSE);
    } catch (BaseBizRuntimeException e){
      Assert.assertTrue(e.getMessage().contains("长度超过"));
    }catch (Exception e){
      Assert.assertTrue(Boolean.FALSE);
    }
  }

  /** 三要素测试 */
  @Test
  public void telecomSingleTest() {
    HeaderUtils.setHeader();

    TelecomRequest request =
        TelecomRequest.builder()
            .name("武玉华")
            .idno("******************")
            .mobile("***********")
            .build();

    TelecomResponse response = infoauthService.telecomAuth(request);

    Assert.assertTrue(response.isPass());
    Assert.assertEquals(request.getIdno(), "******************");
  }

  @Test
  public void psnTwoTest() {
    HeaderUtils.setHeader();

    IdnoAuthRequest request =
        IdnoAuthRequest.builder().name("高美君").idno("14273019911003072X").build();

    IdnoAuthResponse response = infoauthService.idnoAuth(request);

    Assert.assertTrue(response.isPass());
    Assert.assertEquals(request.getIdno(), "14273019911003072X");

    try{
      request =
              IdnoAuthRequest.builder()
                      .name("高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君高美君君高美君高美君高美君高美君高美君君高美君高美君高美君高美君高美君")
                      .idno("14273019911003072X")
                      .build();
      infoauthService.idnoAuth(request);
      Assert.assertTrue(Boolean.FALSE);
    } catch (BaseBizRuntimeException e){
      Assert.assertTrue(e.getMessage().contains("长度超过"));
    }catch (Exception e){
      Assert.assertTrue(Boolean.FALSE);
    }
  }

  @Test
  public void psnNameMobileTwoTest() {
    HeaderUtils.setHeader();

    NameMobileRequest request =
        NameMobileRequest.builder().name("陈旭博").mobile("18637847399").build();

    IdnoAuthResponse response = infoauthService.psnNameMobileAuth(request);

    NameMobileRequest request1 =
        NameMobileRequest.builder().name("陈旭").mobile("18637847399").build();

    IdnoAuthResponse response1 = infoauthService.psnNameMobileAuth(request1);

    Assert.assertNotNull(response);
    Assert.assertTrue(response.isPass());
  }

  @Test
  public void psnTwoMayiTest() {
    HeaderUtils.setHeader();


    AbstractProviderInput input = new PersonProvin();
    input.setName("高美君");
    input.setCertNumber("14273019911003072X");
    input.setProviderName(IdCard2ProviderEnum.MAYI.dbKey());
    input.setServiceType(InfoAuthServiceType.IDCARD_2_ELEMENT);
    Psn2Adaptors psn2Adaptors = new Psn2Adaptors();
    Provout provout = psn2Adaptors.MAYI_identityauthValid.apply(input);


    Assert.assertTrue(provout.getProvResult().isResult());
    Assert.assertEquals(provout.getFee(), FeeConstant.FEE);
  }

  @Test
  public void orgInfoAuthTest() {
    HeaderUtils.setHeader();

    OrgInfoAuthRequest request =
        OrgInfoAuthRequest.builder()
            .name("北京百乐文化传媒有限公司")
            .codeUSC("91110105306395818U")
            .legalName("贾跃亭")
            .legalCertNo("14262319731215081X")
            .legalArea(Nation.CH_MAINLAND)
            .build();
    request.setBizAppId("**********");
    OrgInfoAuthResponse response = infoauthService.orgInfoAuth(request);
    Assert.assertTrue(response.isPass());
    Assert.assertEquals(request.getCodeUSC(), "91110105306395818U");
  }

  @Test
  public void orgInfoFourTest() {
    HeaderUtils.setHeader();

    OrgInfoAuthRequest request =
        OrgInfoAuthRequest.builder()
            .name("北京百乐文化传媒有限公司")
            .codeUSC("91110105306395818U")
            .legalName("贾跃亭")
            .legalCertNo("14262319731215081X")
            .legalArea(Nation.CH_MAINLAND)
            .build();
    //        request.setBizAppId(HeaderUtils.APP_ID);
    OrgInfoAuthResponse response = infoauthService.orgInfoFour(request);

    Assert.assertTrue(response.isPass());
    Assert.assertEquals(request.getCodeUSC(), "91110105306395818U");
  }

  @Test
  public void orgInfoAuthTestshucifang() {
    HeaderUtils.setHeader();

    OrgInfoAuthRequest request =
            OrgInfoAuthRequest.builder()
                    .name("北京百乐文化传媒有限公司")
                    .codeUSC("91110105306395818U")
                    .legalName("贾跃亭")
                    .legalCertNo("14262319731215081X")
                    .legalArea(Nation.CH_MAINLAND)
                    .build();
    request.setBizAppId("**********");
    OrgInfoAuthResponse response = infoauthService.orgInfoAuth(request);
    Assert.assertTrue(response.isPass());
    Assert.assertEquals(request.getCodeUSC(), "91110105306395818U");
  }
  @Test
  public void orgInfoFourTestshucifang() {
    HeaderUtils.setHeader();

    OrgInfoAuthRequest request =
            OrgInfoAuthRequest.builder()
                    .name("北京百乐文化传媒有限公司")
                    .codeUSC("91110105306395818U")
                    .legalName("贾跃亭")
                    .legalCertNo("14262319731215081X")
                    .legalArea(Nation.CH_MAINLAND)
                    .build();
            request.setBizAppId("**********");
    OrgInfoAuthResponse response = infoauthService.orgInfoFour(request);

    Assert.assertTrue(response.isPass());
    Assert.assertEquals(request.getCodeUSC(), "91110105306395818U");
  }

  @Test
  public void orgInfnshucifangStaticHandleBizDataFour() {
    Provout provout = new Provout();

    ShuCiFangFourElementResponse shuCiFangFourElementResponse = new ShuCiFangFourElementResponse();
    ShuCiFangFourElementResponse.ShuCiFangFourElementDataResponse data = new ShuCiFangFourElementResponse.ShuCiFangFourElementDataResponse();

    //1
    ShuCiFangClient.handleBizDataFour(provout, null);

    //2
    ShuCiFangClient.handleBizDataFour(provout, shuCiFangFourElementResponse);

    //3
    shuCiFangFourElementResponse.setCode(ShuCiFangConstant.CODE_ORG4_UNABLE_TO_VERIFY);
    shuCiFangFourElementResponse.setData(data);
    ShuCiFangClient.handleBizDataFour(provout, shuCiFangFourElementResponse);
    data.setReasonCode(ShuCiFangConstant.REASON_CODE_1);
    ShuCiFangClient.handleBizDataFour(provout, shuCiFangFourElementResponse);
    data.setReasonCode(ShuCiFangConstant.REASON_CODE_2);
    ShuCiFangClient.handleBizDataFour(provout, shuCiFangFourElementResponse);

    //3
    shuCiFangFourElementResponse.setCode(ShuCiFangConstant.CODE_SUCCESS);
    shuCiFangFourElementResponse.setData(data);
    //4
    data.setIsAllMatch(ShuCiFangConstant.Match_SUCCESS);
    ShuCiFangClient.handleBizDataFour(provout, shuCiFangFourElementResponse);
    Assert.assertNotNull(provout);
  }

//  @Test
  public void orgInfoFourWithoutNameTest() {
    HeaderUtils.setHeader();

    OrgInfoAuthRequest request =
        OrgInfoAuthRequest.builder()
            .name("*")
            .codeUSC("92500113MA5YWNRG7D")
            .legalName("李华兵")
            .legalCertNo("500381198403221211")
            .legalArea(Nation.CH_MAINLAND)
            .build();
    OrgInfoAuthResponse response = infoauthService.orgInfoFour(request);

    Assert.assertFalse(response.isPass());
//    Assert.assertEquals(request.getCodeUSC(), "92500113MA5YWNRG7D");
  }

  // 企业二要素
  @Test
  public void orgInfoTwoTest() {
    HeaderUtils.setHeader();

    OrganTwoRequest request = new OrganTwoRequest();
    request.setName("北京百乐文化传媒有限公司");
    request.setCodeUSC("91110105306395818U");

    OrganTwoResponse response = infoauthService.organTwo(request).getData();

    Assert.assertTrue(response.isPass());
    Assert.assertEquals(request.getCodeUSC(), "91110105306395818U");
  }

  @Test
  public void personCheckTest() {
    HeaderUtils.setHeader();

    IdnoAuthRequest request =
        IdnoAuthRequest.builder().name("贾跃亭").idno("14262319731215081X").build();

    Assert.assertTrue(infoauthService.idnoAuth(request).isPass());
    Assert.assertEquals(request.getIdno(), "14262319731215081X");
  }

  @Test
  public void personCheckNotMatchTest() {
    HeaderUtils.setHeader();

    IdnoAuthRequest request =
        IdnoAuthRequest.builder().name("李佳").idno("411082199702149049").build();

    Assert.assertFalse(infoauthService.idnoAuth(request).isPass());
    Assert.assertEquals(request.getIdno(), "411082199702149049");
  }

  // 律所测试
  @Test
  public void lawFirmTest() {
    HeaderUtils.setHeader();

    OrgInfoAuthRequest request =
        OrgInfoAuthRequest.builder()
            .name("山东海乐普律师事务所")
            .codeUSC("31370000693763173G")
            .legalName("李乐国")
            .build();

    Assert.assertTrue(infoauthService.lawFirmByName(request).isPass());
    Assert.assertEquals(request.getCodeUSC(), "31370000693763173G");
  }

  @Test
  public void qiChaChaByCardNo() {
    HeaderUtils.setHeader();

    OrgInfoAuthRequest request =
        OrgInfoAuthRequest.builder()
            .name("江北区妙凌洋小吃馆")
            .codeUSC("92500105MA61CEGM49")
            .legalName("龙洋")
            .build();
    final OrgInfoAuthResponse orgInfoAuthResponse = infoauthService.registrationNoThree(request);
    Assert.assertNotNull(orgInfoAuthResponse);

    OrgInfoAuthRequest request1 =
        OrgInfoAuthRequest.builder()
            .name("杭州天谷信息科技有限公司")
            .codeUSC("913301087458306077")
            .legalName("金宏洲")
            .build();
    final OrgInfoAuthResponse orgInfoAuthResponse1 = infoauthService.registrationNoThree(request1);
    Assert.assertNotNull(orgInfoAuthResponse1);

    request1 =
            OrgInfoAuthRequest.builder()
                    .name("杭州司" + UUIDUtil.generateUUID())
                    .codeUSC("91110105306395818U")
                    .legalName("金宏洲")
                    .build();
    final OrgInfoAuthResponse orgInfoAuthResponse2 = infoauthService.registrationNoThree(request1);
    Assert.assertNotNull(orgInfoAuthResponse2);
  }

  @Test
  public void lawFirmTwoTest() {
    HeaderUtils.setHeader();
    LawFirmTwoRequest request = new LawFirmTwoRequest();
    request.setName("浙江浙临律师事务所");
    request.setCodeUSC("31330000725275079F");
    Assert.assertTrue(infoauthService.lawFirmTwo(request).isPass());
    Assert.assertEquals(request.getCodeUSC(), "31330000725275079F");
  }

  // 社会组织测试
  @Test
  public void socialTest() {
    HeaderUtils.setHeader();

    OrgInfoAuthRequest request =
        OrgInfoAuthRequest.builder()
            .name("潇湘晨报社")
            .codeUSC("1243000044880463XP")
            .legalName("伍洪涛")
            .build();

    Assert.assertTrue(infoauthService.socialOrganizationByName(request).isPass());
    Assert.assertEquals(request.getCodeUSC(), "1243000044880463XP");
  }

  @Test
  public void socialOrganTwoTest() {
    HeaderUtils.setHeader();
    SocialOrganTwoRequest request = new SocialOrganTwoRequest();
    request.setName("北京理工大学教育基金会");
    request.setCodeUSC("53100000500021676K");
    Assert.assertTrue(infoauthService.socialOrganTwo(request).isPass());
    Assert.assertEquals(request.getCodeUSC(), "53100000500021676K");
  }

  // 个人身份证识别
  @Test
  public void ocrTest() {
    HeaderUtils.setHeader();

    OcrRequest request = new OcrRequest();
    request.setIdnoFaceImgKey("$5fa803b9-c32f-43b6-bb20-282e7476b995$1272663297");
    request.setIdnoEmblemImgKey("KeyIdentFace_qpqyAXk4Vd5qeyvsz2ItyGeP3fGhBSzK");

    Assert.assertNotNull(infoauthService.ocr(request));
    Assert.assertEquals(
        request.getIdnoFaceImgKey(), "$5fa803b9-c32f-43b6-bb20-282e7476b995$1272663297");
  }

  // 外国人永久居住证OCR
  @Test
  @Ignore
  public void foreignPermanentOcrTest() {
    HeaderUtils.setHeader ();

    ForeignPermanentOcrRequest request = new ForeignPermanentOcrRequest();
    request.setBizAppId("3438757422");
    request.setImgKey("$acba548d-734a-4d26-b701-f55540dc4616$2695367453");

    ForeignPermanentOcrResponse response = infoauthService.foreignPermanentOcr(request).getData();
    Assert.assertNotNull(response);
//    Assert.assertEquals(response.getName(),"");
    Assert.assertEquals(response.getBirthDay(),"1981年08月03日");
  }

  @Test
  @Ignore
  public void generalCharactersOcrTest1() {
    HeaderUtils.setHeader();

    GeneralCharactersOcrRequest request = new GeneralCharactersOcrRequest();
    request.setBizAppId("3438757422");
    request.setImgKey("$23d520da-d916-444d-866e-dcb65fe75cc3$4288884231");

    Assert.assertThrows(() -> infoauthService.generalCharactersOcr(request));
  }

  @Test
  @Ignore
  public void generalCharactersOcrTest2() {
    HeaderUtils.setHeader();

    GeneralCharactersOcrRequest request = new GeneralCharactersOcrRequest();
    request.setBizAppId("3438757422");
    request.setImgKey("$e2439ed0-0519-4eb0-9f91-62357e6bc74a$3461126911");

    GeneralCharactersOcrResponse response = infoauthService.generalCharactersOcr(request).getData();
    Assert.assertNotNull(response);
    Assert.assertNotNull(response.getAngle());
  }

  @Test
  @Ignore
  public void vehicleRegCertOcrTest1() {
    HeaderUtils.setHeader();

    VehicleRegCertOcrRequest request = new VehicleRegCertOcrRequest();
    request.setBizAppId("3438757422");
    request.setImgKey("$23d520da-d916-444d-866e-dcb65fe75cc3$4288884231");

    Assert.assertThrows(() -> infoauthService.vehicleRegCertOcr(request));
  }

  @Test
  @Ignore
  public void vehicleRegCertOcrTest2() {
    HeaderUtils.setHeader();

    VehicleRegCertOcrRequest request = new VehicleRegCertOcrRequest();
    request.setBizAppId("3438757422");
    request.setImgKey("$c56ab0a1-0b69-4bdf-8af6-3296708eaa6e$999193961");

    VehicleRegCertOcrResponse response = infoauthService.vehicleRegCertOcr(request).getData();
    Assert.assertNotNull(response);
    Assert.assertNotNull(response.getTransferRegistration());
  }

  // 查询接口单测
  @Test
  public void authResultTest() {
    HeaderUtils.setHeader();

    AuthResultRequest request = new AuthResultRequest();
    request.setInfoAuthId("f02aeb33-afee-4b3b-883a-ff231d2f84d2");
    AuthResultResponse result = infoauthService.authResult(request);
    Assert.assertNotNull(result);
    Assert.assertEquals(result.getServiceType(), InfoAuthServiceType.ENTERPRISE_3_ELEMENT.name());
  }

  @Test
  public void psnTwoNotPassTest() {
    HeaderUtils.setHeader();
    IdnoAuthRequest request =
        IdnoAuthRequest.builder().name("高美").idno("14273019911003072X").build();
    IdnoAuthResponse response = infoauthService.idnoAuth(request);
    Assert.assertNotNull(response);
    Assert.assertFalse(response.isPass());
  }

  @Test
  public void psnTwoNotPassTestRisk() {
    Map<String, Object> contextMap = new HashMap<>();
    contextMap.put("X-Tsign-Open-App-Id", "7876663471");
    contextMap.put("trace-id", GlobalIdGenerator.generate().toString());
    RequestContext.setContextMap(contextMap);
    IdnoAuthRequest request =
            IdnoAuthRequest.builder().name("高美").idno("14273019911003072X").build();
    request.setBizAppId("7876663471");
    try {
      IdnoAuthResponse response = infoauthService.idnoAuth(request);
    } catch (Exception e) {

    }

    Assert.assertNotNull(contextMap);

  }


//  @Test
  public void orgInfoAuthNotPassTest() {
    HeaderUtils.setHeader();
    try {
      OrgInfoAuthRequest request =
          OrgInfoAuthRequest.builder()
              .name(getName("北京百乐文化传媒有限公司"))
              .codeUSC("91110105306395818U")
              .legalName("贾跃亭")
              .legalCertNo("14262319731215081X")
              .legalArea(Nation.CH_MAINLAND)
              .build();
      request.setBizAppId(HeaderUtils.APP_ID);
      OrgInfoAuthResponse result = infoauthService.orgInfoAuth(request);
      Assert.assertNotNull(result);
      Assert.assertFalse(result.isPass());
    } catch (BaseBizRuntimeException e) {

    }
  }

//  @Test
  public void orgInfoFourNotPassTest() {
    HeaderUtils.setHeader();
    OrgInfoAuthRequest request =
        OrgInfoAuthRequest.builder()
            .name(getName("北京百乐文化传媒有限公司"))
            .codeUSC("91110105306395818U")
            .legalName("贾跃亭")
            .legalCertNo("14262319731215081X")
            .legalArea(Nation.CH_MAINLAND)
            .build();
    OrgInfoAuthResponse response = infoauthService.orgInfoFour(request);
    Assert.assertNotNull(response);
    Assert.assertFalse(response.isPass());
  }

  @Test
  public void orgInfoTwoNotPassTest() {
    HeaderUtils.setHeader();
    try {
      OrganTwoRequest request = new OrganTwoRequest();
      request.setName(getName("北京百乐文化传媒有限公司"));
      request.setCodeUSC("91110105306395818U");
      RpcOutput<OrganTwoResponse> result = infoauthService.organTwo(request);
      Assert.assertNotNull(result);
      Assert.assertFalse(result.getData().isPass());
    } catch (BaseBizRuntimeException e) {

    }
  }

  @Test
  public void bankfourInternationalTest() {
    HeaderUtils.setHeader();
    try {
      BankFourRequest request =
          BankFourRequest.builder()
              .name("张三")
              .idno("M02120434")
              .mobile("***********")
              .cardno("6217858000078814644")
              .userCenterCertType(UserCenterCertTypeEnum.CRED_PSN_PASSPORT.dbValue())
              .build();
      BankFourResponse result = infoauthService.psnBankFour(request);
      Assert.assertNotNull(result);
      Assert.assertNotNull(result.getInfoauthId());
    } catch (Exception e) {

    }
  }

  @Test
  public void bankfourInternationalTest2() {
    HeaderUtils.setHeader();
    try {
      BankFourRequest request =
          BankFourRequest.builder()
              .name("LEE BENJAMIN MICHAEL")
              .idno("*********")
              .mobile("***********")
              .cardno("****************")
              .userCenterCertType(UserCenterCertTypeEnum.CRED_PSN_PASSPORT.dbValue())
              .build();
      BankFourResponse result = infoauthService.psnBankFour(request);
      Assert.assertNotNull(result);
      Assert.assertNotNull(result.getInfoauthId());
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  public void bankfourInternationalTest3() {
    HeaderUtils.setHeader();
    try {
      BankFourRequest request =
          BankFourRequest.builder()
              .name("杨申")
              .idno("********")
              .mobile("***********")
              .cardno("6212260200075667689")
              .userCenterCertType(UserCenterCertTypeEnum.CRED_PSN_CH_TWCARD.dbValue())
              .build();
      BankFourResponse result = infoauthService.psnBankFour(request);
      Assert.assertNotNull(result);
      Assert.assertNotNull(result.getInfoauthId());
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  public void bankfourInternationalCHHONGKONGTest() {
    HeaderUtils.setHeader();

    BankFourRequest request =
        BankFourRequest.builder()
            .name("张三")
            .idno("M02120434")
            .mobile("***********")
            .cardno("6217858000078814644")
            .userCenterCertType(UserCenterCertTypeEnum.CRED_PSN_CH_HONGKONG.dbValue())
            .build();

    BankFourResponse result = infoauthService.psnBankFour(request);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getInfoauthId());
  }

  @Test
  public void testisValidEnterpriseInfo() {
    final QiChaChaFuzzySearchProvout.Result result = new QiChaChaFuzzySearchProvout.Result();
    QiChaChaClient.isValidEnterpriseInfo(result, new OrganProvin(), false);
    Assert.assertNotNull(result);
  }

  @Test
  public void bankfourInternationalCHTWCARDTest() {
    HeaderUtils.setHeader();
    try {
      BankFourRequest request =
          BankFourRequest.builder()
              .name("林奕嘉")
              .idno("**********")
              .mobile("***********")
              .cardno("6217858000078814644")
              .userCenterCertType(UserCenterCertTypeEnum.CRED_PSN_CH_TWCARD.dbValue())
              .build();
      BankFourResponse result = infoauthService.psnBankFour(request);
      Assert.assertNotNull(result);
      Assert.assertNotNull(result.getInfoauthId());
    } catch (Exception e) {

    }
  }

  @Test
  public void bankfourInternationalCHMACAOTest() {
    HeaderUtils.setHeader();

    BankFourRequest request =
        BankFourRequest.builder()
            .name("张三")
            .idno("M02120434")
            .mobile("***********")
            .cardno("6217858000078814644")
            .userCenterCertType(UserCenterCertTypeEnum.CRED_PSN_CH_MACAO.dbValue())
            .build();

    BankFourResponse result = infoauthService.psnBankFour(request);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getInfoauthId());
  }

  @Test
  public void bankfourInternationalCheckTest() {
    HeaderUtils.setHeader();
    try {
      BankFourRequest request =
          BankFourRequest.builder()
              .name("张三")
              .idno("M02120434")
              .mobile("***********")
              .cardno("6217858000078814644")
              .userCenterCertType(UserCenterCertTypeEnum.CRED_PSN_CH_TWCARD.dbValue())
              .build();
      BankFourResponse response = infoauthService.psnBankFour(request);
      Assert.assertFalse(response.isPass());
      request.setUserCenterCertType(UserCenterCertTypeEnum.CRED_PSN_CH_MACAO.dbValue());
      response = infoauthService.psnBankFour(request);
      Assert.assertFalse(response.isPass());
      request.setUserCenterCertType(UserCenterCertTypeEnum.CRED_PSN_CH_HONGKONG.dbValue());
      response = infoauthService.psnBankFour(request);
      Assert.assertFalse(response.isPass());
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  public void orgInfoInternationalCheckIDNoTest() {
    HeaderUtils.setHeader();
    try {
      OrgInfoAuthRequest request =
          OrgInfoAuthRequest.builder()
              .name("杭州天谷信息有限")
              .codeUSC("913301087458306077")
              .legalCertNo("***********")
              .legalName("何一兵")
              .legalCertType(UserCenterCertTypeEnum.CRED_PSN_CH_IDCARD.dbValue())
              .build();
      OrgInfoAuthResponse response = infoauthService.orgInfoFour(request);
      Assert.assertNotNull(response);
      Assert.assertFalse(response.isPass());
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  public void orgInfoInternationalCheckCH_MACAOTest() {
    HeaderUtils.setHeader();
    try {
      OrgInfoAuthRequest request =
          OrgInfoAuthRequest.builder()
              .name("杭州天谷信息有限")
              .codeUSC("913301087458306077")
              .legalCertNo("***********")
              .legalName("何一兵")
              .build();
      request.setLegalCertType(UserCenterCertTypeEnum.CRED_PSN_CH_MACAO.dbValue());
      OrgInfoAuthResponse response = infoauthService.orgInfoFour(request);
      Assert.assertNotNull(response);
      Assert.assertFalse(response.isPass());
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  public void orgInfoInternationalCheckCH_HONGKONGTest() {
    HeaderUtils.setHeader();
    try {
      OrgInfoAuthRequest request =
          OrgInfoAuthRequest.builder()
              .name("杭州天谷信息有限")
              .codeUSC("913301087458306077")
              .legalCertNo("***********")
              .legalName("何一兵")
              .legalCertType(UserCenterCertTypeEnum.CRED_PSN_CH_HONGKONG.dbValue())
              .build();
      OrgInfoAuthResponse response = infoauthService.orgInfoFour(request);
      Assert.assertNotNull(response);
      Assert.assertFalse(response.isPass());
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  public void orgInfoInternationalCheckCH_TWCARDTest() {
    HeaderUtils.setHeader();
    try {
      OrgInfoAuthRequest request =
          OrgInfoAuthRequest.builder()
              .name("杭州天谷信息有限")
              .codeUSC("913301087458306077")
              .legalCertNo("***********")
              .legalName("何一兵")
              .legalCertType(UserCenterCertTypeEnum.CRED_PSN_CH_TWCARD.dbValue())
              .build();
      OrgInfoAuthResponse response = infoauthService.orgInfoFour(request);
      Assert.assertNotNull(response);
      Assert.assertFalse(response.isPass());
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  public void orgInfoInternationalCheckPASSPORTTest() {
    HeaderUtils.setHeader();
    try {
      OrgInfoAuthRequest request =
          OrgInfoAuthRequest.builder()
              .name("杭州天谷信息有限")
              .codeUSC("913301087458306077")
              .legalCertNo("***********")
              .legalName("何一兵")
              .legalCertType(UserCenterCertTypeEnum.CRED_PSN_PASSPORT.dbValue())
              .build();
      OrgInfoAuthResponse response = infoauthService.orgInfoFour(request);
      Assert.assertNotNull(response);
//      Assert.assertFalse(response.isPass());
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  public void internationalQueryErrorTest2() {
    try {
      BankauthProvin input = new BankauthProvin();
      input.setName("张三");
      input.setCertNumber("M02120434");
      input.setMobile("***********");
      input.setCardno("6217858000078814644");
      input.setUserCenterCertType(UserCenterCertTypeEnum.CRED_PSN_PASSPORT.dbValue());
      input.setProviderName("UNIONPAY_INTERNATIONAL");
      Provout result = bankAuthUnionPayInternational.internationalQuery(input);
      Assert.assertNotNull(result);
      Assert.assertNotNull(result.getInfoauthId());
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  @Ignore
  public void ocrPass() {
    OcrRequest request = new OcrRequest();
    request.setIdnoFaceImgKey("$aac87d67-a915-4061-80b7-bef4861539df$**********");
    request.setIdnoEmblemImgKey("$5458ad07-d900-4a73-a48d-9f5f4d88c1dc$**********");
    RpcOutput<OcrResponse> result = infoauthService.ocr(request);
    Assert.assertNotNull(result);
    Assert.assertEquals(result.getData().getIdno(), "211005199409063123");
  }

//  @Ignore
  @Test
  @Ignore
  public void bankCardOcrPass() {
    BankCardOcrRequest request = new BankCardOcrRequest();
    request.setFrontImgKey("$4cd11b4e-04c2-4456-9c6b-3eb3d8fe1aec$*********");
    RpcOutput<BankCardOcrResponse> result = infoauthService.bankCardOcr(request);
    Assert.assertNotNull(result);
    Assert.assertEquals(result.getData().getBankCardNo(), "623063000001319311");
  }

  @Test
  @Ignore
  public void orgLicenseTencentOcrPass() {
    OrgLicenseOcrRequest request = new OrgLicenseOcrRequest();
    request.setBizAppId("**********");
    request.setImgKey("$9df9385c-f56c-48f3-a81e-dc5fe02a861e$**********");
    RpcOutput<OrgLicenseOcrResponse> result = infoauthService.orgLicenseOcr(request);
    Assert.assertNotNull(result);
//    expected [前信（上海）信息科技有限公司] but found [前信(上海)信息科技有限公司]
//    Assert.assertEquals(result.getData().getName(),"前信（上海）信息科技有限公司");
    Assert.assertEquals(result.getData().getCertNo(),"9100000094935624UN");
  }

  /**
   *
   */@Test
  @Ignore
  public void orgLicenseTencentOcrWarning() {
    OrgLicenseOcrRequest request = new OrgLicenseOcrRequest();
    request.setBizAppId("**********");
    request.setImgKey("$de416d1f-1f99-4b4d-b7f6-a8bd06267a3b$**********");
    org.testng.Assert.assertThrows(() -> infoauthService.orgLicenseOcr(request));
  }

  @Ignore
  @Test
  public void orgLicenseOcrOrgQueryPass() {
    OrgLicenseOcrRequest request = new OrgLicenseOcrRequest();
    request.setImgKey("$d1e7cb04-7075-40b8-8c9f-6572d54d9a8a$**********");
    RpcOutput<OrgLicenseOcrResponse> result = infoauthService.orgLicenseOcr(request);
    Assert.assertNotNull(result);
    Assert.assertEquals(result.getData().getLegalRepName(), "王芳芳");
  }

  @Test
  @Ignore
  public void orgLicenseOcrOrgQueryPassALIYUN() {
    OrgLicenseOcrRequest request = new OrgLicenseOcrRequest();
    request.setBizAppId("**********");
    request.setImgKey("$9df9385c-f56c-48f3-a81e-dc5fe02a861e$**********");
    RpcOutput<OrgLicenseOcrResponse> result = infoauthService.orgLicenseOcr(request);
    Assert.assertNotNull(result);
    Assert.assertEquals(result.getData().getLegalRepName(), "俞杭峰");
  }


  @Test
  public void orgLice() {
    BaiduOrgLicenseOcrClient instance1 = BaiduOrgLicenseOcrClient.getInstance();
    instance1.handle(new BaiduOrgLicenseOcrProvout(), null);

    HuaweiOrgLicenceOcrClient instance = HuaweiOrgLicenceOcrClient.getInstance();
    Assert.assertNotNull(instance.handle(new HuaweiOrgLicenceOcrProvout(), null));
  }



  @Test
  public void bankFourDetailTest() {
    RequestContext.setTransactionId(UUID.randomUUID().toString());
    BankFourRequest bankFourRequest = new BankFourRequest();
    bankFourRequest.setName("武玉华");
    bankFourRequest.setIdno("******************");
    bankFourRequest.setCardno("****************");
    bankFourRequest.setMobile("***********");
    PsnBankFourDetailResponse result = infoauthService.psnBankFourDetail(bankFourRequest);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getInfoauthId());
  }

  @Test
  public void telecomDetailTest() {
    RequestContext.setTransactionId(UUID.randomUUID().toString());
    TelecomRequest telecomRequest = new TelecomRequest();
    telecomRequest.setName("高美君");
    telecomRequest.setIdno("14273019911003072X");
    telecomRequest.setMobile("***********");
    TelecomDetailResponse result = infoauthService.telecomDetail(telecomRequest);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getTelecomAuthId());
  }

  @Test
  public void facePhotoCompareWithoutSource() {
    CompareWithoutSourceRequest request = new CompareWithoutSourceRequest();
    request.setAppId("**********");
    request.setName("朱涛");
    request.setCertNo("******************");
    request.setFaceImgBase64(getImageBase64("facePhotoCompareWithoutSource.txt", "faceImgBase64"));
    RpcOutput<CompareWithoutSourceResponse> result = infoauthService.facePhotoCompareWithoutSource(request);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getData().getInfoauthId());
  }

  @Test
  @Ignore
  public void facePhotoCompareWithoutSource_notPass() {
    CompareWithoutSourceRequest request = new CompareWithoutSourceRequest();
    request.setAppId("**********");
    request.setName("朱涛");
    request.setCertNo("******************");
    request.setFaceImgBase64(
        getImageBase64("facePhotoCompareWithoutSource_notPass.txt", "faceImgBase64"));
    RpcOutput<CompareWithoutSourceResponse> result = infoauthService.facePhotoCompareWithoutSource(request);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getData().getInfoauthId());
  }

  @Test
  public void facePhotoCompareWithSource() {
    CompareWithSourceRequest request = new CompareWithSourceRequest();
    request.setAppId("**********");
    request.setName("朱涛");
    request.setCertNo("******************");
    request.setFaceImgSourceBase64(
        getImageBase64("facePhotoCompareWithSource.txt", "faceImgSourceBase64"));
    request.setFaceImgTargetBase64(
        getImageBase64("facePhotoCompareWithSource.txt", "faceImgTargetBase64"));
    RpcOutput<CompareWithSourceResponse> result = infoauthService.facePhotoCompareWithSource(request);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getData().getInfoauthId());
  }

  @Test
  public void orgInfoAuth_compareName() throws ParseException {
    OrganProvin provin = new OrganProvin();
    OrgProvout provout = new OrgProvout();

    provin.setName("杭州天谷信息科技有限公司");
    provin.setCodeUSC("913301087458306077");
    InfoServiceOrgcheckDO standard = new InfoServiceOrgcheckDO();
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
    String dateStr = "2025.1.1 00:00:00";
    standard.setBusinessEndtime(sdf.parse(dateStr));
    Assert.assertFalse(OrgAuthResultChecker.compareBaseInfo(provin, provout));
    Assert.assertEquals(ProvResult.FAILURE, provout.getProvResult());
  }

  @Test
  public void orgInfoAuth_compareREG() throws ParseException {
    OrganProvin provin = new OrganProvin();
    OrgProvout provout = new OrgProvout();

    provin.setName("杭州天谷信息科技有限公司");
    provin.setCodeREG("913301087458306077");
    InfoServiceOrgcheckDO standard = new InfoServiceOrgcheckDO();
    standard.setName("杭州天谷信息科技有限公司");
    standard.setCodeorg("913301087458306077");
    standard.setCodeusc("913301087458306077");
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
    String dateStr = "2025.1.1 00:00:00";
    standard.setBusinessEndtime(sdf.parse(dateStr));
    Assert.assertFalse(OrgAuthResultChecker.compareBaseInfo(provin, provout));
    Assert.assertEquals(ProvResult.FAILURE, provout.getProvResult());
  }

  @Test
  public void orgInfoAuth_compareORG() throws ParseException {
    OrganProvin provin = new OrganProvin();
    OrgProvout provout = new OrgProvout();

    provin.setName("杭州天谷信息科技有限公司");
    provin.setCodeORG("913301087458306077");

    InfoServiceOrgcheckDO standard = new InfoServiceOrgcheckDO();
    standard.setName("杭州天谷信息科技有限公司");
    standard.setCodeusc("913301087458306077");
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
    String dateStr = "2025.1.1 00:00:00";
    standard.setBusinessEndtime(sdf.parse(dateStr));
    Assert.assertFalse(OrgAuthResultChecker.compareBaseInfo(provin, provout));
    Assert.assertEquals(ProvResult.FAILURE, provout.getProvResult());
  }

  @Test
  public void orgInfoAuth_compareUSC() throws ParseException {
    OrganProvin provin = new OrganProvin();
    OrgProvout provout = new OrgProvout();

    provin.setName("杭州天谷信息科技有限公司");
    provin.setCodeUSC("913301087458306077");

    InfoServiceOrgcheckDO standard = new InfoServiceOrgcheckDO();
    standard.setName("杭州天谷信息科技有限公司");
    standard.setCodereg("913301087458306077");
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
    String dateStr = "2025.1.1 00:00:00";
    standard.setBusinessEndtime(sdf.parse(dateStr));
    OrgInfoAuthOutput output = new OrgInfoAuthOutput(null);
    Assert.assertFalse(OrgAuthResultChecker.compareBaseInfo(provin, provout));
    Assert.assertEquals(ProvResult.FAILURE, provout.getProvResult());
  }

  @Test
  public void orgInfoAuth_compareLegalName() throws ParseException {
    OrganProvin provin = new OrganProvin();
    OrgProvout provout = new OrgProvout();
    provin.setName("杭州天谷信息科技有限公司");
    provin.setCodeUSC("913301087458306077");

    InfoServiceOrgcheckDO standard = new InfoServiceOrgcheckDO();
    standard.setName("杭州天谷信息科技有限公司");
    standard.setCodeusc("913301087458306077");
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
    String dateStr = "2025.1.1 00:00:00";
    standard.setBusinessEndtime(sdf.parse(dateStr));
    OrgAuthResultChecker.compareBaseInfo(provin, provout);
    Assert.assertEquals(ProvResult.FAILURE, provout.getProvResult());

    provin.setLegalRepName("何一兵");
    OrgAuthResultChecker.compareBaseInfo(provin, provout);
    Assert.assertEquals(ProvResult.FAILURE, provout.getProvResult());
  }

  @Test
  public void orgInfoAuth_compareBaseInfoLegalNameIfNotNull() throws ParseException {
    OrganProvin provin = new OrganProvin();
    OrgProvout provout = new OrgProvout();
    provin.setName("杭州天谷信息科技有限公司");
    provin.setCodeUSC("913301087458306077");
    provin.setLegalRepName("何一兵");

    InfoServiceOrgcheckDO standard = new InfoServiceOrgcheckDO();
    standard.setName("杭州天谷信息科技有限公司");
    standard.setCodeusc("913301087458306077");
    standard.setLegalName("何二兵");
    standard.setBusinessStatus(EntStatus.ING.status().dbValue());
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
    String dateStr = "2025.1.1 00:00:00";
    standard.setBusinessEndtime(sdf.parse(dateStr));
    Assert.assertFalse(OrgAuthResultChecker.compareBaseInfoLegalNameIfNotNull(provin, provout));
    Assert.assertEquals(ProvResult.FAILURE, provout.getProvResult());
  }

  @Test
  public void orgInfoAuth_compareBusiness() throws ParseException {
    OrganProvin provin = new OrganProvin();
    OrgProvout provout = new OrgProvout();

    provin.setName("杭州天谷信息科技有限公司");
    provin.setCodeUSC("913301087458306077");
    provin.setLegalRepName("何一兵");

    InfoServiceOrgcheckDO standard = new InfoServiceOrgcheckDO();
    standard.setName("杭州天谷信息科技有限公司");
    standard.setCodeusc("913301087458306077");
    standard.setLegalName("何二兵");
    standard.setBusinessStatus(EntStatus.OTHER.status().dbValue());
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
    String dateStr = "2025.1.1 00:00:00";
    standard.setBusinessEndtime(sdf.parse(dateStr));
    Assert.assertFalse(OrgAuthResultChecker.compareBaseInfoLegalNameIfNotNull(provin, provout));
    Assert.assertEquals(ProvResult.FAILURE, provout.getProvResult());
  }

  @Test
  public void orgInfoAuth_compareOrganTwo() throws ParseException {
    OrganProvin provin = new OrganProvin();
    OrgProvout provout = new OrgProvout();

    provin.setName("杭州天谷信息科技有限公司");
    provin.setCodeUSC("913301087458306077");
    provin.setCodeORG("913301087458306077");
    provin.setCodeREG("913301087458306077");

    InfoServiceOrgcheckDO standard = new InfoServiceOrgcheckDO();
    standard.setName("杭州天谷信息科技有限公司");
    standard.setCodeusc("913301087458306077");
    standard.setCodeorg("913301087458306077");
    standard.setCodereg("913301087458306077");
    standard.setBusinessStatus(EntStatus.ING.status().dbValue());
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
    String dateStr = "2025.1.1 00:00:00";
    standard.setBusinessEndtime(sdf.parse(dateStr));
    Assert.assertFalse(OrgAuthResultChecker.compareOrganTwo(provin, provout));
    Assert.assertEquals(ProvResult.FAILURE, provout.getProvResult());
  }

  @Test
  @Ignore
  public void psnBankFourDetailTest_pass() {
    RequestContext.setTransactionId(UUID.randomUUID().toString());
    BankFourRequest request = new BankFourRequest();
    request.setBizAppId("**********");
    request.setName("林书芳");
    request.setIdno("330327199106085684");
    request.setMobile("***********");
    request.setCardno("6222081203009064490");
    PsnBankFourDetailResponse result = infoauthService.psnBankFourDetail(request);
    Assert.assertNotNull(result);
    Assert.assertTrue(result.isPass());
  }

  @Test
  public void psnBankFourDetailTest_name_incorrect() {
    RequestContext.setTransactionId(UUID.randomUUID().toString());
    BankFourRequest request = new BankFourRequest();
    request.setBizAppId("**********");
    request.setName("林书芳二");
    request.setIdno("330327199106085684");
    request.setMobile("***********");
    request.setCardno("6222081203009064490");
    RequestContext.setTransactionId("6222081203009064490");
    PsnBankFourDetailResponse result = infoauthService.psnBankFourDetail(request);
    Assert.assertNotNull(result);
    Assert.assertFalse(result.isPass());
  }

  @Test
  public void psnBankFourDetailTest_idno_incorrect() {
    RequestContext.setTransactionId(UUID.randomUUID().toString());
    BankFourRequest request = new BankFourRequest();
    request.setBizAppId("**********");
    request.setName("林书芳");
    request.setIdno("610112199309060526");
    request.setMobile("***********");
    request.setCardno("6222081203009064490");
    PsnBankFourDetailResponse result = infoauthService.psnBankFourDetail(request);
    Assert.assertNotNull(result);
    Assert.assertFalse(result.isPass());
  }

  @Test
  public void psnBankFourDetailTest_mobile_incorrect() {
    RequestContext.setTransactionId(UUID.randomUUID().toString());
    BankFourRequest request = new BankFourRequest();
    request.setBizAppId("**********");
    request.setName("林书芳");
    request.setIdno("330327199106085684");
    request.setMobile("***********");
    request.setCardno("6222081203009064490");
    PsnBankFourDetailResponse result = infoauthService.psnBankFourDetail(request);
    Assert.assertNotNull(result);
    Assert.assertFalse(result.isPass());
  }

  @Test
  public void psnBankFourDetailTest_cardno_incorrect() {
    RequestContext.setTransactionId(UUID.randomUUID().toString());
    BankFourRequest request = new BankFourRequest();
    request.setBizAppId("**********");
    request.setName("林书芳");
    request.setIdno("330327199106085684");
    request.setMobile("***********");
    request.setCardno("6217003230045253139");
    PsnBankFourDetailResponse result = infoauthService.psnBankFourDetail(request);
    Assert.assertNotNull(result);
    Assert.assertFalse(result.isPass());
  }

  @Test
  @Ignore
  public void bankThreeDetailTest_pass() {
    RequestContext.setTransactionId(UUID.randomUUID().toString());
    BankThreeRequest request = new BankThreeRequest();
    request.setBizAppId("**********");
    request.setName("林书芳");
    request.setIdno("330327199106085684");
    request.setCardno("6222081203009064490");
    PsnBankThreeDetailResponse result = infoauthService.bankThreeDetail(request);
    Assert.assertNotNull(result);
    Assert.assertTrue(result.isPass());
  }

  @Test
  public void bankThreeDetailTest_name_incorrect() {
    RequestContext.setTransactionId(UUID.randomUUID().toString());
    BankThreeRequest request = new BankThreeRequest();
    request.setBizAppId("**********");
    request.setName("林书芳二");
    request.setIdno("330327199106085684");
    request.setCardno("6222081203009064490");
    PsnBankThreeDetailResponse result = infoauthService.bankThreeDetail(request);
    Assert.assertNotNull(result);
    Assert.assertFalse(result.isPass());
  }

  @Test
  public void bankThreeDetailTest_idno_incorrect() {
    RequestContext.setTransactionId(UUID.randomUUID().toString());
    BankThreeRequest request = new BankThreeRequest();
    request.setBizAppId("**********");
    request.setName("林书芳");
    request.setIdno("610112199309060526");
    request.setCardno("6222081203009064490");
    PsnBankThreeDetailResponse result = infoauthService.bankThreeDetail(request);
    Assert.assertNotNull(result);
    Assert.assertFalse(result.isPass());
  }

  @Test
  public void bankThreeDetailTest_cardno_incorrect() {
    RequestContext.setTransactionId(UUID.randomUUID().toString());
    BankThreeRequest request = new BankThreeRequest();
    request.setBizAppId("**********");
    request.setName("林书芳");
    request.setIdno("330327199106085684");
    request.setCardno("6217003230045253139");
    PsnBankThreeDetailResponse result = infoauthService.bankThreeDetail(request);
    Assert.assertNotNull(result);
    Assert.assertFalse(result.isPass());
  }

  @Test
  public void telecomAuthDetailTest_pass() {
    RequestContext.setTransactionId(UUID.randomUUID().toString());
    TelecomRequest request = new TelecomRequest();
    request.setBizAppId("**********");
    request.setName("黄珂");
    request.setIdno("610112199309060526");
    request.setMobile("***********");
    PsnTelecomDetailResponse result = infoauthService.telecomAuthDetail(request);
    Assert.assertNotNull(result);
    Assert.assertTrue(result.isPass());
  }

  @Test
  public void telecomAuthDetailTest_name_incorrect() {
    RequestContext.setTransactionId(UUID.randomUUID().toString());
    TelecomRequest request = new TelecomRequest();
    request.setBizAppId("**********");
    request.setName("黄珂二");
    request.setIdno("610112199309060526");
    request.setMobile("***********");
    PsnTelecomDetailResponse result = infoauthService.telecomAuthDetail(request);
    Assert.assertNotNull(result);
    Assert.assertFalse(result.isPass());
  }

  @Test
  public void telecomAuthDetailTest_idno_incorrect() {
    RequestContext.setTransactionId(UUID.randomUUID().toString());
    TelecomRequest request = new TelecomRequest();
    request.setBizAppId("**********");
    request.setName("黄珂");
    request.setIdno("330327199106085684");
    request.setMobile("***********");
    PsnTelecomDetailResponse result = infoauthService.telecomAuthDetail(request);
    Assert.assertNotNull(result);
    Assert.assertFalse(result.isPass());
  }

  @Test
  public void telecomAuthDetailTest_mobile_incorrect() {
    RequestContext.setTransactionId(UUID.randomUUID().toString());
    TelecomRequest request = new TelecomRequest();
    request.setBizAppId("**********");
    request.setName("黄珂");
    request.setIdno("610112199309060526");
    request.setMobile("***********");
    PsnTelecomDetailResponse result = infoauthService.telecomAuthDetail(request);
    Assert.assertNotNull(result);
    Assert.assertFalse(result.isPass());
  }

  @SneakyThrows
  private static String getImageBase64(String path, String field) {
    InputStream input;
    input = InfoauthServiceTest.class.getResourceAsStream(path);
    if (input == null) {
      String classPathPath = "classpath:" + path;
      try {
        input = ResourceUtils.getURL(classPathPath).openStream();
      } catch (IOException e) {
        e.printStackTrace();
      }
    }
    if (input == null) {
      throw new RuntimeException("can not load file:" + path);
    }
    byte[] data = IOUtil.readStreamAsByteArray(input);
    String text = new String(data);
    return JSON.parseObject(text).getString(field);
  }

  private String getName(String name) {
    return name + UUIDUtil.generateUUID().substring(0, 5);
  }


    @Test
    public void test() {
      boolean singleAvailableProvider = MonitorAlertResultSupport.singleAvailableProvider(InfoAuthServiceType.ORG_SUMMARY_QUERY, ProviderEnum.TEST);
      boolean isOffLine = MonitorAlertResultSupport.checkProviderIsOffLine(InfoAuthServiceType.ORG_SUMMARY_QUERY, ProviderEnum.TEST);
      InfoProviderDomainDO providerDomainDO = MonitorAlertResultSupport.getDbInfoProviderDomainDOWithOnline(InfoAuthServiceType.ORG_SUMMARY_QUERY, ProviderEnum.TEST);


      Assert.assertNotNull(singleAvailableProvider);
    }
}
