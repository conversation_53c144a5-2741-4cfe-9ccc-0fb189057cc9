package com.timevale.infoauth.service.api;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.impl.biz.impl.inner.api.QueryService;
import com.timevale.infoauth.service.impl.provider.biz.api.XReceiver;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.Bank4Adaptors;
import com.timevale.infoauth.service.enums.InfoAuthServiceType;
import com.timevale.infoauth.service.enums.UserCenterCertTypeEnum;
import com.timevale.infoauth.service.impl.common.dto.Provout;
import com.timevale.infoauth.service.impl.dto.BankauthProvin;
import com.timevale.infoauth.service.response.query.PsnInfoResponse;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2020/4/16 下午5:36
 */
@JsonDataIgnore
public class BankfourJzClientTest extends ApplicationTest {

  @Resource private QueryService queryService;

  @Test
  public void queryPsnInfoErrorTest() {
    PsnInfoResponse result = queryService.queryPsnInfo("dcb47d4b-cfd5-4905-9179-6c277554215d");
    Assert.assertTrue(true);
    Assert.assertNull(result);
  }

  @Test
  public void bank4AdaptorsTest() {
    Bank4Adaptors bank4Adaptors = new Bank4Adaptors();
    XReceiver xReceiver = bank4Adaptors.JZ_bankFourInternational;
    String name = "张三";
    String idNo = "M02120434";
    String bankCard = "6217858000078814644";
    String mobile = "***********";
    int idTypeCode = UserCenterCertTypeEnum.CRED_PSN_PASSPORT.dbValue();
    BankauthProvin bankauthProvin = new BankauthProvin();
    bankauthProvin.setProviderName("JINGZHONG_INTERNATIONAL");
    bankauthProvin.setMobile(mobile);
    bankauthProvin.setCardno(bankCard);
    bankauthProvin.setUserCenterCertType(idTypeCode);
    bankauthProvin.setCertNumber(idNo);
    bankauthProvin.setName(name);
    bankauthProvin.setServiceType(InfoAuthServiceType.BANKCARD_4_ELEMENT_INTERNATIONAL);
    Provout response = xReceiver.apply(bankauthProvin);
    Assert.assertNotNull(response);
    Assert.assertEquals(bankauthProvin.getMobile(), mobile);
  }
}
