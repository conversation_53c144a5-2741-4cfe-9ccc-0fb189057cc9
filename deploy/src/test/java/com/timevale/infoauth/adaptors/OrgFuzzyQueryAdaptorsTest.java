package com.timevale.infoauth.adaptors;

import com.alibaba.fastjson.JSON;
import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.impl.common.dto.Provout;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.OrgFuzzyQueryAdaptors;
import com.timevale.infoauth.service.impl.provider.biz.api.XReceiver;
import com.timevale.infoauth.service.impl.provider.constants.FuzzyQueryProviderEnum;
import com.timevale.infoauth.service.impl.provider.dto.OrgFuzzyQueryProvin;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @DATE 2024/7/19 15:14
 */
@JsonDataIgnore
public class OrgFuzzyQueryAdaptorsTest extends ApplicationTest {

    OrgFuzzyQueryAdaptors adaptors = new OrgFuzzyQueryAdaptors();

    @Test
    public void testQxbNew_query_v131() {

        String json = "{\"bizAppId\":\"**********\",\"bizId\":\"8765de64-ceb1-4e5d-b226-6e8f10024aff\",\"confirmProvider\":false,\"keyword\":\"杭州天谷信息科技有限公司\",\"matchType\":\"ename\",\"providerName\":\"QIXINBAO_NEW_v131\",\"serviceType\":\"ENTERPRISE_FUZZY_QUERY\",\"shuntTag\":false,\"skip\":0,\"transactionId\":\"1921684885T411T17213736485470000\",\"userCenterCertType\":0}";
        XReceiver receiver = adaptors.qxbNew_query_v131;

        OrgFuzzyQueryProvin orgFuzzyQueryProvin = JSON.parseObject(json, OrgFuzzyQueryProvin.class);
        orgFuzzyQueryProvin.setProviderName(FuzzyQueryProviderEnum.QIXINBAO_NEW_v131.name());

        Provout apply = (Provout) receiver.apply(orgFuzzyQueryProvin);
        logger.info(" receiver apply: {}" + JSON.toJSONString(apply));
        Assert.assertNotNull(apply);
    }

    @Test
    public void testQxbNew_query() {

        String json = "{\"bizAppId\":\"**********\",\"bizId\":\"8765de64-ceb1-4e5d-b226-6e8f10024aff\",\"confirmProvider\":false,\"keyword\":\"杭州天谷信息科技有限公司\",\"matchType\":\"ename\",\"providerName\":\"QIXINBAO_NEW_v131\",\"serviceType\":\"ENTERPRISE_FUZZY_QUERY\",\"shuntTag\":false,\"skip\":0,\"transactionId\":\"1921684885T411T17213736485470000\",\"userCenterCertType\":0}";
        XReceiver receiver = adaptors.qxbNew_query;

        OrgFuzzyQueryProvin orgFuzzyQueryProvin = JSON.parseObject(json, OrgFuzzyQueryProvin.class);
        orgFuzzyQueryProvin.setProviderName(FuzzyQueryProviderEnum.QIXINBAO_NEW.name());

        Provout apply = (Provout) receiver.apply(orgFuzzyQueryProvin);
        logger.info(" receiver apply: {}" + JSON.toJSONString(apply));
        Assert.assertNotNull(apply);
    }

    @Test
    public void testQx_query() {

        String json = "{\"bizAppId\":\"**********\",\"bizId\":\"8765de64-ceb1-4e5d-b226-6e8f10024aff\",\"confirmProvider\":false,\"keyword\":\"杭州天谷信息科技有限公司\",\"matchType\":\"ename\",\"providerName\":\"QIXINBAO_NEW_v131\",\"serviceType\":\"ENTERPRISE_FUZZY_QUERY\",\"shuntTag\":false,\"skip\":0,\"transactionId\":\"1921684885T411T17213736485470000\",\"userCenterCertType\":0}";
        XReceiver receiver = adaptors.qxb_query;

        OrgFuzzyQueryProvin orgFuzzyQueryProvin = JSON.parseObject(json, OrgFuzzyQueryProvin.class);
        orgFuzzyQueryProvin.setProviderName(FuzzyQueryProviderEnum.QIXINBAO.name());

        Provout apply = (Provout) receiver.apply(orgFuzzyQueryProvin);
        logger.info(" receiver apply: {}" + JSON.toJSONString(apply));
        Assert.assertNotNull(apply);
    }
}
