package com.timevale.infoauth.service.agreement.impl;

import com.timevale.infoauth.dal.infoauth.dao.agreement.CommonAgreementInfoDAO;
import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementInfoDO;
import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementInfoExtendDO;
import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementReleaseRecordDO;
import com.timevale.infoauth.service.agreement.component.CommonAgreementComponent;
import com.timevale.infoauth.service.agreement.component.CommonAgreementGenerateRecordComponent;
import com.timevale.infoauth.service.agreement.config.CommonAgreementConstant;
import com.timevale.infoauth.service.api.agreement.CommonAgreementService;
import com.timevale.infoauth.service.enums.agreement.CommonAgreementErrorCodeEnum;
import com.timevale.infoauth.service.enums.agreement.CommonAgreementLangEnum;
import com.timevale.infoauth.service.enums.agreement.CommonAgreementReleaseStatusEnum;
import com.timevale.infoauth.service.enums.agreement.CommonAgreementStatusEnum;
import com.timevale.infoauth.service.model.CommonResultEnum;
import com.timevale.infoauth.service.model.SupportResult;
import com.timevale.infoauth.service.request.agreement.CommonAgreementBatchQueryRequest;
import com.timevale.infoauth.service.request.agreement.CommonAgreementGenerateRecordQueryRequest;
import com.timevale.infoauth.service.request.agreement.CommonAgreementGenerateRecordRequest;
import com.timevale.infoauth.service.request.agreement.CommonAgreementQueryRequest;
import com.timevale.infoauth.service.request.agreement.CommonAgreementReleaseRecordQueryRequest;
import com.timevale.infoauth.service.response.agreement.CommonAgreementBatchExtendResponse;
import com.timevale.infoauth.service.response.agreement.CommonAgreementExtendResponse;
import com.timevale.infoauth.service.response.agreement.CommonAgreementGenerateRecordDetailResponse;
import com.timevale.infoauth.service.response.agreement.CommonAgreementGenerateRecordResponse;
import com.timevale.infoauth.service.response.agreement.CommonAgreementInfoDetailResponse;
import com.timevale.infoauth.service.response.agreement.CommonAgreementInfoExtendBO;
import com.timevale.infoauth.service.response.agreement.CommonAgreementReleaseRecordBaseResponse;
import com.timevale.infoauth.service.response.agreement.CommonAgreementReleaseRecordResponse;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.annotation.RestService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.timevale.infoauth.service.request.agreement.CommonAgreementBatchQueryRequest.MAX_BATCH_SIZE;

/**
 * 通用协议服务实现类
 * <AUTHOR>
 * @since 2024/12/3 14:42
 */
@RestService
public class CommonAgreementServiceImpl implements CommonAgreementService {

    @Autowired
    private CommonAgreementComponent agreementComponent;

    @Autowired
    private CommonAgreementInfoDAO agreementInfoDAO;
    @Autowired
    private CommonAgreementGenerateRecordComponent agreementGenerateComponent;

    @Override
    public SupportResult<CommonAgreementExtendResponse> queryAgreementExtendInfo(CommonAgreementQueryRequest request) {
        // 检查通用协议编号是否为空
        String commonAgreementId = request.getCommonAgreementId();
        if (StringUtils.isBlank(commonAgreementId)) {
            return SupportResult.fail(CommonResultEnum.NULL_ARGUMENT,"通用协议编号不能未空");
        }
        // 获取协议基本信息和发布记录
        Pair<CommonAgreementInfoDO, CommonAgreementReleaseRecordDO> agreementInfoPair
                =  agreementComponent.getAgreementInfoBase(request);
        // 校验协议信息是否有效
        SupportResult checkResult = checkAgreementInfo(agreementInfoPair);
        if(checkResult.ifFail()){
            return checkResult;
        }
        // 构建并返回协议扩展信息响应
        CommonAgreementInfoDO agreementInfoDO = agreementInfoPair.getLeft();
        CommonAgreementReleaseRecordDO releaseRecordDO =  agreementInfoPair.getRight();
        CommonAgreementExtendResponse baseResponse = new CommonAgreementExtendResponse();
        baseResponse.setReleaseRecordId(releaseRecordDO.getReleaseRecordId());
        baseResponse.setReleaseVersion(releaseRecordDO.getReleaseVersion());
        baseResponse.setReleaseTime(releaseRecordDO.getReleaseTime().getTime());
        baseResponse.setContentFill(Objects.equals(releaseRecordDO.getContentFill(), CommonAgreementConstant.YES));
        baseResponse.setShortName(releaseRecordDO.getShortName());
        baseResponse.setFullName(releaseRecordDO.getFullName());
        baseResponse.setLang(releaseRecordDO.getLang());
        CommonAgreementResponseConverter.convertInfoResponse(baseResponse, agreementInfoDO);
        return SupportResult.success(baseResponse);
    }

    @Override
    public SupportResult<CommonAgreementBatchExtendResponse> queryBatchAgreementExtendInfo
            (CommonAgreementBatchQueryRequest request) {
        // 检查通用协议编号列表是否为空
        List<String> commonAgreementIds = request.getCommonAgreementIds();
        if(CollectionUtils.isEmpty(commonAgreementIds)){
            return SupportResult.fail(CommonResultEnum.NULL_ARGUMENT,"通用协议编号不能未空");
        }
        // 检查批次大小是否超过限制
        if(commonAgreementIds.size() > MAX_BATCH_SIZE){
            return SupportResult.fail(CommonResultEnum.PARAM_WRONG,"通用协议编号批次不能大于："+MAX_BATCH_SIZE);
        }
        // 获取一批协议信息
        List<CommonAgreementInfoExtendDO> agreementDOList  =
                agreementComponent.getBatchAgreementIdOnRelease(request);
        // 如果协议列表为空，直接返回成功响应
        if(CollectionUtils.isEmpty(agreementDOList)){
            return SupportResult.success();
        }
        // 构建协议信息映射表
        Map<String, CommonAgreementInfoExtendDO> agreementDOCHMap  = agreementDOList
                .stream().filter(item-> CommonAgreementStatusEnum.enable(item.getStatus()))
                .collect(Collectors.toMap(
                        item -> item.getAgreementId() + item.getLang(),
                        item -> item,
                        (existing, replacement) -> existing
                ));
        // 根据请求的语言偏好获取协议信息
        CommonAgreementLangEnum langEnum = CommonAgreementLangEnum.getByLang(request.getLang());
        CommonAgreementBatchExtendResponse response =  new CommonAgreementBatchExtendResponse();
        List<CommonAgreementInfoExtendBO> data = new ArrayList<>();
        for(String commonAgreementId : commonAgreementIds){
            String compositeKey = commonAgreementId + langEnum.getLang();
            CommonAgreementInfoExtendDO target = agreementDOCHMap.get(compositeKey);
            if(target == null){
                // 默认显示中文协议
                compositeKey = commonAgreementId + CommonAgreementLangEnum.ZH_CN.getLang();
                target = agreementDOCHMap.get(compositeKey);
            }
            if(target ==  null){
                continue;
            }
            CommonAgreementInfoExtendBO infoDTO =  new CommonAgreementInfoExtendBO();
            CommonAgreementResponseConverter.convertExtendInfoResponse(infoDTO, target);
            data.add(infoDTO);

        }
        response.setAgreementInfos(data);
        return SupportResult.success(response);
    }

    @Override
    public SupportResult<CommonAgreementInfoDetailResponse> queryAgreementInfoDetail(CommonAgreementQueryRequest request) {
        // 检查通用协议编号是否为空
        String commonAgreementId = request.getCommonAgreementId();
        if (StringUtils.isBlank(commonAgreementId)) {
            return SupportResult.fail(CommonResultEnum.NULL_ARGUMENT,"通用协议编号不能未空");
        }
        // 获取协议基本信息和发布记录
        Pair<CommonAgreementInfoDO, CommonAgreementReleaseRecordDO> agreementInfoPair
                =  agreementComponent.getAgreementInfoBase(request);
        // 校验协议信息是否有效
        SupportResult checkResult = checkAgreementInfo(agreementInfoPair);
        if(checkResult.ifFail()){
            return checkResult;
        }
        // 构建并返回协议详细信息响应
        CommonAgreementInfoDO agreementInfoDO = agreementInfoPair.getLeft();
        CommonAgreementReleaseRecordDO releaseRecordDO =  agreementInfoPair.getRight();
        CommonAgreementInfoDetailResponse detailResponse =  new CommonAgreementInfoDetailResponse();
        detailResponse.setReleaseRecordId(releaseRecordDO.getReleaseRecordId());
        detailResponse.setReleaseTime(releaseRecordDO.getReleaseTime().getTime());
        detailResponse.setContext(releaseRecordDO.getContent());
        detailResponse.setContentType(releaseRecordDO.getContentType());
        detailResponse.setContentFill(Objects.equals(releaseRecordDO.getContentFill(), CommonAgreementConstant.YES));
        detailResponse.setLang(releaseRecordDO.getLang());
        detailResponse.setShortName(releaseRecordDO.getShortName());
        detailResponse.setFullName(releaseRecordDO.getFullName());
        CommonAgreementResponseConverter.convertInfoResponse(detailResponse, agreementInfoDO);
        return SupportResult.success(detailResponse);
    }

    @Override
    public SupportResult<CommonAgreementReleaseRecordBaseResponse> queryAgreementReleaseRecordBase(CommonAgreementReleaseRecordQueryRequest request) {
        // 检查并获取协议发布记录
        SupportResult<CommonAgreementReleaseRecordDO>  supportResult =
                checkAndGetReleaseRecord(request);
        if(supportResult.ifFail()){
            return SupportResult.fail(supportResult.getCode(),supportResult.getMessage());
        }
        CommonAgreementReleaseRecordDO recordDO = supportResult.getData();
        CommonAgreementReleaseRecordBaseResponse response = new CommonAgreementReleaseRecordBaseResponse();
        CommonAgreementResponseConverter.convertReleasedInfo(response, recordDO);
        return SupportResult.success(response);
    }

    @Override
    public SupportResult<CommonAgreementReleaseRecordResponse> queryAgreementReleaseRecordDetail(CommonAgreementReleaseRecordQueryRequest request) {
        SupportResult<CommonAgreementReleaseRecordDO>  supportResult =
                checkAndGetReleaseRecord(request);
        if(supportResult.ifFail()){
            return SupportResult.fail(supportResult.getCode(),supportResult.getMessage());
        }
        CommonAgreementReleaseRecordDO recordDO = supportResult.getData();
        CommonAgreementReleaseRecordResponse response = new CommonAgreementReleaseRecordResponse();

        CommonAgreementResponseConverter.convertReleasedInfo(response, recordDO);

        CommonAgreementInfoDO agreementInfoDO = agreementInfoDAO.getOneAndNotDeleted(recordDO.getAgreementId());
        response.setAgreementType(Optional.ofNullable(agreementInfoDO).map(CommonAgreementInfoDO::getAgreementType).orElse(StringUtils.EMPTY));
        response.setAgreementId(recordDO.getAgreementId());
        return SupportResult.success(response);
    }

    @Override
    public SupportResult<CommonAgreementGenerateRecordDetailResponse>
    queryCommonAgreementGenerateRecordDetail(CommonAgreementGenerateRecordQueryRequest request) {

        // 检查生成协议记录ID是否为空
        String generateRecordId = request.getGenerateRecordId();
        if (StringUtils.isBlank(generateRecordId)) {
            return SupportResult.fail(CommonResultEnum.NULL_ARGUMENT,"生成协议记录ID不能未空");
        }
        CommonAgreementGenerateRecordDetailResponse recordDetailResponse =
                agreementGenerateComponent.getByGenerateRecordId(generateRecordId);

        if (recordDetailResponse == null) {
            return SupportResult.fail(CommonResultEnum.PARAM_WRONG,"生成协议记录未找到");
        }
        return SupportResult.success(recordDetailResponse);
    }

    @Override
    public SupportResult<CommonAgreementGenerateRecordResponse> generateCommonAgreementRecord(CommonAgreementGenerateRecordRequest request) {
        // 检查并获取协议发布记录
        CommonAgreementReleaseRecordQueryRequest recordQueryRequest = new CommonAgreementReleaseRecordQueryRequest();
        recordQueryRequest.setReleaseRecordId(request.getReleaseRecordId());
        SupportResult<CommonAgreementReleaseRecordDO>  supportResult =
                checkAndGetReleaseRecord(recordQueryRequest);
        if(supportResult.ifFail()){
            return SupportResult.fail(supportResult.getCode(),supportResult.getMessage());
        }


        CommonAgreementReleaseRecordDO releaseRecordDO =  supportResult.getData();
        String generateRecordId = agreementGenerateComponent.saveGenerateRecord(request, releaseRecordDO);
        CommonAgreementGenerateRecordResponse generateRecordResponse = new CommonAgreementGenerateRecordResponse();
        generateRecordResponse.setGenerateRecordId(generateRecordId);
        return SupportResult.success(generateRecordResponse);
    }

    @Override
    public SupportResult<CommonAgreementGenerateRecordDetailResponse> generateCommonAgreementRecordDetail(CommonAgreementGenerateRecordRequest request) {
        SupportResult<CommonAgreementGenerateRecordResponse> generateRecordSupportResult =
                generateCommonAgreementRecord(request);
        if(generateRecordSupportResult.ifFail()){
             return SupportResult.fail(generateRecordSupportResult.getCode(),generateRecordSupportResult.getMessage());
        }

        CommonAgreementGenerateRecordQueryRequest queryRequest = new CommonAgreementGenerateRecordQueryRequest();
        queryRequest.setGenerateRecordId(generateRecordSupportResult.getData().getGenerateRecordId());
        return queryCommonAgreementGenerateRecordDetail(queryRequest);
    }

    /**
     * 检查协议发布内部是否有效并返回发布内容
     * @param request
     * @return
     */
    private SupportResult<CommonAgreementReleaseRecordDO> checkAndGetReleaseRecord(
            CommonAgreementReleaseRecordQueryRequest request){
        if(request == null || StringUtils.isBlank(request.getReleaseRecordId())){
            return SupportResult.fail(CommonResultEnum.NULL_ARGUMENT,"协议发布记录ID不能未空");
        }
        CommonAgreementReleaseRecordDO releaseRecordDO = agreementComponent.getByReleaseRecordId(request.getReleaseRecordId());
        if(releaseRecordDO == null){
            return SupportResult.fail(CommonAgreementErrorCodeEnum.AGR_RELEASE_VERSION_NOT_FOUND);
        }
        return SupportResult.success(releaseRecordDO);
    }


    /**
     * 检查通用协议是否有效并返回协议信息
     * @param agreementInfoPair
     * @return
     */
    private SupportResult checkAgreementInfo(
            Pair<CommonAgreementInfoDO, CommonAgreementReleaseRecordDO> agreementInfoPair) {

        if(agreementInfoPair == null){
            return SupportResult.fail(CommonAgreementErrorCodeEnum.AGR_INFO_NOT_FOUND);
        }
        CommonAgreementInfoDO agreementInfoDO = agreementInfoPair.getLeft();
        if(agreementInfoDO == null){
            return SupportResult.fail(CommonAgreementErrorCodeEnum.AGR_INFO_NOT_FOUND);
        }
        if (CommonAgreementStatusEnum.notEnable(agreementInfoDO.getStatus())) {
            return SupportResult.fail(CommonAgreementErrorCodeEnum.AGR_INFO_NOT_ENABLE);
        }
        CommonAgreementReleaseRecordDO releaseRecordDO =  agreementInfoPair.getRight();
        if(releaseRecordDO == null){
            return SupportResult.fail(CommonAgreementErrorCodeEnum.AGR_RELEASE_VERSION_NOT_FOUND);
        }
        if (CommonAgreementReleaseStatusEnum.notEnable(releaseRecordDO.getEnableStatus())) {
            return SupportResult.fail(CommonAgreementErrorCodeEnum.AGR_RELEASE_VERSION_NOT_ENABLE);
        }

        return SupportResult.success();
    }

}
