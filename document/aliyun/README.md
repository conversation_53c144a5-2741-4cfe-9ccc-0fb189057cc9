## 一、供应商文档
- 阿里云OCR对接文档 ： https://help.aliyun.com/zh/ocr/product-overview/ocr-unified-identification/?spm=a2c4g.11186623.0.0.252d5442KUnOF0
- 阿里云OCR官网 ： https://homenew.console.aliyun.com/home/<USER>/ResourceDashboard
- 阿里云AK地址 ： https://ram.console.aliyun.com/manage/ak?spm=a2c8b.20231166.console-base_top-nav.dak.20ae336aBZinJK
- 登录账号 ： timevale123
- appId =  LTAI5....qumNDU
- 采购  八戒 叔夜
- 对接人：芒草
- 产品 ：淡竹
- 备注：
- 身份证识别
  银行卡识别
  营业执照识别
  驾驶证识别
  行驶证识别 
- 不支持外国人永久居住证识别

## 二、对接流程

- 1、运营支持平台注册供应商信息
  （1）供应商管理页面注册供应商信息
  https://testsupport.tsign.cn/microfe/realname/provider
  （2）配置中心配置供应商流水查询供应商信息
  https://dcc.tsign.cn/config.html?#/appid=manual-web-service
  key =record.query.condition.provider.list
  （3）主通道配置新供应商
  https://testsupport.tsign.cn/microfe/open/application/2139
  （4）数据库
  供应商记录修改密钥
- 2、代码注册供应商信息
  com.timevale.infoauth.service.impl.constants.ProviderEnum
  com.timevale.infoauth.service.impl.provider.constants.OcrProviderEnum
- 3、注册供应商渠道和实现
  com.timevale.infoauth.service.impl.provider.biz.adaptors.PsnIdOcrAdaptors
  com.timevale.infoauth.service.impl.provider.biz.adaptors.BankCardOcrAdaptors
  com.timevale.infoauth.service.impl.provider.biz.adaptors.OrgLicenceOcrAdaptors
  com.timevale.infoauth.service.impl.provider.biz.adaptors.DriversLicenceOcrAdaptors
  com.timevale.infoauth.service.impl.provider.biz.adaptors.DriversPermitOcrAdaptors

#### 发布注意
- 先运营支撑后台新增供应商，切记不要上架供应商
- 更新秘钥 使用正式环境的预发网关更新
RPC : com.timevale.infoauth.service.api.InfoauthBackStage.updateProviderDomainClientMetaPasswordByIdAndAccount
- 在运营支撑后台在把供应商上架