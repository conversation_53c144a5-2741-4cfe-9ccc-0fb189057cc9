package com.timevale.infoauth.dal.infoauth.dataobject;

import lombok.Data;

import java.util.Date;

@Data
public class InfoServiceOrgTwoDO {
  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;
  /** name 企业名称. */
  private String name;
  /** codeorg 企业组织机构代码. */
  private String codeorg;
  /** codereg 工商注册号. */
  private String codereg;
  /** codeusc 企业社会统一信用代码. */
  private String codeusc;
  /** infoauthId 信息比对服务id. */
  private String infoauthId;

  /** id ID. */
  private Integer id;
  /** result 适配结果. */
  private Integer result;

  /** serviceStatus 记录状态. */
  private Integer serviceStatus;
}
