package com.timevale.infoauth.dal.infoauth.facade;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class OutlierDataRecordDTO {
  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;
  /** id 自增主键. */
  private Long id;
  /** infoauthId 业务主键. */
  private String infoauthId;
  /** @See com.timevale.infoauth.service.enums.InfoAuthServiceType */
  private Integer type;
  /** 操作类型:A-add;U-update;D-delete */
  private String eventType;
  /** 操作异常数据 */
  private String requestDate;
  /** 状态0-未补偿;1-已经补偿 */
  private Integer status;
  /**
   * 开始日期
   * */
  private Date startTime;
  /**
   * 结束日期
   * */
  private Date endTime;

  /**
   * 开始id
   * */
  private Long startId;
  /**
   * 结束id
   * */
  private Long endId;

  /**
   * 分页信息
   * */
  int pageIndex, pageSize;
}
