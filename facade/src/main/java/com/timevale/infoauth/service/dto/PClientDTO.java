package com.timevale.infoauth.service.dto;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PClientDTO extends ToString {

  private Integer id;

  private String name;

  private String desc;

  private String url;

  private String account;

  private String password;

  private String signKey;

  private String verifyKey;

  private Integer connectTimeout;

  private Integer readTimeout;

  private Integer tokenTtl;

  private String mockUrl;

  private String extendJson;
}
