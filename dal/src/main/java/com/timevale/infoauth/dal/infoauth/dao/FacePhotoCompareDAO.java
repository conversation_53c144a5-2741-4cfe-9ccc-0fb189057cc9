package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.FacePhotoCompareDO;
import org.apache.ibatis.annotations.Insert;

/** 人脸照片比对 */
public interface FacePhotoCompareDAO {

  @Insert({
    "<script>",
    "INSERT INTO `face_photo_compare` (`infoauth_id`, `biz_type`, `request_id`, `app_id`, ",
    "`biz_id`, `cert_no`, `name`, `provider_id`, `provider_result`, `result`, `service_status`, `response_id`) VALUES ",
    "(#{infoauthId}, #{bizType}, #{requestId}, #{appId},",
    "#{bizId}, #{certNo}, #{name}, #{providerId}, #{providerResult}, #{result}, #{serviceStatus}, #{responseId})",
    "</script>"
  })
  int insert(FacePhotoCompareDO entity);
}
