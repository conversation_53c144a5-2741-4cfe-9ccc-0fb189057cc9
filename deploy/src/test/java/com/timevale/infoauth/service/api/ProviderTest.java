package com.timevale.infoauth.service.api;

import com.timevale.filesystem.common.service.api.FileSystemService;
import com.timevale.filesystem.common.service.query.GetDownloadUrlInput;
import com.timevale.filesystem.common.service.result.GetDownloadUrlResult;
import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.impl.provider.biz.api.XReceiver;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.BaseBizAdaptors;
import com.timevale.infoauth.service.impl.provider.constants.BizAdaptorsEnum;
import com.timevale.infoauth.service.enums.InfoAuthServiceType;
import com.timevale.infoauth.service.enums.ProvResult;
import com.timevale.infoauth.service.exception.SuperException;
import com.timevale.infoauth.service.impl.provider.biz.adapter.organ.OrganAdapterMAYI;
import com.timevale.infoauth.service.impl.provider.constants.Org4ProviderEnum;
import com.timevale.infoauth.service.impl.common.dto.OrgProvout;
import com.timevale.infoauth.service.impl.common.dto.OrganProvin;
import com.timevale.mandarin.base.util.HttpUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;

@JsonDataIgnore
public class ProviderTest extends ApplicationTest {

  @Resource private FileSystemService fileSystemService;

  @Resource private OrganAdapterMAYI organAdapterMAYI;

  @Test
  public void test21() {
    OrganProvin provin = new OrganProvin();
    provin.setProviderName("MAYI");
    provin.setOrgName("杭州天谷信息科技有限公司");
    provin.setOrgCertNo("330108000003512");
    provin.setLegalRepName("何一兵");
    provin.setLegalRepCertNo("110108196710262291");
    OrgProvout provout = organAdapterMAYI.info4(provin);
    Assert.assertNotNull(provout);
    Assert.assertEquals(provin.getOrgCertNo(), "330108000003512");
  }

//  @Test
//  public void jzOrg4() {
//    OrganProvin organProvin = new OrganProvin();
//    organProvin.setServiceType(InfoAuthServiceType.ENTERPRISE_4_ELEMENT);
//    organProvin.setOrgName("杭州天谷信息科技有限公司");
//    organProvin.setOrgCertNo("913301087458306077");
//    organProvin.setLegalRepName("金宏洲");
//    organProvin.setLegalRepCertNo("330722197904110013");
//    BaseBizAdaptors receiverCollection =
//        BizAdaptorsEnum.getAdaptorsViaServiceType(InfoAuthServiceType.ENTERPRISE_4_ELEMENT);
//    XReceiver receiver = receiverCollection.getReceiver(Org4ProviderEnum.JINGZHONG.dbKey());
//    Assert.assertNotNull(receiver);
//    Assert.assertEquals(
//        ProvResult.SUCCESS, receiver.apply(organProvin).getProvResult());
//  }

  // 下载base64字符串
  private String downloadString(String key) {
    GetDownloadUrlInput input = new GetDownloadUrlInput();
    input.setBucket("esignoss");
    input.setInternal(false);
    input.setExpire(30000);
    input.setFileKey(key);
    GetDownloadUrlResult downUrl = fileSystemService.getDownloadUrl(input);

    try {
      return HttpUtils.executeGetMethod(downUrl.getUrl(), "utf-8");
    } catch (Exception e) {
      throw new SuperException("下载文件出错");
    }
  }
}
