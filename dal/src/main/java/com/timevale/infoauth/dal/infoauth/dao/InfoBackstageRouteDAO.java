package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.cache.CacheConstant;
import com.timevale.infoauth.dal.infoauth.dataobject.InfoBackstageRouteDO;
import org.apache.ibatis.annotations.*;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@CacheConfig(cacheNames = CacheConstant.CACHE_NAME)
public interface InfoBackstageRouteDAO {

  @Insert(
      "INSERT INTO `info_backstage_route` ("
          + "`cert_no`, `name`, `verify_type`, `provider`,`create_time`, `lost_time`)"
          + " VALUES (#{certNo}, #{name},#{verifyType}, #{provider}, now(), #{lostTime})")
  @Options(useGeneratedKeys = true, keyColumn = "id")
  int insert(InfoBackstageRouteDO entity);

  @Select(
      "SELECT * FROM `info_backstage_route` WHERE cert_no = #{certNo} and verify_type = #{verifyType} ORDER BY id DESC LIMIT 1")
  InfoBackstageRouteDO selectByCertAndVerifyType(
      @Param("certNo") String certNo, @Param("verifyType") int verifyType);


  @Select("SELECT * FROM `info_backstage_route` WHERE id = #{id}")
  InfoBackstageRouteDO getById(@Param("id") long id);

  @Select({
      "<script>",
      "SELECT * FROM `info_backstage_route` WHERE id in (",
      "<foreach collection='ids' item='item' separator=','>",
      "#{item}",
      "</foreach>",
      ")",
      "</script>"
  })
  List<InfoBackstageRouteDO> getByIds(@Param("ids") List<Long> ids);

  @Delete("DELETE FROM `info_backstage_route` WHERE id = #{id}")
  int deleteById(
      @Param("id") long id);

  @Select("SELECT * FROM `info_backstage_route` WHERE cert_no = #{certNo} and `provider` = #{provider} ORDER BY ID DESC LIMIT 1")
  InfoBackstageRouteDO getEntityByCertNoAndProvider(@Param("certNo") String certNo,@Param("provider")String provider);

  @Select(
      "<script>"
          + "SELECT * FROM `info_backstage_route` WHERE 1=1 "
          + "<if test=\"name != null\">"
          + " AND name=#{name}"
          + "</if>"
          + "<if test=\"certNo != null\">"
          + " AND cert_no=#{certNo}"
          + "</if>"
          + "<if test=\"type != null\">"
          + " AND verify_type=#{type}"
          + "</if>"
          + "<if test=\"start != null and end != null\">"
          + " AND `create_time` &gt; #{start} AND `create_time` &lt; #{end}"
          + "</if>"
          + " ORDER BY id DESC LIMIT #{pageIndex},#{pageSize}"
          + "</script>")
  List<InfoBackstageRouteDO> getListPages(
      @Param("name") String name,
      @Param("certNo") String certNo,
      @Param("type") Integer type,
      @Param("start") String start,
      @Param("end") String end,
      @Param("pageIndex") int pageIndex,
      @Param("pageSize") int pageSize);

  @Select(
      "<script>"
          + "SELECT count(1) FROM `info_backstage_route` WHERE 1=1"
          + "<if test=\"name != null\">"
          + " AND `name` = #{name}"
          + "</if>"
          + "<if test=\"certNo != null\">"
          + " AND `cert_no` = #{certNo}"
          + "</if>"
          + "<if test=\"type != null\">"
          + " AND verify_type=#{type}"
          + "</if>"
          + "<if test=\"start != null and end != null\">"
          + " AND `create_time` &gt; #{start} AND `create_time` &lt; #{end}"
          + "</if>"
          + " ORDER BY id DESC"
          + "</script>")
  int getListCount(
      @Param("name") String name,
      @Param("certNo") String certNo,
      @Param("type") Integer type,
      @Param("start") String start,
      @Param("end") String end);

  @Select({
          "<script>",
          "SELECT count(1) FROM `info_backstage_route`",
          "where  1=1 ",
          "<if test='name != null'> and name = #{name} </if>",
          "<if test='certNo != null'> and cert_no= #{certNo} </if>",
          "<if test='type != null'> and verify_type = #{type} </if>",
          "<if test='start != null'>AND `create_time` &lt;=  #{start} AND #{start} &lt;= `lost_time` </if>",
          "ORDER BY id DESC",
          "</script>"})
  int getListCountByLostTime(
          @Param("name") String name,
          @Param("certNo") String certNo,
          @Param("type") Integer type,
          @Param("start") Timestamp start);

  @Select("SELECT count(*) FROM `info_backstage_route` WHERE lost_time > now()")
  int selectCount();

  @Select("SELECT * FROM `info_backstage_route` WHERE lost_time > now() LIMIT #{offset},#{pageSize}")
  List<InfoBackstageRouteDO> selectAll(@Param("offset") int offset, @Param("pageSize") int pageSize);

  @Select({
      "<script>",
      "SELECT count(*) FROM info_backstage_route ",
      "WHERE `verify_type` = #{verifyType} ",
      "AND `cert_no` IN",
      "<foreach item='item' index='index' collection='certNoList' open='(' separator=',' close=')'>",
      "#{item}",
      "</foreach>",
      "</script>"})
  int countExist(@Param("verifyType") int verifyType, @Param("certNoList") List<String> certNoList);

  @Insert({
      "<script>",
      "INSERT INTO info_backstage_route (`cert_no`, `name`, `verify_type`, `provider`,`create_time`, `lost_time`) VALUES ",
      "<foreach collection='list' item='item' separator=','>",
      "(#{item.certNo}, #{item.name}, #{item.verifyType}, #{item.provider}, now(), #{item.lostTime})",
      "</foreach>",
      "</script>"
  })
  @Options(useGeneratedKeys = true, keyColumn = "id")
  int insertBatch(List<InfoBackstageRouteDO> list);
}
