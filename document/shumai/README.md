## 一、供应商文档

- 数脉对接文档 ： https://www.showdoc.com.cn/p/c7f3c9a548cfc9d623c677930bd62b72
- 数脉官网 ： https://my.shumaiapi.com/#/login
- 登录账号 ： 15397076876
- 查询appId 和 秘钥 路径 ： 用户中心-账户信息
- appId =  Y29LOi9kleRLVe368wCyaQ4dUHjXZsoi
- 采购 叔夜
- 对接人：芒草
- 产品 ：笙声-李霄荻
- 备注：除了大陆，还支持港澳台居住证的号码 81 82 83开头的

## 二、对接流程

- 1、运营支持平台注册供应商信息
  （1）供应商管理页面注册供应商信息
  https://testsupport.tsign.cn/microfe/realname/provider
  （2）配置中心配置供应商流水查询供应商信息
  https://dcc.tsign.cn/config.html?#/appid=manual-web-service
  key =record.query.condition.provider.list
  （3）主通道配置新供应商
  https://testsupport.tsign.cn/microfe/open/application/2139
  （4）数据库
  供应商记录修改密钥
- 2、代码注册供应商信息
  com.timevale.infoauth.service.impl.constants.ProviderEnum
  com.timevale.infoauth.service.impl.provider.constants.IdCard2ProviderEnum
- 3、注册供应商渠道和实现
  com.timevale.infoauth.service.impl.provider.biz.adaptors.Psn2Adaptors

#### 发布注意
- 先运营支撑后台新增供应商，切记不要上架供应商
- 更新秘钥 使用正式环境的预发网关更新
RPC : com.timevale.infoauth.service.api.InfoauthBackStage.updateProviderDomainClientMetaPasswordByIdAndAccount
- 在运营支撑后台在把供应商上架



