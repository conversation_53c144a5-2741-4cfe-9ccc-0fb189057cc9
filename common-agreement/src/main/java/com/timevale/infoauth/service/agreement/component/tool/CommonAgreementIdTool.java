package com.timevale.infoauth.service.agreement.component.tool;

import com.timevale.framework.sands.Sahara;
import com.timevale.infoauth.service.agreement.config.CommonAgreementConfigurableProperties;

/**
 * 协议ID小工具
 *
 * <AUTHOR>
 * @since 2024/12/4 21:29
 */
public class CommonAgreementIdTool {

  public static String markAgreementId() {
    return CommonAgreementConfigurableProperties.getInstance().getAgreementIdPrefix()
        + Sahara.instance.getSand();
  }

  public static String markAgreementReleaseId() {
    return CommonAgreementConfigurableProperties.getInstance().getAgreementReleaseIdPrefix()
        + Sahara.instance.getSand();
  }
}
