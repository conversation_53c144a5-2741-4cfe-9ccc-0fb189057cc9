package com.timevale.infoauth.dal.infoauth.dao.agreement;

import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementReleaseRecordDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 通用协议内部发布记录DAO
 *
 * <AUTHOR> Kunpeng
 */
@Repository
public class CommonAgreementReleaseRecordDAO {

    /**
     * The Mapper for common agreement release record.
     */
    @Autowired
    private CommonAgreementReleaseRecordMapper recordMapper;

    /**
     * Save a common agreement release record.
     *
     * @param infoDO the information data object to be saved
     * @return true if the save operation is successful, otherwise false
     */
    public boolean save(CommonAgreementReleaseRecordDO infoDO){
        return recordMapper.insert(infoDO) > 0;
    }

    /**
     * Update a common agreement release record by its ID.
     *
     * @param infoDO the information data object containing the updated information
     * @return always returns true, indicating the update operation has been executed
     */
    public boolean updateById(CommonAgreementReleaseRecordDO infoDO){
        recordMapper.updateById(infoDO);
        return Boolean.TRUE;
    }

    /**
     * Get a common agreement release record by its release record ID.
     *
     * @param releaseRecordId the release record ID
     * @return the queried common agreement release record data object, or null if not found
     */
    public CommonAgreementReleaseRecordDO getOne(String releaseRecordId){
        return recordMapper.getByReleaseRecordId(releaseRecordId);
    }
}
