<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE log4j2:configuration>
<!-- log4j2 配置文件 -->
<configuration name="log4j2Conf" status="WARN" monitorInterval="60" xmlns:xi="http://www.w3.org/2001/XInclude">
	<properties>  
		<property name="log.path" value="./logs" />
		<property name="log.encoding" value="UTF-8" />
		<property name="log.level" value="INFO" />
		<property name="appName" value="infoauth" />
	</properties>

	<xi:include href="log4j2_appenders.xml" />

	<Loggers>
		<!-- DAL统一日志 -->
		<Logger name="com.timevale.infoauth.common.dal" level="${log.level}"
			additivity="false">
			<appender-ref ref="DAL-APPENDER" />
			<appender-ref ref="SYS-ERROR-APPENDER" />
			<appender-ref ref="Console" />
		</Logger>
		
		<Logger name="org.apache.ibatis" level="${log.level}"
			additivity="false">
			<appender-ref ref="DAL-APPENDER" />
			<appender-ref ref="SYS-ERROR-APPENDER" />
			<appender-ref ref="Console" />
		</Logger>

		<!-- 数据访问层 - SQL -->
		<Logger name="com.alibaba.druid" level="${log.level}"
			additivity="false">
			<appender-ref ref="DAL-APPENDER" />
			<appender-ref ref="SYS-ERROR-APPENDER" />
			<appender-ref ref="Console" />
		</Logger>

		<!-- 业务服务层日志 -->
		<Logger name="com.timevale.infoauth.service" level="${log.level}"
			additivity="false">
			<appender-ref ref="SYS-BIZ-SERVICE-APPENDER" />
			<appender-ref ref="SYS-ERROR-APPENDER" />
			<appender-ref ref="Console" />
		</Logger>

		<!-- 外部业务调用层日志 -->
		<Logger name="com.timevale.infoauth.integration" level="${log.level}"
			additivity="false">
			<appender-ref ref="SYS-BIZ-SERVICE-APPENDER" />
			<appender-ref ref="SYS-ERROR-APPENDER" />
			<appender-ref ref="Console" />
		</Logger>

		<!-- 供应商调用日志 -->
		<Logger name="providerLog" level="${log.level}"
				additivity="false">
<!--			<appender-ref ref="SYS-BIZ-SERVICE-APPENDER" />-->
			<appender-ref ref="SYS-BIZ-PROVIDER-APPENDER" />
<!--			<appender-ref ref="SYS-ERROR-APPENDER" />-->
			<appender-ref ref="Console" />
		</Logger>

		<!-- 监控供应商调用日志 -->
		<Logger name="monitorProviderLog" level="${log.level}"
				additivity="false">
			<appender-ref ref="SYS-BIZ-MONITOR-APPENDER" />
			<appender-ref ref="Console" />
		</Logger>

		<!-- 监控供应商调用日志 -->
		<Logger name="riskProviderLog" level="${log.level}"
				additivity="false">
			<appender-ref ref="SYS-BIZ-RISK-APPENDER" />
			<appender-ref ref="Console" />
		</Logger>

		<!-- 风控SDK 内部日志 Appender -->
		<Logger name="com.timevale.security.risk" level="info"
				additivity="false">
			<AppenderRef ref="SYS-RISK-SERVICE-APPENDER"/>
			<appender-ref ref="Console" />
		</Logger>

		<Root level="${log.level}">
			<appender-ref ref="DEFAULT-APPENDER" />
			<appender-ref ref="SYS-ERROR-APPENDER" />
			<appender-ref ref="Console" />
		</Root>
	</Loggers>
</configuration>