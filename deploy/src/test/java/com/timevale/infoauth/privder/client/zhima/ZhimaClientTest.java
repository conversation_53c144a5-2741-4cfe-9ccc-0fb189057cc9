package com.timevale.infoauth.privder.client.zhima;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.model.enums.OrganSelectTypeEnum;
import com.timevale.infoauth.service.enums.EntStatus;
import com.timevale.infoauth.service.enums.InfoAuthServiceType;
import com.timevale.infoauth.service.enums.ProvResult;
import com.timevale.infoauth.service.impl.common.dto.OrgProvout;
import com.timevale.infoauth.service.impl.common.dto.OrganProvin;
import com.timevale.infoauth.service.impl.common.dto.QueryOrgProvout;
import com.timevale.infoauth.service.impl.dto.EnterpriseAccurateQueryProvin;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.CommunityAdaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.EnterpriseAccurateCertNoQueryAdaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.LawFirmAdaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.Org2Adaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.Org3Adaptors;
import com.timevale.infoauth.service.impl.provider.constants.AccurateQueryProviderEnum;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @since 2023/11/22 14:03
 */
@JsonDataIgnore
public class ZhimaClientTest extends ApplicationTest {



    @Test
    public void zhima_certNoQuery(){
        EnterpriseAccurateCertNoQueryAdaptors certNoQueryAdaptors = new EnterpriseAccurateCertNoQueryAdaptors();
        EnterpriseAccurateQueryProvin input = new EnterpriseAccurateQueryProvin();
        input.setBizAppId("**********");
        input.setProviderName(AccurateQueryProviderEnum.ZHIMA.dbKey());
        input.setCertNumber("91331004092797378L");
        QueryOrgProvout queryOrgProvout = (QueryOrgProvout) certNoQueryAdaptors.zhima_identityauthvalid.apply(input);
        Assert.assertEquals(queryOrgProvout.getCodeUSC(), input.getCertNumber());
        Assert.assertNull(queryOrgProvout.getCodeORG());
        Assert.assertEquals(queryOrgProvout.getName(),"台州市路桥轩驰机械有限公司");
        Assert.assertEquals(queryOrgProvout.getOperName(),"戴文西");
        Assert.assertNotNull(queryOrgProvout.getAddress());
        Assert.assertNotNull(queryOrgProvout.getRegistCapi());
        Assert.assertNotNull(queryOrgProvout.getScope());
        Assert.assertNotNull(queryOrgProvout.getStatus());
        Assert.assertNotNull(queryOrgProvout.getStartDate());
        Assert.assertNotNull(queryOrgProvout.getEconKind());
//        Assert.assertNotNull(queryOrgProvout.getTermStart());
//        Assert.assertNotNull(queryOrgProvout.getTermEnd());
    }


    @Test
    public void ZHIMA_lawFirmQuery(){
        LawFirmAdaptors adaptors = new LawFirmAdaptors();
        OrganProvin input = new OrganProvin();
        input.setBizAppId("**********");
        input.setProviderName(AccurateQueryProviderEnum.ZHIMA.dbKey());
        input.setOrganSelectType(OrganSelectTypeEnum.LAWFIRM);
        input.setOrgCertNo("311100007263734044");
        input.setOrgName("北京市盈科律师事务所");
        input.setLegalRepName("梅向荣");
        input.setCodeUSC("311100007263734044");
        OrgProvout provout = (OrgProvout) adaptors.ZHIMA_lawFirmQuery.apply(input);
        Assert.assertNotNull(provout);
        Assert.assertEquals(provout.getEntStatus(), EntStatus.ING);
        Assert.assertEquals(provout.getProvResult(), ProvResult.SUCCESS);
    }


    @Test
    public void ZHIMA_lawFirm2Query(){
        LawFirmAdaptors adaptors = new LawFirmAdaptors();
        OrganProvin input = new OrganProvin();
        input.setBizAppId("**********");
        input.setProviderName(AccurateQueryProviderEnum.ZHIMA.dbKey());
        input.setOrganSelectType(OrganSelectTypeEnum.LAWFIRM2);
        input.setOrgCertNo("311100007263734044");
        input.setOrgName("北京市盈科律师事务所");
        input.setCodeUSC("311100007263734044");
        OrgProvout provout = (OrgProvout) adaptors.ZHIMA_lawFirmQuery.apply(input);
        Assert.assertNotNull(provout);
        Assert.assertEquals(provout.getEntStatus(), EntStatus.ING);
        Assert.assertEquals(provout.getProvResult(), ProvResult.SUCCESS);
    }

    @Test
    public void ZHIMA_socialOrganizationQuery(){
        CommunityAdaptors adaptors = new CommunityAdaptors();
        OrganProvin input = new OrganProvin();
        input.setBizAppId("**********");
        input.setProviderName(AccurateQueryProviderEnum.ZHIMA.dbKey());
        input.setOrganSelectType(OrganSelectTypeEnum.SOCIALORGANIZATION);
        input.setOrgCertNo("51640000MJX1502942");
        input.setOrgName("宁夏社会组织总会");
        input.setLegalRepName("陈忠焕");
        input.setCodeUSC("51640000MJX1502942");
        OrgProvout provout = (OrgProvout) adaptors.ZHIMA_socialOrganizationQuery.apply(input);
        Assert.assertNotNull(provout);
        Assert.assertEquals(provout.getEntStatus(), EntStatus.ING);
        Assert.assertEquals(provout.getProvResult(), ProvResult.SUCCESS);
    }

    @Test
    public void ZHIMA_socialOrganization2Query(){
        CommunityAdaptors adaptors = new CommunityAdaptors();
        OrganProvin input = new OrganProvin();
        input.setBizAppId("**********");
        input.setProviderName(AccurateQueryProviderEnum.ZHIMA.dbKey());
        input.setOrganSelectType(OrganSelectTypeEnum.SOCIALORGANIZATION2);
        input.setOrgCertNo("51640000MJX1502942");
        input.setOrgName("宁夏社会组织总会");
        input.setCodeUSC("51640000MJX1502942");
        OrgProvout provout = (OrgProvout) adaptors.ZHIMA_socialOrganizationQuery.apply(input);
        Assert.assertNotNull(provout);
        Assert.assertEquals(provout.getEntStatus(), EntStatus.ING);
        Assert.assertEquals(provout.getProvResult(), ProvResult.SUCCESS);
    }


    @Test
    public void Zhima_organThreeElement(){
        Org3Adaptors adaptors = new Org3Adaptors();
        OrganProvin input = new OrganProvin();
        input.setBizAppId("**********");
        input.setProviderName(AccurateQueryProviderEnum.ZHIMA.dbKey());
        input.setOrganSelectType(OrganSelectTypeEnum.ByCertNo);
        input.setServiceType(InfoAuthServiceType.ENTERPRISE_3_ELEMENT);
        input.setOrgCertNo("91331004092797378L");
        input.setOrgName("台州市路桥轩驰机械有限公司");
        input.setCodeUSC("91331004092797378L");
        input.setLegalRepName("戴文西");
        OrgProvout provout = (OrgProvout) adaptors.Zhima_organThreeElement.apply(input);
        Assert.assertNotNull(provout);
        Assert.assertEquals(provout.getEntStatus(), EntStatus.ING);
        Assert.assertEquals(provout.getProvResult(), ProvResult.SUCCESS);
    }

    @Test
    public void Zhima_organTwoElement(){
        Org2Adaptors adaptors = new Org2Adaptors();
        OrganProvin input = new OrganProvin();
        input.setBizAppId("**********");
        input.setProviderName(AccurateQueryProviderEnum.ZHIMA.dbKey());
        input.setOrganSelectType(OrganSelectTypeEnum.ByCertNo);
        input.setServiceType(InfoAuthServiceType.ENTERPRISE_2_ELEMENT);
        input.setOrgCertNo("91331004092797378L");
        input.setOrgName("台州市路桥轩驰机械有限公司");
        input.setCodeUSC("91331004092797378L");
        OrgProvout provout = (OrgProvout) adaptors.Zhima_twoElement.apply(input);
        Assert.assertNotNull(provout);
        Assert.assertEquals(provout.getEntStatus(), EntStatus.ING);
        Assert.assertEquals(provout.getProvResult(), ProvResult.SUCCESS);
    }
}
