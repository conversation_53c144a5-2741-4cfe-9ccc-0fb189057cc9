package com.timevale.infoauth.service.dto.motitor;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/7/17 21:10
 */
@Data
public class ProviderDegradeDTO extends ToString {

    private String condition = "Count:count >= 1; Condition:markStatus=='UNKNOWN' ||markStatus=='TIMEOUT";
    private String bizType ="FACE";
    private String providerKey;
}
