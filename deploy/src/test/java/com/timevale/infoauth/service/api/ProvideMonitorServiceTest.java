package com.timevale.infoauth.service.api;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.dal.infoauth.dao.monitor.AlertConfigDAO;
import com.timevale.infoauth.dal.infoauth.dao.monitor.DegradationConfigDAO;
import com.timevale.infoauth.dal.infoauth.dao.monitor.ProviderNotifyConfigDAO;
import com.timevale.infoauth.dal.infoauth.dataobject.monitor.AlertConfigDO;
import com.timevale.infoauth.dal.infoauth.dataobject.monitor.DegradationConfigDO;
import com.timevale.infoauth.dal.infoauth.dataobject.monitor.ProviderNotifyConfigDO;
import com.timevale.infoauth.service.dto.motitor.*;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @since 2024/7/18 15:03
 */
@JsonDataIgnore
public class ProvideMonitorServiceTest extends ApplicationTest {

    @Autowired
    public ProvideMonitorService provideMonitorService;
    @Autowired
    private AlertConfigDAO alertConfigDAO;

    @Autowired
    private DegradationConfigDAO degradationConfigDAO;
    @Autowired
    private ProviderNotifyConfigDAO notifyConfigDAO;

    @Test
    public void providerRecovery(){
        ProviderRecoveryDTO recoveryDTO = new ProviderRecoveryDTO();
        recoveryDTO.setProviderKey("test");
        provideMonitorService.providerRecovery(recoveryDTO);

        ProviderDegradeDTO degradeDTO = new ProviderDegradeDTO();
        degradeDTO.setProviderKey("test");
        provideMonitorService.providerDegrade(degradeDTO);
    }

    @Test
    public void monitorAlertConfig(){
        AlertConfigAddDTO addDTO = new AlertConfigAddDTO();
        addDTO.setAlertId("test_01");
        addDTO.setTargetId("setTargetId");
        addDTO.setTargetType("setTargetType");
        addDTO.setExpression("setExpression");
        addDTO.setExtend("setExtend");
        addDTO.setDesc("desc");
        provideMonitorService.monitorAlertConfigAdd(addDTO);
        AlertConfigDO alertConfigDO = alertConfigDAO.getByAlertId(addDTO.getAlertId());
        Assert.assertEquals(alertConfigDO.getExpression(),addDTO.getExpression());
        Assert.assertEquals(alertConfigDO.getTargetId(),addDTO.getTargetId());
        Assert.assertEquals(alertConfigDO.getExtend(),addDTO.getExtend());

        AlertConfigModifyDTO modifyDTO = new AlertConfigModifyDTO();
        modifyDTO.setAlertId(addDTO.getAlertId());
        modifyDTO.setExtend("set");
        modifyDTO.setExpression("xxxx");
        provideMonitorService.monitorAlertConfigModify(modifyDTO);
        alertConfigDO = alertConfigDAO.getByAlertId(addDTO.getAlertId());
        Assert.assertEquals(alertConfigDO.getExpression(),modifyDTO.getExpression());
        Assert.assertEquals(alertConfigDO.getExtend(),modifyDTO.getExtend());
    }

    @Test
    public void monitorDegradationConfig(){
        DegradationConfigAddDTO addDTO = new DegradationConfigAddDTO();
        addDTO.setConfigId("setConfigId");
        addDTO.setAlertId("test_01");

        addDTO.setChangeAction("ALL");
        addDTO.setScopeAppId("ALL");
        addDTO.setChangeTime(600);
        addDTO.setSwitchEnabled(1);
        addDTO.setDesc("desc");
        provideMonitorService.monitorDegradationConfigAdd(addDTO);
        DegradationConfigDO configDO =  degradationConfigDAO.getByAlertId(addDTO.getAlertId());
        Assert.assertEquals(configDO.getAlertId(),addDTO.getAlertId());
        Assert.assertEquals(configDO.getScope(),addDTO.getScope());
        Assert.assertEquals(configDO.getChangeTime(),addDTO.getChangeTime());

        DegradationConfigModifyDTO modifyDTO = new DegradationConfigModifyDTO();
        modifyDTO.setConfigId(addDTO.getConfigId());
        modifyDTO.setAlertId("test_02");
        modifyDTO.setChangeAction("ALL,222");
        modifyDTO.setScopeAppId("ALL,222");
        modifyDTO.setChangeTime(500);
        modifyDTO.setSwitchEnabled(0);

        provideMonitorService.monitorDegradationConfigModify(modifyDTO);
        configDO = degradationConfigDAO.getByAlertId(modifyDTO.getAlertId());
        Assert.assertEquals(configDO.getAlertId(),modifyDTO.getAlertId());
        Assert.assertEquals(configDO.getScope(),modifyDTO.getScope());
        Assert.assertEquals(configDO.getChangeTime(),modifyDTO.getChangeTime());
    }

    @Test
    public void monitorNotifyConfig(){
        NotifyConfigAddDTO addDTO = new NotifyConfigAddDTO();
        addDTO.setConfigId("setConfigId");
        addDTO.setAlertId("test_01");
        addDTO.setSwitchEnabled(1);
        addDTO.setCallEnabled(1);
        addDTO.setSmsEnabled(0);
        addDTO.setDingCropEnabled(1);
        addDTO.setDingRobotEnabled(1);
        addDTO.setDingRobotToken("desc");
        addDTO.setNotifyUsernames("xdsdsd");
        provideMonitorService.monitorNotifyConfigAdd(addDTO);
        ProviderNotifyConfigDO configDO =  notifyConfigDAO.getByAlertId(addDTO.getAlertId());
        Assert.assertEquals(configDO.getAlertId(),addDTO.getAlertId());
        Assert.assertEquals(configDO.getNotifyUsernames(),addDTO.getNotifyUsernames());
        Assert.assertEquals(configDO.getCallEnabled(),addDTO.getCallEnabled());

        NotifyConfigModifyDTO modifyDTO = new NotifyConfigModifyDTO();
        modifyDTO.setConfigId(addDTO.getConfigId());
        modifyDTO.setAlertId("test_02");
        modifyDTO.setNotifyUsernames("222222");
        modifyDTO.setSwitchEnabled(0);

        provideMonitorService.monitorNotifyConfigAddModify(modifyDTO);
        configDO = notifyConfigDAO.getByAlertId(modifyDTO.getAlertId());
        Assert.assertEquals(configDO.getAlertId(),modifyDTO.getAlertId());
        Assert.assertEquals(configDO.getNotifyUsernames(),modifyDTO.getNotifyUsernames());
        Assert.assertEquals(configDO.getSwitchEnabled(),modifyDTO.getSwitchEnabled());
    }
}
