<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="info_service" physicalName="info_service" remark="info_service">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,INFOAUTH_ID,TYPE,STATUS,OBJECT_TYPE,CREATE_TIME,MODIFY_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.INFOAUTH_ID,sf.TYPE,sf.STATUS,sf.OBJECT_TYPE,sf.CREATE_TIME,sf.MODIFY_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:info_service">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO info_service(
            ID
            ,INFOAUTH_ID
            ,TYPE
            ,STATUS
            ,OBJECT_TYPE
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES(
             null
            , #{infoauthId,jdbcType=VARCHAR}
            , #{type,jdbcType=TINYINT}
            , #{status,jdbcType=TINYINT}
            , #{objectType,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{modifyTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:info_service">
        INSERT INTO info_service(
            ID
            ,INFOAUTH_ID
            ,TYPE
            ,STATUS
            ,OBJECT_TYPE
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.infoauthId,jdbcType=VARCHAR}
                , #{item.type,jdbcType=TINYINT}
                , #{item.status,jdbcType=TINYINT}
                , #{item.objectType,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.modifyTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:info_service">
        <![CDATA[
        UPDATE info_service
        SET
            INFOAUTH_ID     = #{infoauthId,jdbcType=VARCHAR}
            ,TYPE            = #{type,jdbcType=TINYINT}
            ,STATUS          = #{status,jdbcType=TINYINT}
            ,OBJECT_TYPE     = #{objectType,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,MODIFY_TIME     = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:info_service">
        <![CDATA[
        DELETE FROM info_service
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:info_service">
        SELECT *
        FROM info_service
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
</table>
