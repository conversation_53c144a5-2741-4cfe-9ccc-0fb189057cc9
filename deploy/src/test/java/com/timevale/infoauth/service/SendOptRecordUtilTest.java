package com.timevale.infoauth.service;

import com.timevale.component.identity.record.RecordResult;
import com.timevale.component.identity.record.constants.OptProductChild;
import com.timevale.component.identity.record.constants.OptResult;
import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.enums.ProvResult;
import com.timevale.infoauth.service.model.IProviderEnumeration;
import com.timevale.infoauth.service.impl.common.dto.Provout;
import com.timevale.infoauth.service.impl.dto.BankauthProvin;
import com.timevale.infoauth.service.impl.dto.BankCardOcrProvin;
import com.timevale.infoauth.service.impl.dto.BankCardOcrProvout;
import com.timevale.infoauth.service.impl.dto.BankthreeProvin;
import com.timevale.infoauth.service.impl.provider.constants.*;
import com.timevale.infoauth.service.impl.dto.PersonProvin;
import com.timevale.infoauth.service.impl.dto.OcrProvin;
import com.timevale.infoauth.service.impl.dto.OcrProvout;
import com.timevale.infoauth.service.impl.common.dto.OrganProvin;
import com.timevale.infoauth.service.impl.dto.OrgLicenceOcrProvout;
import com.timevale.infoauth.service.impl.dto.OrgLicenseOcrProvin;
import com.timevale.infoauth.service.impl.dto.TelecomauthProvin;
import com.timevale.infoauth.service.impl.utils.SendOptRecordUtil;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2020/4/27 6:59 下午
 */
@JsonDataIgnore
public class SendOptRecordUtilTest extends ApplicationTest {

  private static final String appId = "**********";
  @Resource SendOptRecordUtil sendOptRecordUtil;

  /** 个人供应商二要素 */
  @Test
  public void sendPersonIdNo() {
    PersonProvin input = new PersonProvin();
    input.setBizAppId(appId);
    input.setCertNumber("*********");
    input.setName("项目");
    IProviderEnumeration provider = IdCard2ProviderEnum.JINGZHONG;
    RecordResult recordResult = new RecordResult();
    String provResult = "成功";
    sendOptRecordUtil.sendPersonIdNo(input, provider, recordResult, provResult);
    Assert.assertTrue(true);
    Assert.assertNotNull(recordResult);
  }

  /** 个人运营商三要素 */
  @Test
  public void sendTelecomAuth() {
    TelecomauthProvin input = new TelecomauthProvin();
    input.setBizAppId(appId);
    input.setMobile("*********");
    input.setCertNumber("*********");
    input.setName("项目");
    IProviderEnumeration provider = Telecom3ProviderEnum.JINGZHONG;
    RecordResult recordResult = new RecordResult();
    String provResult = "成功";
    sendOptRecordUtil.sendTelecomAuth(input, provider, recordResult, provResult);
    Assert.assertTrue(true);
    Assert.assertNotNull(recordResult);
  }

  /** 个人银行三要素 */
  @Test
  public void sendPersonBankThree() {
    BankthreeProvin input = new BankthreeProvin();
    input.setBizAppId(appId);
    input.setCardno("*********");
    input.setCertNumber("*********");
    input.setName("项目");
    IProviderEnumeration provider = Bank3ProviderEnum.JINGZHONG;
    RecordResult recordResult = new RecordResult();
    String provResult = "成功";
    sendOptRecordUtil.sendPersonBankThree(input, provider, recordResult, provResult);
    Assert.assertTrue(true);
    Assert.assertNotNull(recordResult);
  }

  /** 个人银行四要素 */
  @Test
  public void sendPersonBankFour() {
    BankauthProvin input = new BankauthProvin();
    input.setMobile("*********");
    input.setCertNumber("*********");
    input.setName("项目");
    input.setBizAppId(appId);
    IProviderEnumeration provider = Bank4ProviderEnum.JINGZHONG;
    RecordResult recordResult = new RecordResult();
    String provResult = "成功";
    sendOptRecordUtil.sendPersonBankFour(input, provider, recordResult, provResult);
    Assert.assertTrue(true);
    Assert.assertNotNull(recordResult);
  }

  /** 身份证OCR */
  @Test
  public void sendPersonOcr() {
    OcrProvin input = new OcrProvin();
    OcrProvout out = new OcrProvout();
    input.setBizAppId(appId);
    IProviderEnumeration provider = OcrProviderEnum.HUAWEI;
    RecordResult recordResult = new RecordResult();
    sendOptRecordUtil.sendPersonOcr(input, out, provider, recordResult);
    Assert.assertTrue(true);
    Assert.assertNotNull(recordResult);
  }

  /** 银行卡OCR */
  @Test
  public void sendBankCardOcr() {
    BankCardOcrProvin input = new BankCardOcrProvin();
    BankCardOcrProvout out = new BankCardOcrProvout();
    input.setBizAppId(appId);
    IProviderEnumeration provider = OcrProviderEnum.HUAWEI;
    RecordResult recordResult = new RecordResult();
    sendOptRecordUtil.sendBankCardOcr(input, out, provider, recordResult);
    Assert.assertTrue(true);
    Assert.assertNotNull(recordResult);
  }

  /** 营业执照OCR */
  @Test
  public void sendOrgLicenseOcr() {
    OrgLicenseOcrProvin input = new OrgLicenseOcrProvin();
    OrgLicenceOcrProvout out = new OrgLicenceOcrProvout();
    input.setBizAppId(appId);
    IProviderEnumeration provider = OcrProviderEnum.HUAWEI;
    RecordResult recordResult = new RecordResult();
    sendOptRecordUtil.sendOrgLicenseOcr(input, out, provider, recordResult);
    Assert.assertTrue(true);
    Assert.assertNotNull(recordResult);
  }

  //    /** 个人活体检测 */
  //    @Test
  //    public void sendPersonFace() {
  //
  //        RecoProvin input = new RecoProvin();
  //        input.setBizAppId(appId);
  //        IProviderEnumeration provider = ProviderPersonCheck.JINGZHONG;
  //        RecordResult recordResult = new RecordResult();
  //        sendOptRecordUtil.sendPersonFace(input, provider, recordResult);
  //    }

  /** 企业信息/企业二要素/企业三要素,律所||社会组织三要素 */
  @Test
  public void sendOrgInfo() {
    OptProductChild productChild = OptProductChild.INFO_OAUTH_ORG_INFO;
    OrganProvin input = new OrganProvin();
    IProviderEnumeration provider = Org3ProviderEnum.QIXINBAO;
    RecordResult recordResult = new RecordResult();
    String provResult = "成功";
    Provout provout = new Provout();
    provout.setProviderResultString(provResult);
    sendOptRecordUtil.sendOrgInfo(productChild, input, provider, recordResult, provout);
    Assert.assertTrue(true);
    Assert.assertNotNull(recordResult);
  }

  /** 企业四要素 */
  @Test
  public void sendOrgInfoFour() {
    OrganProvin input = new OrganProvin();
    RequestContext.put("X-Tsign-Open-App-Id", appId);
    IProviderEnumeration provider = Org4ProviderEnum.JINGZHONG;
    RecordResult recordResult = new RecordResult();
    String provResult = "成功";
    Provout provout = new Provout();
    provout.setProviderResultString(provResult);
    sendOptRecordUtil.sendOrgInfoFour(input, provider, recordResult, provout);
    Assert.assertTrue(true);
    Assert.assertNotNull(recordResult);
  }

  @Test
  public void fillServiceErrorCode() {
    Provout provout = new Provout();
    RecordResult recordResult = new RecordResult();
    provout.setProvResult(ProvResult.SUCCESS);
    sendOptRecordUtil.fillServiceErrorCode(recordResult, provout);
    Assert.assertEquals(recordResult.getServiceProvErrorCode(), OptResult.SUCCESS.getCode());

    provout.setProvResult(ProvResult.FAILURE);
    sendOptRecordUtil.fillServiceErrorCode(recordResult, provout);
    Assert.assertEquals(recordResult.getServiceProvErrorCode(), OptResult.FAIL.getCode());

    provout.setProvResult(ProvResult.TIMEOUT);
    sendOptRecordUtil.fillServiceErrorCode(recordResult, provout);
    Assert.assertEquals(recordResult.getServiceProvErrorCode(), OptResult.TIMEOUT.getCode());

    provout.setProvResult(ProvResult.NOTFOUND);
    sendOptRecordUtil.fillServiceErrorCode(recordResult, provout);
    Assert.assertEquals(recordResult.getServiceProvErrorCode(), OptResult.NOTFOUND.getCode());

    provout.setProvResult(ProvResult.UNKNOWN);
    sendOptRecordUtil.fillServiceErrorCode(recordResult, provout);
    Assert.assertEquals(recordResult.getServiceProvErrorCode(), OptResult.UNKOWN.getCode());
  }
}
