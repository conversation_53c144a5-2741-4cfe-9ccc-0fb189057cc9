<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="info_provider_organ" physicalName="info_provider_organ" remark="info_provider_organ">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,URL,DISC,NAME,SECRET,ACCOUNT,SIGN_KEY,VERIFY_KEY,LEVEL,SIGN_TYPE,PROVIDER_TYPE,CREATE_TIME,MODIFY_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.URL,sf.DISC,sf.NAME,sf.SECRET,sf.ACCOUNT,sf.SIGN_KEY,sf.VERIFY_KEY,sf.LEVEL,sf.SIGN_TYPE,sf.PROVIDER_TYPE,sf.CREATE_TIME,sf.MODIFY_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:info_provider_organ">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO info_provider_organ(
            ID
            ,URL
            ,DISC
            ,NAME
            ,SECRET
            ,ACCOUNT
            ,SIGN_KEY
            ,VERIFY_KEY
            ,LEVEL
            ,SIGN_TYPE
            ,PROVIDER_TYPE
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES(
             null
            , #{url,jdbcType=VARCHAR}
            , #{disc,jdbcType=VARCHAR}
            , #{name,jdbcType=VARCHAR}
            , #{secret,jdbcType=VARCHAR}
            , #{account,jdbcType=VARCHAR}
            , #{signKey,jdbcType=LONGVARCHAR}
            , #{verifyKey,jdbcType=LONGVARCHAR}
            , #{level,jdbcType=TINYINT}
            , #{signType,jdbcType=TINYINT}
            , #{providerType,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{modifyTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:info_provider_organ">
        INSERT INTO info_provider_organ(
            ID
            ,URL
            ,DISC
            ,NAME
            ,SECRET
            ,ACCOUNT
            ,SIGN_KEY
            ,VERIFY_KEY
            ,LEVEL
            ,SIGN_TYPE
            ,PROVIDER_TYPE
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.url,jdbcType=VARCHAR}
                , #{item.disc,jdbcType=VARCHAR}
                , #{item.name,jdbcType=VARCHAR}
                , #{item.secret,jdbcType=VARCHAR}
                , #{item.account,jdbcType=VARCHAR}
                , #{item.signKey,jdbcType=LONGVARCHAR}
                , #{item.verifyKey,jdbcType=LONGVARCHAR}
                , #{item.level,jdbcType=TINYINT}
                , #{item.signType,jdbcType=TINYINT}
                , #{item.providerType,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.modifyTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:info_provider_organ">
        <![CDATA[
        UPDATE info_provider_organ
        SET
            URL             = #{url,jdbcType=VARCHAR}
            ,DISC            = #{disc,jdbcType=VARCHAR}
            ,NAME            = #{name,jdbcType=VARCHAR}
            ,SECRET          = #{secret,jdbcType=VARCHAR}
            ,ACCOUNT         = #{account,jdbcType=VARCHAR}
            ,SIGN_KEY        = #{signKey,jdbcType=LONGVARCHAR}
            ,VERIFY_KEY      = #{verifyKey,jdbcType=LONGVARCHAR}
            ,LEVEL           = #{level,jdbcType=TINYINT}
            ,SIGN_TYPE       = #{signType,jdbcType=TINYINT}
            ,PROVIDER_TYPE   = #{providerType,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,MODIFY_TIME     = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:info_provider_organ">
        <![CDATA[
        DELETE FROM info_provider_organ
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:info_provider_organ">
        SELECT *
        FROM info_provider_organ
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
</table>
