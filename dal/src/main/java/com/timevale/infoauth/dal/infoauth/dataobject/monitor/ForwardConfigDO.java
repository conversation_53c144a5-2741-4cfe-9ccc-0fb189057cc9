package com.timevale.infoauth.dal.infoauth.dataobject.monitor;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/12/2
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForwardConfigDO extends ToString {

    /** id 自增主键. */
    private Long id;
    /** 降级配置ID. */
    private String configId;
    /** 告警配置ID */
    private String alertId;

    /** 配置生效开关[0关闭,1开启] */
    private Integer switchEnabled;

    /** 转发目标Topic */
    private String topicName;
    /** 转发目标tag */
    private String topicTag;

    private String desc;
    /** createTime 记录创建时间. */
    private Date createTime;
    /** modifyTime 记录修改时间. */
    private Date modifyTime;
}
