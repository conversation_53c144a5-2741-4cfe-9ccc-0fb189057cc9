```markdown
# 银行卡要素校验(多证件版)技术规范文档 v2.0

## 1.1 文档简介

本文档是银行卡二三四要素校验服务接口规范，主要为该服务的需求客户描述接口的连接方式、通讯协议、报文格式等技术约定，以及相关的数据产品定义和使用说明。

## 1.2 接口详情

- **请求地址**: `https://123.56.221.164/api/v2/bankcardverify`
- **请求方法**: `POST`
- **请求及响应报文类型**: `application/json`

## 1.3 请求参数

| 参数名  | 类型   | 是否必填 | 参数说明 |
|---------|--------|----------|----------|
| APPID   | string | 是       | 12 位账号（平台发放） |
| type    | string | 是       | 请求类型，详见 1.5 请求类型 |
| name    | string | 是       | 姓名 |
| idcard  | string | 否       | 证件号，三要素校验请求必填 |
| cardno  | string | 是       | 银行卡号 |
| mobile  | string | 否       | 手机号码，四要素校验请求必填 |
| cardtype| string | 否       | 证件类型，`idcard` 传入时，`cardtype` 必填，详见 1.6 证件类型 |

## 1.4 请求示例

```
json
{
"APPID": "************",
"type": "0203",
"name": "张三",
"idcard": "350011************",
"cardno": "62100*************",
"mobile": "188****0110",
"cardtype": "01"
}
```
## 1.5 请求类型

| 请求类型 | 类型编码 | 校验要素 |
|----------|----------|----------|
| 二要素校验 | 0201     | 银行卡号 + 姓名 |
| 三要素校验 | 0202     | 银行卡号 + 姓名 + 身份证号 |
| 四要素校验 | 0203     | 银行卡号 + 姓名 + 身份证号 + 手机号 |

## 1.6 证件类型

| 证件类型 | 类型编码 | 校验要素 |
|----------|----------|----------|
| 身份证   | 01       | 支持     |
| 军官证   | 02       | 支持     |
| 护照     | 03       | 支持     |
| 户口簿   | 04       | 暂不支持 |
| 士兵证   | 05       | 支持     |
| 内地居民来往港澳通行证 | 06 | 支持 |
| 台湾居民来往内地通行证 | 07 | 支持 |
| 临时身份证 | 08      | 暂不支持 |
| 外国人永久居住证 | 09 | 支持 |
| 警官证   | 10       | 支持     |
| 营业执照 | 11       | 暂不支持 |
| 组织商户代码证 | 12 | 暂不支持 |
| 税务登记证 | 13      | 暂不支持 |
| 统一社会信用代码证 | 14 | 暂不支持 |
| 港澳居民来往内地通行证（回乡证） | 15 | 支持 |
| 驾驶证   | 16       | 暂不支持 |
| 学生证   | 17       | 暂不支持 |
| 台湾居民居住证 | 18 | 支持 |
| 港澳居民居住证 | 19 | 支持 |
| 外国护照 | 20       | 支持     |
| 旅行者   | 21       | 支持     |
| 出入境通行证 | 22 | 支持 |

## 1.7 响应报文参数

| 参数名   | 类型   | 参数说明 |
|----------|--------|----------|
| request_id | string | 请求流水号 |
| status  | string | 状态码 |
| message | string | 响应状态描述 |

## 1.8 响应报文示例

```
json
{
"request_id": "请求流水号",
"status": "200",
"message": "信息一致"
}
```
## 1.9 响应状态说明

| 状态码 | 状态描述 | 是否计费 |
|--------|----------|----------|
| 200    | 信息一致 | 是       |
| 201    | 信息不一致 | 是       |
| 202    | 无效卡号或卡号不存在，请换卡重试或联系发卡行 | 是       |
| 203    | 卡状态异常，请换卡重试或联系发卡行 | 是       |
| 204    | 受限制的卡，请换卡重试或联系发卡行 | 是       |
| 205    | 交易失败，详情请咨询您的发卡行 | 否       |
| 100    | 参数不正确或不规范 | 否       |
| 300    | 系统繁忙，请稍后重试 | 否       |
| 400    | AppID 无效或服务未开通 | 否       |
| 401    | IP 未报备白名单或签名失败 | 否       |
| 402    | 账户授信额度已用尽或服务已过期 | 否       |
| 403    | 交易限流或查询过于频繁，请稍后重试 | 否       |
| 404    | 当日交易总量已达上限，于次日 0 点恢复 | 否       |

希望这份 `README.md` 文件能帮助您更好地理解和使用运营商三要素校验服务。如果有任何问题或建议，请随时联系我们。

```


