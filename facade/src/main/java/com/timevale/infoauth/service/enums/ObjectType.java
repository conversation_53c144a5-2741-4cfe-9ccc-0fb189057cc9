package com.timevale.infoauth.service.enums;

/**
 * 类名：ObjectType.java <br>
 * 功能说明：存证对象唯一标识 <br>
 * 修改历史： <br>
 * 1.[2016年11月14日下午2:35:24]创建类 by Administrator
 */
public enum ObjectType {

  /** 身份证标示唯一性的用户 */
  CODE_PERSON_IDCARD((short) 1),

  /** 组织机构代码/社会统一信用代码标示唯一性的用户 */
  CODE_ORGAN_CODE((short) 2),

  ORG_CODE_USC((short) 11),

  ORG_CODE_REG((short) 12),

  ORG_CODE_ORG((short) 13),

  /** 注册号标示唯一性的用户 */
  CODE_ORGAN_REGCODE((short) 11),

  /** 第三方自定义代码 */
  CODE_THIRD_CUSTOM_CODE_1((short) 101),

  UNKNOWN((short) 0);

  public short dbValue() {
    return dbValue;
  }

  ObjectType(short dbValue) {
    this.dbValue = dbValue;
  }

  private short dbValue;

  public static ObjectType from(short dbValue) {

    for (ObjectType i : ObjectType.values()) {
      if (i.dbValue == dbValue) {
        return i;
      }
    }

    return UNKNOWN;
  }
}
