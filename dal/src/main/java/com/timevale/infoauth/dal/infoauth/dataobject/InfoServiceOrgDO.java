package com.timevale.infoauth.dal.infoauth.dataobject;

import lombok.Data;

import java.util.Date;

/**
 * The table. 企业实名认证入参表
 *
 * <AUTHOR> Kunpeng
 */
@Data
public class InfoServiceOrgDO extends InfoServiceAuthDO {

  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;
  /** name 企业名称. */
  private String name;
  /** codeorg 企业组织机构代码. */
  private String codeorg;
  /** codereg 工商注册号. */
  private String codereg;
  /** codeusc 企业社会统一信用代码. */
  private String codeusc;
  /** legalName 法人名字. */
  private String legalName;
  /** 法人身份证. */
  private String legalIdno;
  /** 法人证件类型. */
  private Integer legalCertType;
  /** infoauthId 信息比对服务id. */
  private String infoauthId;
  /** billorderId 计费订单ID. */
  private String billorderId;
  /** timestampId 时间戳记录ID. */
  private String timestampId;
  /** ServiceType */
  private Integer type;
  /** appId */
  private String appId;
  /** id ID. */
  private Integer id;
  /** result 适配结果. */
  private Integer result;
  /** legalArea 企业法人国籍. */
  private Short legalArea;
  /** serviceStatus 记录状态. */
  private Integer serviceStatus;
  private Long errId;
}
