<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="provider_err_msg_template" physicalName="provider_err_msg_template" remark="供应商错误msg模板表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,P_CODE,EXTEND,PROVIDER,P_MSG_REGEX,P_MSG_REPLACE,BIZ_TYPE,CREATE_TIME,MODIFY_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.P_CODE,sf.EXTEND,sf.PROVIDER,sf.P_MSG_REGEX,sf.P_MSG_REPLACE,sf.BIZ_TYPE,sf.CREATE_TIME,sf.MODIFY_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:provider_err_msg_template">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO provider_err_msg_template(
            ID
            ,P_CODE
            ,EXTEND
            ,PROVIDER
            ,P_MSG_REGEX
            ,P_MSG_REPLACE
            ,BIZ_TYPE
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES(
             null
            , #{pCode,jdbcType=VARCHAR}
            , #{extend,jdbcType=VARCHAR}
            , #{provider,jdbcType=VARCHAR}
            , #{pMsgRegex,jdbcType=VARCHAR}
            , #{pMsgReplace,jdbcType=VARCHAR}
            , #{bizType,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{modifyTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:provider_err_msg_template">
        INSERT INTO provider_err_msg_template(
            ID
            ,P_CODE
            ,EXTEND
            ,PROVIDER
            ,P_MSG_REGEX
            ,P_MSG_REPLACE
            ,BIZ_TYPE
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.pCode,jdbcType=VARCHAR}
                , #{item.extend,jdbcType=VARCHAR}
                , #{item.provider,jdbcType=VARCHAR}
                , #{item.pMsgRegex,jdbcType=VARCHAR}
                , #{item.pMsgReplace,jdbcType=VARCHAR}
                , #{item.bizType,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.modifyTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:provider_err_msg_template">
        <![CDATA[
        UPDATE provider_err_msg_template
        SET
            P_CODE          = #{pCode,jdbcType=VARCHAR}
            ,EXTEND          = #{extend,jdbcType=VARCHAR}
            ,PROVIDER        = #{provider,jdbcType=VARCHAR}
            ,P_MSG_REGEX     = #{pMsgRegex,jdbcType=VARCHAR}
            ,P_MSG_REPLACE   = #{pMsgReplace,jdbcType=VARCHAR}
            ,BIZ_TYPE        = #{bizType,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,MODIFY_TIME     = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:provider_err_msg_template">
        <![CDATA[
        DELETE FROM provider_err_msg_template
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:provider_err_msg_template">
        SELECT *
        FROM provider_err_msg_template
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxBizType" multiplicity="many" remark="根据普通索引IdxBizType获取数据:provider_err_msg_template">
        SELECT *
        FROM provider_err_msg_template
        WHERE
        <![CDATA[
            BIZ_TYPE        = #{bizType,jdbcType=TINYINT}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxPCode" multiplicity="many" remark="根据普通索引IdxPCode获取数据:provider_err_msg_template">
        SELECT *
        FROM provider_err_msg_template
        WHERE
        <![CDATA[
            P_CODE          = #{pCode,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxProvider" multiplicity="many" remark="根据普通索引IdxProvider获取数据:provider_err_msg_template">
        SELECT *
        FROM provider_err_msg_template
        WHERE
        <![CDATA[
            PROVIDER        = #{provider,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>
