package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.ProviderErrMsgTemplateDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/** 供应商错误msg模板表 */
public interface ProviderErrMsgTemplateDAO {

  @Select({
    "<script>",
    "SELECT `biz_type`,`provider`,`p_code`,`p_msg_regex`,`p_msg_replace` FROM `provider_err_msg_template`",
    " WHERE `biz_type`=#{serviceType} AND `provider`=#{provider} AND`p_code`=#{code}",
    "</script>",
  })
  List<ProviderErrMsgTemplateDO> getListByProviderCode(
      @Param("serviceType") Integer serviceType,
      @Param("provider") String provider,
      @Param("code") String code);
}
