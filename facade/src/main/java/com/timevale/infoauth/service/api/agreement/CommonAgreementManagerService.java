package com.timevale.infoauth.service.api.agreement;

import com.timevale.infoauth.service.model.SupportResult;
import com.timevale.infoauth.service.request.agreement.*;
import com.timevale.infoauth.service.response.agreement.CommonAgreementPageListResponse;
import com.timevale.infoauth.service.response.agreement.CommonAgreementReleaseHistoryResponse;
import com.timevale.mandarin.common.annotation.RestClient;

/**
 * 通用协议管理api
 * <AUTHOR>
 * @since 2024/12/3 10:57
 */
@RestClient(serviceId = "infoauth-service")
public interface CommonAgreementManagerService {

    /**
     * 通用协议服务- 获取协议列表
     * @param request
     * @return
     */
    SupportResult<CommonAgreementPageListResponse>
    queryAgreementPageList(CommonAgreementListQueryRequest request);


    /**
     * 通用协议服务- 查询发布协议版本记录
     * @param request
     * @return 协议编号
     */
    SupportResult<CommonAgreementReleaseHistoryResponse> queryAgreementReleaseHistory(CommonAgreementReleaseHistoryQueryRequest request);


    /**
     * 通用协议服务- 新增协议信息
     * @param request
     * @return 协议编号
     */
    SupportResult<String> addAgreement(CommonAgreementInfoAddRequest request);


    /**
     * 通用协议服务- 删除协议信息(发布之后的协议不允许删除)
     * @param request
     * @return 协议编号
     */
    SupportResult<Boolean> delAgreement(CommonAgreementInfoDelRequest request);


    /**
     * 通用协议服务- 修改协议信息
     * @param request
     * @return 协议编号
     */
    SupportResult<Boolean> modifyAgreement(CommonAgreementInfoModifyRequest request);

    /**
     * 通用协议服务- 改变协议状态（发布之后的协议不能改变状态，只能编辑信息）
     * @param request
     * @return 协议编号
     */
    SupportResult<Boolean> changeAgreementStatus(CommonAgreementChangeStatusRequest request);


    /**
     * 通用协议服务- 协议内容-新增协议内容
     * @param request
     * @return 协议编号
     */
    SupportResult<String> agreementContentAdd(CommonAgreementContentAddRequest request);

    /**
     * 通用协议服务- 协议内容-修改协议内容
     * @param request
     * @return 协议编号
     */
    SupportResult<Boolean> agreementContentModify(CommonAgreementContentModifyRequest request);


    /**
     * 通用协议服务- 协议内容-发布协议内容
     * @param request
     * @return 协议编号
     */
    SupportResult<Boolean> agreementContentChangeStatus(CommonAgreementContentStatustChangeRequest request);
}
