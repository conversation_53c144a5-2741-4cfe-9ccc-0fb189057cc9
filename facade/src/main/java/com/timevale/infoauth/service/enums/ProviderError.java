package com.timevale.infoauth.service.enums;

public enum ProviderError {
  Provider_RequestErr(100001, "请求供应商失败");
  private int code;
  private String msg;

  private ProviderError(int code, String msg) {
    this.code = code;
    this.msg = msg;
  }

  public int getCode() {
    return code;
  }

  public void setCode(int code) {
    this.code = code;
  }

  public String getMsg() {
    return msg;
  }

  public void setMsg(String msg) {
    this.msg = msg;
  }
}
