package com.timevale.infoauth.adaptors;

import com.alibaba.fastjson.JSON;
import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.enums.InfoAuthServiceType;
import com.timevale.infoauth.service.impl.common.dto.OrganProvin;
import com.timevale.infoauth.service.impl.common.dto.Provout;
import com.timevale.infoauth.service.impl.constants.ProviderEnum;
import com.timevale.infoauth.service.impl.dto.BankthreeProvin;
import com.timevale.infoauth.service.impl.dto.EnterpriseAccurateQueryProvin;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.Bank3Adaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.EnterpriseAccurateCertNoQueryAdaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.Org3Adaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.Org4Adaptors;
import com.timevale.infoauth.service.impl.provider.biz.api.XReceiver;
import com.timevale.infoauth.service.impl.provider.biz.dto.Config;
import com.timevale.infoauth.service.impl.provider.client.core.auth.ICredential;
import com.timevale.infoauth.service.impl.provider.client.core.auth.MoreKeyCredentials;
import com.timevale.infoauth.service.impl.provider.client.shucifang.ShuCiFangClient;
import com.timevale.infoauth.service.impl.provider.client.shumai.ShumaiIdCard2ElementClient;
import com.timevale.infoauth.service.impl.provider.client.wangdian.WangdianMobile3ElementClient;
import com.timevale.infoauth.service.impl.provider.constants.Bank3ProviderEnum;
import com.timevale.infoauth.service.impl.provider.dto.QXBHaSocialorg3ElementRequest;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2025/2/6 09:38
 */
@JsonDataIgnore
public class Md5Test extends ApplicationTest {

    private static final String name = "熊佳";
    private static final String psnCardNo = "36220219950130352X";


    @Test
    public void testBank3Adaptors() {
        final Bank3Adaptors adaptors = new Bank3Adaptors();
        XReceiver xReceiver = adaptors.WANGDIAN_bankthreeValid;
        BankthreeProvin provin = new BankthreeProvin();
        provin.setCardno("11u8912312");
        provin.setName(name);
        provin.setCertNumber(psnCardNo);
        provin.setProviderName(Bank3ProviderEnum.WANGDIAN.name());
        Provout apply = xReceiver.apply(provin);
        logger.info(" receiver apply: {}" + JSON.toJSONString(apply));
        Assert.assertNotNull(apply);
    }

    @Test
    public void testEnterpriseAccurateCertNoQueryAdaptors() {
        final EnterpriseAccurateCertNoQueryAdaptors adaptors = new EnterpriseAccurateCertNoQueryAdaptors();
        XReceiver xReceiver = adaptors.qichacha_identityauthvalid;
        EnterpriseAccurateQueryProvin provin = new EnterpriseAccurateQueryProvin();
        provin.setCertNumber("913301087458306077");
        provin.setServiceType(InfoAuthServiceType.REGISTRATION_NO_3_ELEMENT);
        Provout apply = xReceiver.apply(provin);
        logger.info(" receiver apply: {}" + JSON.toJSONString(apply));
        Assert.assertNotNull(apply);

        provin.setCertNumber("*********");
        Provout apply1 = xReceiver.apply(provin);
        logger.info(" receiver apply: {}" + JSON.toJSONString(apply1));
        Assert.assertNotNull(apply1);
    }

    @Test
    public void testOrg3Adaptors() {
        final Org3Adaptors adaptors = new Org3Adaptors();
        XReceiver xReceiver = adaptors.YOU_SHU_ThreeElement;
        OrganProvin provin = new OrganProvin();
        provin.setProviderName(ProviderEnum.YOU_SHU.name());
        provin.setServiceType(InfoAuthServiceType.ENTERPRISE_3_ELEMENT);
        provin.setOrgName("新会区星恒汽车维修中心");
        provin.setCertNumber("92440705MA55F4M541");
        provin.setLegalRepName("陈福传");
        Provout apply = xReceiver.apply(provin);
        logger.info(" receiver apply: {}" + JSON.toJSONString(apply));
        Assert.assertNotNull(apply);

        provin.setOrgName("四川今点企业管理咨询服务有限公司");
        provin.setCertNumber("91510107MA7GKH7F76");
        provin.setLegalRepName("李康艳");
        Provout apply1 = xReceiver.apply(provin);
        logger.info(" receiver apply: {}" + JSON.toJSONString(apply1));
        Assert.assertNotNull(apply1);
    }

    @Test
    public void testOrg4Adaptors() {
        final Org4Adaptors adaptors = new Org4Adaptors();
        XReceiver xReceiver = adaptors.YOU_SHU_info4;
        OrganProvin provin = new OrganProvin();
        provin.setProviderName(ProviderEnum.YOU_SHU.name());
        provin.setServiceType(InfoAuthServiceType.ENTERPRISE_4_ELEMENT);
        provin.setOrgName("新会区星恒汽车维修中心");
        provin.setCertNumber("92440705MA55F4M541");
        provin.setLegalRepName("陈福传");
        provin.setLegalRepCertNo("440782199408302818");
        Provout apply = xReceiver.apply(provin);
        logger.info(" receiver apply: {}" + JSON.toJSONString(apply));
        Assert.assertNotNull(apply);

        provin.setOrgName("四川今点企业管理咨询服务有限公司");
        provin.setCertNumber("91510107MA7GKH7F76");
        provin.setLegalRepName("李康艳");
        provin.setLegalRepCertNo("510727199302040825");
        Provout apply1 = xReceiver.apply(provin);
        logger.info(" receiver apply: {}" + JSON.toJSONString(apply1));
        Assert.assertNotNull(apply1);
    }

    @Test
    public void testQXBHaSocialorg3ElementRequestBuildRequestHead() {
        final QXBHaSocialorg3ElementRequest request = new QXBHaSocialorg3ElementRequest();
        final ICredential iCredential = new MoreKeyCredentials("1", "2");
        final Map<String, String> apply = request.buildRequestHead("https://www.baidu.com/", iCredential);
        logger.info(" receiver apply: {}" + JSON.toJSONString(apply));
        Assert.assertNotNull(apply);
    }


    @Test
    public void testTelecomauthValid() {
        Provout apply = WangdianMobile3ElementClient.telecomauthValid("bizId", name,
                psnCardNo, "13123919273", new Config());
        logger.info(" receiver apply: {}" + JSON.toJSONString(apply));
        Assert.assertNotNull(apply);
    }

    @Test
    public void testShumaiIdCard2ElementClientGetSign() {
        final String apply = ShumaiIdCard2ElementClient.getSign("x", System.currentTimeMillis() + "", "2");
        logger.info(" receiver apply: {}" + JSON.toJSONString(apply));
        Assert.assertNotNull(apply);

    }

    @Test
    public void testShuCiFangClientBuildUrl() {
        final String apply = ShuCiFangClient.buildUrl(name, new HashMap<>(), new Config());
        logger.info(" receiver apply: {}" + JSON.toJSONString(apply));
        Assert.assertNotNull(apply);
    }
}
