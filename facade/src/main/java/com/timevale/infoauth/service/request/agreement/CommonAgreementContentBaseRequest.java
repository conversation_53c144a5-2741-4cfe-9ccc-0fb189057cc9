package com.timevale.infoauth.service.request.agreement;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;


/**
 * 通用协议查询
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CommonAgreementContentBaseRequest extends ToString {

  /** 协议发布记录ID. */
  private String releaseRecordId;


}
