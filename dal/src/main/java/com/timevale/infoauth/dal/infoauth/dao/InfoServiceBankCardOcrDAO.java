package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceBankCardOcrDO;
import org.apache.ibatis.annotations.*;

public interface InfoServiceBankCardOcrDAO {

  @Insert(
      "INSERT INTO info_service_bank_card_ocr "
          + "(`id`, `infoauth_id`, `front_img_key`, `create_time`, `modify_time`) "
          + "VALUES "
          + "(#{id}, #{infoauthId}, #{frontImgKey}, now(), now())")
  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  int insert(InfoServiceBankCardOcrDO entity);

  @Update("update info_service_bank_card_ocr set service_status=#{status} where id = #{id}")
  void updateStatus(@Param("id") Integer id, @Param("status") Integer status);

  @Update(
      "update info_service_bank_card_ocr set "
          + "ocr_bank_card_no=#{ocrBankCardNo}, "
          + "ocr_bank_name=#{ocrBankName}, "
          + "ocr_bank_card_type=#{ocrBankCardType}, "
          + "`result`=#{result}, "
          + "service_status=#{serviceStatus}, "
          + "provider_id=#{providerId} "
          + "where id = #{id}")
  void updateBankCardOcrResult(InfoServiceBankCardOcrDO entity);

  @Select("select * from info_service_bank_card_ocr where infoauth_id = #{infoauthId}")
  InfoServiceBankCardOcrDO queryByInfoAuthId(@Param("infoauthId") String infoauthId);
}
