package com.timevale.infoauth.service.api;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.dal.infoauth.dao.InfoBackstageRouteDAO;
import com.timevale.infoauth.dal.infoauth.dao.InfoProviderDomainDAO;
import com.timevale.infoauth.dal.infoauth.dataobject.InfoBackstageRouteDO;
import com.timevale.infoauth.dal.infoauth.dataobject.InfoProviderDomainDO;
import com.timevale.infoauth.dal.infoauth.dataobject.domain.DomainClientDO;
import com.timevale.infoauth.service.enums.InfoAuthServiceType;
import com.timevale.infoauth.service.impl.biz.dispatcher.RouteControl;
import com.timevale.infoauth.service.impl.utils.ModelUtilX;
import com.timevale.infoauth.service.model.SupportResult;
import com.timevale.infoauth.service.request.backstage.*;
import com.timevale.infoauth.service.response.backstage.GetErrMsgPageListResponse;
import com.timevale.infoauth.service.response.backstage.ProviderDataList;
import com.timevale.infoauth.service.response.backstage.ProviderList;
import com.timevale.infoauth.service.response.backstage.QueryOrgMockResponse;
import com.timevale.infoauth.service.response.backstage.RouteConfigResponse;
import com.timevale.infoauth.service.response.backstage.RouteProviderConfigResponse;
import com.timevale.infoauth.service.response.backstage.ServiceTypeMapper;
import com.timevale.infoauth.utils.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@JsonDataIgnore
public class InfoauthBackServiceTest extends ApplicationTest {
  @Resource
  InfoauthBackStage infoauthBackStage;

//  @Resource InfoBackstageRouteInnerService infoBackstageRouteInnerService;

  @Resource
  private RouteControl routeControl;

  @Resource
  InfoBackstageRouteDAO infoBackstageRouteDAO;

  @Resource
  private InfoProviderDomainDAO infoProviderDomainDAO;

  @Test
  public void configTest() {
    RouteProviderConfigResponse routeProviderConfigResponse0 = infoauthBackStage.providerConfig();
    Assert.assertNotNull(routeProviderConfigResponse0);
    Assert.assertTrue(CollectionUtils.isNotEmpty(routeProviderConfigResponse0.getProviderList()));
  }

  @Test
  public void test2() {
    ProviderDataList result = infoauthBackStage.queryProviderData();
    Assert.assertNotNull(result);
    Assert.assertTrue(result.getData().size() > 0);
  }

  @Test
  public void test3() {
    ServiceTypeMapper result = infoauthBackStage.serviceTypeMappers();
    Assert.assertNotNull(result);
    Assert.assertTrue(result.getData().size() > 0);
  }

  @Test
  public void test8() {
    ProviderList result = infoauthBackStage.queryProviderDataNoDesensitization(107, 1);
    Assert.assertNotNull(result);
    Assert.assertTrue(CollectionUtils.isNotEmpty(result.getInfoProviderResponseList()));
  }

  @Test
  public void test9() {
    ProviderList result = infoauthBackStage.queryPayProviderList();
    Assert.assertNotNull(result);
    Assert.assertTrue(CollectionUtils.isNotEmpty(result.getInfoProviderResponseList()));
  }

  @Test
  public void createFlowTest1() throws Exception {
    AddConfigRequest request = new AddConfigRequest();
    request.setName("abc");

    List<AddConfigRequest> list = Arrays.asList(request);
    List<String> result = ModelUtilX.toKeyListStringTest(list, "name");
    Assert.assertNotNull(result);
    Assert.assertTrue(result.size() > 0);
  }

  @Test
  public void getErrMsgPageList() {
    GetErrMsgPageListRequest request = new GetErrMsgPageListRequest();
    request.setPageIndex(1);
    request.setPageSize(10);
    GetErrMsgPageListResponse getErrMsgPageListResponse =
        infoauthBackStage.getErrMsgPageList(request);
    Assert.assertNotNull(getErrMsgPageListResponse);
    Assert.assertTrue(getErrMsgPageListResponse.getCount() > 0);
  }

  @Test
  public void modifyAdapterErrMsgById() {
    ModifyErrMsgRequest request = new ModifyErrMsgRequest();
    request.setId(15L);
    request.setAdapterErrMsg("法人姓名不一致");
    infoauthBackStage.modifyAdapterErrMsgById(request);
    Assert.assertTrue(true);
    Assert.assertEquals(request.getAdapterErrMsg(), "法人姓名不一致");
  }

  @Test
  public void removeErrMsgById() {
    long id = 16L;
    infoauthBackStage.removeErrMsgById(id);
    Assert.assertTrue(true);
    Assert.assertEquals(id, 16L);
  }

//  @Test
//  public void insertTest() {
//    InfoBackstageRouteDO entity = new InfoBackstageRouteDO();
//    entity.setCertNo("AW2522722MA6J5ENP1D");
//    entity.setVerifyType(1);
//    entity.setLostTime(new Date(System.currentTimeMillis() + 5000));
//    entity.setName("UnitTest");
//    entity.setProvider("TEST");
//    infoBackstageRouteInnerService.insert(entity);
//    Assert.assertTrue(true);
//    infoBackstageRouteInnerService.deleteById(
//        entity.getId(), entity.getCertNo(), entity.getVerifyType());
//    Assert.assertTrue(true);
//  }

//  @Test
//  public void loadTest() {
//    infoBackstageRouteInnerService.load();
//    Assert.assertTrue(true);
//  }

  @Test
  public void routeControlTest() {
    String certNo = "test123";
    String result =
        routeControl.isChosen(null, certNo, InfoAuthServiceType.ENTERPRISE_4_ELEMENT.dbValue());
    Assert.assertNull(result);
    Assert.assertEquals(certNo, "test123");
  }

  @Test
  public void routeControlTest1() {
    try {
      InfoBackstageRouteDO entity = new InfoBackstageRouteDO();
      entity.setCertNo("AW2522722MA6J5ENP1D");
      entity.setVerifyType(3);
      entity.setLostTime(new Date(System.currentTimeMillis() + 500000));
      entity.setName("UnitTest");
      entity.setProvider("TEST");
      routeControl.addConfig(
          entity.getName(),
          entity.getCertNo(),
          entity.getVerifyType(),
          entity.getProvider(),
          entity.getLostTime().getTime());
      Assert.assertTrue(true);
      Assert.assertEquals(entity.getCertNo(), "AW2522722MA6J5ENP1D");
    } catch (Exception e) {

    }
  }

  @Test
  public void routeControlTest2() {
    try {
      InfoBackstageRouteDO entity = new InfoBackstageRouteDO();
      entity.setCertNo("AW2522722MA6J5ENP1D");
      entity.setVerifyType(3);
      entity.setLostTime(new Date(System.currentTimeMillis() + 500000));
      entity.setName("UnitTest");
      entity.setProvider("TEST");
      int result =
          infoBackstageRouteDAO.getListCountByLostTime(
              entity.getName(),
              entity.getCertNo(),
              entity.getVerifyType(),
              new Timestamp(System.currentTimeMillis()));
      Assert.assertEquals(result, 0);
      Assert.assertEquals(entity.getCertNo(), "AW2522722MA6J5ENP1D");
    } catch (Exception e) {

    }
  }

  @Test
  public void routeListSelectTest() {
    RouteSelectRequest routeSelectRequest = new RouteSelectRequest();
    routeSelectRequest.setServiceTypeValue(InfoAuthServiceType.ENTERPRISE_3_ELEMENT.dbValue());
    routeSelectRequest.setPageSize(60);
    RouteConfigResponse result = infoauthBackStage.routeListSelect(routeSelectRequest);
    Assert.assertNotNull(result);
    Assert.assertTrue(result.getTotal() > 0);
  }

//  @Test
  public void routeSelect() {
    RouteInfoAuthRequest routeSelectRequest = new RouteInfoAuthRequest();
    routeSelectRequest.setVerifyType(InfoAuthServiceType.ENTERPRISE_3_ELEMENT.dbValue());
    routeSelectRequest.setCertNo("92522722MA6J5ENPDD");
    RouteInfoAuthResponse result = infoauthBackStage.routeSelect(routeSelectRequest);
    Assert.assertNotNull(result);
    Assert.assertFalse(result.isExpired());

    routeSelectRequest.setCertNo("111111111111111111");
    result = infoauthBackStage.routeSelect(routeSelectRequest);
    Assert.assertNotNull(result);
    Assert.assertFalse(result.isExpired());
  }

//  @Test
  public void routeSelectAny() {
    RouteInfoAuthManyRequest routeSelectRequest = new RouteInfoAuthManyRequest();
    routeSelectRequest.setVerifyTypes(Arrays.asList(InfoAuthServiceType.LAWFIRM_3_ELEMENT.dbValue(), InfoAuthServiceType.ENTERPRISE_3_ELEMENT.dbValue()));
    routeSelectRequest.setCertNo("92522722MA6J5ENPDD");
    RouteInfoAuthResponse result = infoauthBackStage.routeSelectAny(routeSelectRequest);
    Assert.assertNotNull(result);
    Assert.assertFalse(result.isExpired());

    routeSelectRequest.setCertNo("111111111111111111");
    result = infoauthBackStage.routeSelectAny(routeSelectRequest);
    Assert.assertNotNull(result);
    Assert.assertFalse(result.isExpired());
  }

  @Test
  public void createOrgMockTest() {
    CreateOrgMockRequest request = new CreateOrgMockRequest();
    Assert.assertFalse(infoauthBackStage.createOrgMock(request).ifSuccess());
    request.setAuthType(InfoAuthServiceType.BANKCARD_4_ELEMENT.name());
    Assert.assertFalse(infoauthBackStage.createOrgMock(request).ifSuccess());
    List<OrgMockDataRequest> mockList = new ArrayList<>();
    OrgMockDataRequest orgMockDataRequest = new OrgMockDataRequest();
    mockList.add(orgMockDataRequest);
    request.setMockList(mockList);
    request.setLostTime(System.currentTimeMillis() - 5000);
    Assert.assertFalse(infoauthBackStage.createOrgMock(request).ifSuccess());
    request.setLostTime(System.currentTimeMillis() + 50000);
    Assert.assertFalse(infoauthBackStage.createOrgMock(request).ifSuccess());
    request.setAuthType(InfoAuthServiceType.ENTERPRISE_3_ELEMENT.name());
    orgMockDataRequest.setCertNo("1111111111111111111111111111111111111");
    Assert.assertFalse(infoauthBackStage.createOrgMock(request).ifSuccess());
    orgMockDataRequest.setName(
        "esigntest11111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111");
    Assert.assertFalse(infoauthBackStage.createOrgMock(request).ifSuccess());
    orgMockDataRequest.setName("esigntest" + System.currentTimeMillis());
    Assert.assertFalse(infoauthBackStage.createOrgMock(request).ifSuccess());
    Long id = createOrgMock();
    Assert.assertNotNull(id);
    infoauthBackStage.deleteOrgMock(id);
  }

  @Test
  public void queryOrgMockTest() {
    QueryOrgMockRequest request = new QueryOrgMockRequest();
    Assert.assertFalse(infoauthBackStage.queryOrgMock(request).ifSuccess());
    Long id = createOrgMock();
    List<Long> ids = new ArrayList<>();
    ids.add(id);
    request.setIds(ids);
    SupportResult<QueryOrgMockResponse> response = infoauthBackStage.queryOrgMock(request);
    Assert.assertNotNull(response.getData().getMockList());
  }

  @Test
  public void deleteOrgMockTest() {
    Assert.assertFalse(infoauthBackStage.deleteOrgMock(-1).ifSuccess());
    Long id = createOrgMock();
    infoauthBackStage.deleteOrgMock(id);
    Assert.assertTrue(true);
  }

  private Long createOrgMock() {
    CreateOrgMockRequest request = new CreateOrgMockRequest();
    request.setAuthType(InfoAuthServiceType.ENTERPRISE_3_ELEMENT.name());
    request.setLostTime(System.currentTimeMillis() + 5000);
    List<OrgMockDataRequest> mockList = new ArrayList<>();
    OrgMockDataRequest orgMockDataRequest = new OrgMockDataRequest();
    orgMockDataRequest.setName("esigntest" + System.currentTimeMillis());
    orgMockDataRequest.setCertNo(String.valueOf(System.currentTimeMillis()));
    mockList.add(orgMockDataRequest);
    request.setMockList(mockList);
    return infoauthBackStage.createOrgMock(request).getData().getMockList().get(0).getId();
  }


  @Test
  public void updateProviderDomainClientMetaPasswordByIdAndAccountTest() {

    ProviderDomainClientMetaPasswordRequest request = new ProviderDomainClientMetaPasswordRequest();

    infoauthBackStage.updateProviderDomainClientMetaPasswordByIdAndAccount(null);
    infoauthBackStage.updateProviderDomainClientMetaPasswordByIdAndAccount(request);


    request.setProviderDomainId(-1L);
    infoauthBackStage.updateProviderDomainClientMetaPasswordByIdAndAccount(request);

    Long id = 693L;
    InfoProviderDomainDO providerDomainDO = infoProviderDomainDAO.getById(id);
    DomainClientDO domainClientDO = JsonUtils.json2pojo(StringUtils.defaultString(providerDomainDO.getDomainClient()), DomainClientDO.class);


    request.setProviderDomainId(id);
    request.setConditionalMetaAccount("11");
    request.setMetaPassword(domainClientDO.getMetaPassword());
    infoauthBackStage.updateProviderDomainClientMetaPasswordByIdAndAccount(request);
    

    request.setConditionalMetaAccount(domainClientDO.getMetaAccount());
    infoauthBackStage.updateProviderDomainClientMetaPasswordByIdAndAccount(request);

  }

  @Test
  public void testGetServiceTypesByScene(){
    GetServiceTypeRequest request = new GetServiceTypeRequest();
    request.setSceneType("innerInfoVerify");
    infoauthBackStage.getServiceTypesByScene(request);
  }
}
