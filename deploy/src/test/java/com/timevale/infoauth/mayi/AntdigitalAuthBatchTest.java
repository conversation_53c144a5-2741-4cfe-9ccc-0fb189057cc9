package com.timevale.infoauth.mayi;

import cn.com.antcloud.api.product.AntCloudProdClient;
import com.alibaba.fastjson.JSON;
import com.timevale.infoauth.service.api.OcrParseTest;
import com.timevale.infoauth.utils.ResourceUtils;
import esign.utils.IOUtil;
import lombok.Data;
import lombok.SneakyThrows;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * 文档：https://antdigital.com/docs/11/375425
 * <AUTHOR>
 * @since 2023/9/11 17:07
 */
public class AntdigitalAuthBatchTest {

  // 构造时提供的 AccessKey ID 和 secret 为调用方租户下服务账号的 AccessKey ID 和 secret
// 调试前需要确保该租户的管理员对此服务账号赋予了对应接口的权限
// 同时确保该服务账户在网关上开通了服务调用权限
// ENDPOINT 和 PRODUCT_INSTANCE_ID 值不用修改，直接使用

    private static final String ACCESS_KEY = "LTAI9kGTlVr1a7Wx";
    private static final String ACCESS_SECRET = "UNUR6LopAnkyZarBKUQBPN5LCLoJWW";
    private static final String PRODUCT_INSTANCE_ID = "REALPERSON-PROD";
    private static final String ENDPOINT = "https://openapi.antchain.antgroup.com/gateway.do";

    public static void main(String[] args) throws InterruptedException {
        AntCloudProdClient client = AntCloudProdClient.newBuilder()
                .setEndpoint(ENDPOINT)
                .setAccess(ACCESS_KEY, ACCESS_SECRET)
                .build();




        List<UserInfo> list = loadData("idno/query_result.csv");
        long startTime = System.currentTimeMillis();
        for(UserInfo userInfo : list){
             verify(userInfo, client);
             break;
        }
        long endTime = System.currentTimeMillis();
        System.out.println((endTime - startTime) +" ms");
    }

    private static  void verify(UserInfo userInfo, AntCloudProdClient client) throws InterruptedException {
        CheckRouteTwometaRequest  request = new CheckRouteTwometaRequest();
        request.setScene(PRODUCT_INSTANCE_ID);
        request.setCert_name(userInfo.name);
        request.setCert_no(userInfo.idNio);
        request.setScene("COMP");
        request.setOuter_order_no("IDNO"+System.currentTimeMillis());
        request.setExtern_param("{}");
        CheckRouteTwometaResponse response =   client.execute(request);
        boolean result = userInfo.result.equals("0");
        boolean match = response.getMatch().equals("true");
        if(result != match){
            System.out.println(userInfo.name+","+userInfo.idNio+","+userInfo.result+","+response.getResultMsg());
            System.out.println(JSON.toJSONString(response));
        }

    }

    @SneakyThrows
    private static List<UserInfo> loadData(String path){

        InputStream input;
        input = OcrParseTest.class.getClass().getResourceAsStream(path);
        if (input == null) {
            String classPathPath = "classpath:" + path;
            try {
                input = ResourceUtils.getURL(classPathPath).openStream();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (input == null) {
            throw new RuntimeException("can not load file:" + path);
        }

        byte[] data = IOUtil.readStreamAsByteArray(input);
        String text = new String(data);
        text = text.replace("\"","");
        String[] array = text.split("\\n");
        List<UserInfo> list = new ArrayList<>();
        for(String s : array){
            String[] h = s.split(",");
            UserInfo userInfo = new UserInfo();
            userInfo.setName(h[0]);
            userInfo.setIdNio(h[1]);
            userInfo.setResult(h[2]);
            userInfo.setFee(h[3]);
            list.add(userInfo);
        }


        return list;
    }

    @Data
    public static class UserInfo{
        private String name;
        private String idNio;
        private String result;
        private String fee;
    }
}
