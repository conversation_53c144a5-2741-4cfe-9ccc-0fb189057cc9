package com.timevale.infoauth.dal.infoauth.dataobject;

public interface InfoProviderInterface {

  String getName();

  void setName(String name);

  String getAccount();

  void setAccount(String account);

  String getSignKey();

  void setSignKey(String signKey);

  String getUrl();

  void setUrl(String url);

  String getSecret();

  void setSecret(String secret);

  String getVerifyKey();

  void setVerifyKey(String verifyKey);

  Integer getLevel();

  void setLevel(Integer level);
}
