<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="biz_err_code_enum" physicalName="biz_err_code_enum" remark="信息比对业务错误码枚举">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,MSG,CODE,EXTEND,BIZ_CODE_ID,CREATE_TIME,MODIFY_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.MSG,sf.CODE,sf.EXTEND,sf.BIZ_CODE_ID,sf.CREATE_TIME,sf.MODIFY_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:biz_err_code_enum">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO biz_err_code_enum(
            ID
            ,MSG
            ,CODE
            ,EXTEND
            ,BIZ_CODE_ID
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES(
             null
            , #{msg,jdbcType=VARCHAR}
            , #{code,jdbcType=VARCHAR}
            , #{extend,jdbcType=VARCHAR}
            , #{bizCodeId,jdbcType=VARCHAR}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{modifyTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:biz_err_code_enum">
        INSERT INTO biz_err_code_enum(
            ID
            ,MSG
            ,CODE
            ,EXTEND
            ,BIZ_CODE_ID
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.msg,jdbcType=VARCHAR}
                , #{item.code,jdbcType=VARCHAR}
                , #{item.extend,jdbcType=VARCHAR}
                , #{item.bizCodeId,jdbcType=VARCHAR}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.modifyTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:biz_err_code_enum">
        <![CDATA[
        UPDATE biz_err_code_enum
        SET
            MSG             = #{msg,jdbcType=VARCHAR}
            ,CODE            = #{code,jdbcType=VARCHAR}
            ,EXTEND          = #{extend,jdbcType=VARCHAR}
            ,BIZ_CODE_ID     = #{bizCodeId,jdbcType=VARCHAR}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,MODIFY_TIME     = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:biz_err_code_enum">
        <![CDATA[
        DELETE FROM biz_err_code_enum
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:biz_err_code_enum">
        SELECT *
        FROM biz_err_code_enum
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <!-- 根据唯一约束操作数据 -->
    <operation name="updateByUniqCode" paramtype="object" remark="根据唯一约束UniqCode更新表:biz_err_code_enum">
        <![CDATA[
        UPDATE biz_err_code_enum
        SET
            MSG             = #{msg,jdbcType=VARCHAR}
            ,EXTEND          = #{extend,jdbcType=VARCHAR}
            ,BIZ_CODE_ID     = #{bizCodeId,jdbcType=VARCHAR}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,MODIFY_TIME     = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE
            CODE            = #{code,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="deleteByUniqCode" remark="根据唯一约束UniqCode删除数据:biz_err_code_enum">
        <![CDATA[
        DELETE FROM biz_err_code_enum
        WHERE
            CODE            = #{code,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="getByUniqCode" multiplicity="one" remark="根据唯一约束UniqCode获取数据:biz_err_code_enum">
        SELECT *
        FROM biz_err_code_enum
        WHERE
        <![CDATA[
            CODE            = #{code,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxBizCodeId" multiplicity="many" remark="根据普通索引IdxBizCodeId获取数据:biz_err_code_enum">
        SELECT *
        FROM biz_err_code_enum
        WHERE
        <![CDATA[
            BIZ_CODE_ID     = #{bizCodeId,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxMsg" multiplicity="many" remark="根据普通索引IdxMsg获取数据:biz_err_code_enum">
        SELECT *
        FROM biz_err_code_enum
        WHERE
        <![CDATA[
            MSG             = #{msg,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>
