package com.timevale.infoauth.dal.infoauth.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * The table.
 * 供应商错误码枚举
 * <AUTHOR> Kunpeng
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProviderErrCodeEnumDO{

    /**
     * createTime 记录创建时间.
     */
    private Date createTime;
    /**
     * modifyTime 记录更新时间.
     */
    private Date modifyTime;
    /**
     * id 自增主键.
     */
    private Long id;
    /**
     * pMsg 供应商模板msg,参考provider_err_msg_template.
     */
    private String pMsg;
    /**
     * pCode 供应商错误码.
     */
    private String pCode;
    /**
     * bizMd5 type+provider+code+msg.
     */
    private String bizMd5;
    /**
     * extend 扩展字段.
     */
    private String extend;
    /**
     * pCodeId 业务主键.
     */
    private String pCodeId;
    /**
     * provider 供应商名称.
     */
    private String provider;
    /**
     * bizCodeId 适配错误.
     */
    private String bizCodeId;
    /**
     * bizType 信息比对类型.
     */
    private Integer bizType;
}
