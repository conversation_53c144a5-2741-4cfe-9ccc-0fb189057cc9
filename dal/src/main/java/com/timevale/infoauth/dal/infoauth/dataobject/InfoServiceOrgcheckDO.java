package com.timevale.infoauth.dal.infoauth.dataobject;

import lombok.Data;

import java.util.Date;

/**
 * The table. info_service_orgcheck
 *
 * <AUTHOR> Kunpeng
 */
@Data
public class InfoServiceOrgcheckDO extends InfoServiceAuthDO {
  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;
  /** businessEndtime 经营截止日期. */
  private Date businessEndtime;
  /** businessStarttime 经营起始日期. */
  private Date businessStarttime;
  /** id 主键. */
  private Long id;
  /** name 企业名称. */
  private String name;
  /** codeorg 企业组织机构代码. */
  private String codeorg;
  /** codereg CODEREG. */
  private String codereg;
  /** codeusc 企业社会统一信用代码. */
  private String codeusc;
  /** legalName 企业法人姓名. */
  private String legalName;
  /** infoauthId 信息比对服务ID. */
  private String infoauthId;
  /** providerId 企业查询服务提供商. */
  private String providerId;
  /** billorderId 计费订单ID. */
  private String billorderId;
  /** timestampId 时间戳. */
  private String timestampId;
  /** baseProviderid 组代信息查询服务提供商. */
  private String baseProviderid;
  /** providerResult 服务提供商返回的代码. */
  private String providerResult;
  /** baseProviderresult 组代信息服务提供商返回的代码. */
  private String baseProviderresult;
  /** businessEndtimedesc BUSINESS_ENDTIMEDESC. */
  private String businessEndtimedesc;
  /** result 比对结果. */
  private Integer result;
  /** legalArea 企业法人所在区域(国籍). */
  private Short legalArea;
  /** serviceStatus SERVICE_STATUS. */
  private Integer serviceStatus;
  /** businessStatus 在营状态. */
  private Short businessStatus;

}
