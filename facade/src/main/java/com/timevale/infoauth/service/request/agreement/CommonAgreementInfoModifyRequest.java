package com.timevale.infoauth.service.request.agreement;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 通用协议编辑信息
 *
 * <AUTHOR>
 */
@Data
public class CommonAgreementInfoModifyRequest extends CommonAgreementBaseRequest {

  /** 最新协议版本. */
  private String releaseVersion;


  /** 通用协议场景描述，非协议名称 参考字段（AgreementReleaseRecordDTO.shortName） */
  private String commonAgreementTitle;

  /**
   * 协议类型
   * @see com.timevale.infoauth.service.enums.agreement.CommonAgreementTypeEnum
   * */
  private String commonAgreementType;

  /** 协议备注. */
  private String commonAgreementMark;

  /**
   * 通用协议目录
   * @see com.timevale.infoauth.service.enums.agreement.CommonAgreementCategoryEnum
   * */
  private String category;

  /** 操作人. */
  private String operator;


}
