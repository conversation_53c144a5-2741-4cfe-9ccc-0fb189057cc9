package com.timevale.infoauth.service.enums;

import com.timevale.mandarin.base.enums.EnumBase;

/**
 * 范围：30504101~********
 *
 * @author: jiuchen
 * @since: 2020-08-13 12:48
 */
public enum InfoAuthErrorCodeEnum implements EnumBase {

  /** =========== 人脸无源库照比对 =========== */
  COMPARE_WITHOUT_SOURCE_UNKNOW("30504101", "库照比对-未知结果"),
  COMPARE_WITHOUT_SOURCE_TIMEOUT("30504102", "库照比对-超时"),
  COMPARE_WITHOUT_SOURCE_NOT_FOUND("30504103", "库照比对-数据源无此数据"),
  COMPARE_WITHOUT_SOURCE_NOT_PASS("30504104", "库照比对-不通过"),
  COMPARE_WITHOUT_SOURCE_RISK_LIMIT("30504105", "库照比对-风控限制"),
  COMPARE_WITHOUT_SOURCE_FACE_UNRECOGNIZED("30504106", "库照比对-人脸无法识别"),
  COMPARE_WITHOUT_SOURCE_CERT_INFO_UNKNOWN("30504107", "库照比对-用户证件号信息错误"),
  COMPARE_WITHOUT_SOURCE_FACE_NOT_MATCH("********", "库照比对-人脸核身失败"),

  /** =========== 人脸有源两照比对 =========== */
  COMPARE_WITH_SOURCE_UNKNOW("********", "两照比对-未知结果"),
  COMPARE_WITH_SOURCE_TIMEOUT("********", "两照比对-超时"),
  COMPARE_WITH_SOURCE_FACE_UNRECOGNIZED("********", "两照比对-检测不到人脸"),
  COMPARE_WITH_SOURCE_NOT_PASS("********", "两照比对-不通过"),

  /** =========== 详情版 =========== */
  BANK4_DETAIL_ID2_SIMPLE_NOT_MATCH("********", "姓名与⾝份证号不⼀致"),
  BANK4_DETAIL_BANK3_SIMPLE_MATCH("********", "手机号与银行预留信息不一致"),
  BANK4_DETAIL_BANK3_SIMPLE_NOT_MATCH("********", "银行卡号与身份证号不一致"),

  BANK3_DETAIL_ID2_SIMPLE_MATCH("********", "银行卡号与身份证号不一致"),
  BANK3_DETAIL_ID2_SIMPLE_NOT_MATCH("********", "姓名与⾝份证号不⼀致"),

  TELECOM3_DETAIL_ID2_SIMPLE_MATCH("********", "手机号与身份证号不⼀致"),
  TELECOM3_DETAIL_ID2_SIMPLE_NOT_MATCH("********", "姓名与⾝份证号不⼀致"),
  /** =========== 信息比对优化异常code =========== */
  INFOAUTH_SERVICE_ADD_INFOSERVICE_BIZ_RECORD_ERR("********", "信息比对服务业务记录添加异常"),
  INFOAUTH_SERVICE_ADD_BIZ_RECORD_ERR("********", "信息比对服务主表记录添加异常"),

  INFO_SERVICE_PSN_IDNO_AUTH_ADD_ERR("********", "个人二要素表添加异常"),

  INFO_SERVICE_PSN_BANK_THREE_ADD_ERR("********", "个人银行三要素表添加异常"),

  INFO_SERVICE_PSN_BANK_AUTH_ADD_ERR("********", "个人银行三要素表添加异常"),

  INFO_SERVICE_PSN_TELECOM_AUTH_ADD_ERR("********", "个人电信三要素表添加异常"),

  INFO_SERVICE_ORG_AUTH_ADD_ERR("********", "企业信息比对添加异常"),

  INFO_SERVICE_INFO_SERVICE_COMPARE_ADD_ERR("********", "信息比对业务表es添加失败"),

  INFO_SERVICE_PSN_NAME_MOBILE_AUTH_ADD_ERR("********", "个人姓名手机号二要素表添加异常"),

  /** =========== 参数校验 =========== */
  CHECK_REQUEST_NOT_BLANK("********", "入参不能为空"),
  CHECK_APPID_NOT_BLANK("********", "appId不能为空"),
  CHECK_KEYWORD_NOT_BLANK("********", "keyword不能为空"),

  /** =========== 企业详情版 =========== */
  ORG_DETAIL_ORG_NAME_NOT_MATCH("********", "企业名称不⼀致"),
  ORG_DETAIL_REG_CODE_NOT_MATCH("********", "工商注册号不⼀致"),
  ORG_DETAIL_ORG_CODE_NOT_MATCH("********", "组织机构代码不⼀致"),
  ORG_DETAIL_USC_CODE_NOT_MATCH("********", "统一社会信用代码不⼀致"),
  ORG_DETAIL_LEGAL_NAME_NOT_MATCH("30504234", "法人姓名不⼀致"),
  ORG_DETAIL_LEGAL_IDCARD_NOT_MATCH("30504235", "法人证件号不⼀致"),
  ORG_DETAIL_ABNORMAL_STATUS("30504236", "非正常经营状态"),
  ORG_DETAIL_QUERY_NULL("30504237", "查询无结果"),
  @Deprecated
  ORG_DETAIL_ORG_NAME_CERTNO_ERROR("30504238", "企业名称或企业证件号错误"),
  ORG_DETAIL_USC_CODE_OR_REG_CODE_NOT_MATCH("30504239", "统一社会信用代码/工商注册号不⼀致"),

  ORG_DETAIL_USC_CODE_ERROR_PREFIX("30504240", "暂不支持核验该类型企业"),

  /** =========== OCR =========== */
  OCR_FRONT_FAILURE("30504245", "OCR正面识别失败"),
  OCR_FRONT_TIMEOUT("30504246", "OCR正面识别超时"),
  OCR_BACK_FAILURE("30504247", "OCR反面识别失败"),
  OCR_BACK_TIMEOUT("********", "OCR反面识别超时"),
  OCR_ALL_FAILURE("********", "OCR正反两面识别失败"),
  OCR_ALL_TIMEOUT("********", "OCR正反两面识别超时"),

  /** =========== 供应商错误原因适配 - 动态文案 (******** ~ ********) - 此号段已被特殊征用，请绕道 =========== */
  ADAPTIVE_CODE_NAME_FORMAT_NOT_SUPPORTED("********", "姓名格式不支持"),
  ADAPTIVE_CODE_IDCARD_FORMAT_ILLEGAL("********", "身份证格式不正确"),
  ADAPTIVE_CODE_BANKCARD_FORMAT_ILLEGAL("********", "银行卡格式不正确"),
  ADAPTIVE_CODE_MOBILE_NUMBER_ILLEGAL("********", "手机号段不正确"),
  ADAPTIVE_CODE_ORG_NAME_NOT_MATCH("********", "企业名称不匹配"),
  ADAPTIVE_CODE_LEGAL_NAME_NOT_MATCH("********", "法人姓名不匹配"),
  ADAPTIVE_CODE_USC_CODE_NOT_MATCH("********", "统一社会信用代码不匹配"),
  ADAPTIVE_CODE_IDCARD_NAME_NOT_MATCH("********", "身份证与姓名不匹配"),
  ADAPTIVE_CODE_BUSINESS_STATUS_ABNORMAL("********", "企业经营状态存在异常"),
  ADAPTIVE_CODE_PHOTO_QUALITY_POOR("********", "照片质量不清晰"),
  ADAPTIVE_CODE_PHOTO_FORMAT_NOT_SUPPORTED("********", "照片格式不支持"),
  ADAPTIVE_CODE_PHOTO_SIZE_NOT_SUPPORTED("********", "照片尺寸不支持"),
  ADAPTIVE_CODE_PHOTO_NOT_DETECTED("********", "照片中未检测到人脸"),
  ADAPTIVE_CODE_BANKCARD_VERIFICATION_NOT_SUPPORTED("********", "发卡行返回该卡不支持验证"),
  ADAPTIVE_CODE_QUERY_NOTHING_FROM_DATASOURCE("********", "数据源查询无结果"),
  ADAPTIVE_CODE_REG_CODE_NOT_MATCH("********", "工商注册号不匹配"),
  ADAPTIVE_CODE_ORG_CODE_NOT_MATCH("********", "组织机构代码不匹配"),
  ADAPTIVE_CODE_LEGAL_IDCARD_NOT_MATCH("********", "法人证件号不匹配"),
  ADAPTIVE_CODE_ORG_NAME_CERTNO_ERROR("********", "企业名称或企业证件号错误"),
  ADAPTIVE_CODE_USC_CODE_REG_CODE_NOT_MATCH("********", "统一社会信用代码/工商注册号不匹配"),
  ADAPTIVE_CODE_ORG_NAME_LEGAL_NAME_ERROR("********", "企业名称或法人名称错误"),


  /** =========== 风控相关错误码 - 动态文案 (******** ~ ********) - 此号段已被特殊征用 =========== */
  RISK_COMMON_ERROR("********", "请求已判定被风控请等待/联系管理员处理！"),
  ;

  private String errCode;
  private String errMsg;

  InfoAuthErrorCodeEnum(String errCode, String errMsg) {
    this.errCode = errCode;
    this.errMsg = errMsg;
  }

  public String getErrCode() {
    return errCode;
  }

  public void setErrCode(String errCode) {
    this.errCode = errCode;
  }

  public String getErrMsg() {
    return errMsg;
  }

  public void setErrMsg(String errMsg) {
    this.errMsg = errMsg;
  }

  @Override
  public String message() {
    return errMsg;
  }
}
