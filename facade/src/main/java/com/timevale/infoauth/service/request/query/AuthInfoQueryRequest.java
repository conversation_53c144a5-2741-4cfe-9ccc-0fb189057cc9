package com.timevale.infoauth.service.request.query;

import com.timevale.infoauth.service.model.BaseModel;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class AuthInfoQueryRequest extends BaseModel {

  private String idno;

  private String codeORG;

  private String codeUSC;

  private String codeREG;

  private Date startTime;

  private Date endTime;

  private Timestamp modifyTime;

  private Integer currIndex, pageSize;
}
