package com.timevale.infoauth.service.enums;

public enum ProvResult {

  // 业务成功
  SUCCESS(1,"信息匹配"),

  // 业务失败
  FAILURE(2,"信息不匹配"),

  // 业务离线
//  SVRDOWN(3,"业务离线"),

  // 业务限制
//  ABLIMIT(4,"验证次数超过限制"),

  // 业务中断(网纹照比对失败/成功)
//  SVRABORT(5,"多步骤业务中断"),

  // 超时状态
  TIMEOUT(6,"供应商调用超时"),

  // 查询无结果
  NOTFOUND(7,"供应商数据查询不到"),

  // 格式转化错误
  FORMATERROR(8,"供应商请求/响应格式转化错误"),

  // 未知状态
  UNKNOWN(-1,"未识别状态"),
  ;

  ProvResult(int dbValue, String description) {
    this.dbValue = dbValue;
    this.description = description;
  }

  private int dbValue;

  private String description;

  public int dbValue() {
    return dbValue;
  }

  public String getDescription() {
    return description;
  }

  public boolean isSuccess() {
    return ProvResult.SUCCESS == this;
  }
  // 是否是可以识别的结果
  public boolean isResult(){
    return ProvResult.SUCCESS == this || ProvResult.FAILURE == this || ProvResult.TIMEOUT == this;
  }
  // 是否是可以缓存的结果
  public boolean isCacheResult() {
    return ProvResult.SUCCESS == this || ProvResult.FAILURE == this;
  }
}
