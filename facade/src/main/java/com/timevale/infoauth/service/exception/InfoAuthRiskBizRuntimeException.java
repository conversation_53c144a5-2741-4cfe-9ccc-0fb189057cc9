package com.timevale.infoauth.service.exception;

import com.timevale.mandarin.base.exception.BaseBizRuntimeException;

/**
 * http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=22350211
 *
 * 框架打印的日志等级：
 * BaseRuntimeException	ERROR	500
 * BaseIllegalStateException	WARN	412
 * BaseIllegalArgumentException	WARN	400
 * BaseBizRuntimeException	WARN	503
 *
 * <AUTHOR>
 * @DATE 2025/4/22 15:28
 */
public class InfoAuthRiskBizRuntimeException extends BaseBizRuntimeException {

    public InfoAuthRiskBizRuntimeException(String code, String detailMessage) {
        super(code, detailMessage);
    }



    /**
     * @param code
     * @param digestMessage
     * @param detailMessage
     */
    public InfoAuthRiskBizRuntimeException(String code, String digestMessage, String detailMessage) {
        super(code, digestMessage, detailMessage);
    }

    public InfoAuthRiskBizRuntimeException(String message) {
        super(message);
    }
}


