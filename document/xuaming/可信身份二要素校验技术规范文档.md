```markdown
# 可信身份二要素校验技术规范文档 v1.0

## 1.1 文档简介

本文档是可信身份二要素校验服务接口规范，主要为该服务的需求客户描述接口的连接方式、通讯协议、报文格式等技术约定，以及相关的数据产品定义和使用说明。

## 1.2 接口详情

- **请求地址**: `https://123.56.221.164/api/idcardverify`
- **请求方法**: `POST`
- **请求及响应报文类型**: `json`

## 1.3 请求参数

| 参数名  | 类型   | 是否必填 | 参数说明 |
|---------|--------|----------|----------|
| APPID   | string | 是       | 12 位账号（平台发放） |
| name    | string | 是       | 姓名 |
| idcard  | string | 是       | 身份证号 |

## 1.4 请求示例

```
json
{
"APPID": "************",
"name": "张三",
"idcard": "350011200001011101"
}
```
## 1.5 响应报文参数

| 参数名   | 类型   | 参数说明 |
|----------|--------|----------|
| request_id | string | 请求流水号 |
| status  | string | 状态码 |
| message | string | 响应状态描述 |

## 1.6 响应报文示例

```
json
{
"request_id": "******************",
"status": "200",
"message": "信息一致"
}
```
## 1.7 响应状态说明

| 状态码 | 状态描述 | 是否计费 |
|--------|----------|----------|
| 200    | 信息一致 | 是       |
| 201    | 信息不一致 | 是       |
| 202    | 身份信息无效 | 是       |
| 100    | 参数不正确或不规范 | 否       |
| 300    | 系统繁忙，请稍后重试 | 否       |
| 400    | AppID 无效或服务未开通 | 否       |
| 401    | IP 未报备白名单或签名失败 | 否       |
| 402    | 账户授信额度已用尽或服务已过期 | 否       |
| 403    | 交易限流或查询过于频繁，请稍后重试 | 否       |
| 404    | 当日交易总量已达上限，于次日 0 点恢复 | 否       |

## 1.8 疑问解析

### 身份信息无效的原因是什么？

身份信息无效代表库中无此号，官方原因导致数据无法核验，原因包括但不限于：

1. 现役或刚退役的军人；
2. 刚上大学或刚毕业的大学生；
3. 正在迁移户口或者近期迁移户口；
4. 工作调动导致户口有迁移；
5. 发生更名导致户口有变动；
6. 地方向中心上传时数据缺失。

希望这份 `README.md` 文件能帮助您更好地理解和使用可信身份二要素校验服务。如果有任何问题或建议，请随时联系我们。
```
