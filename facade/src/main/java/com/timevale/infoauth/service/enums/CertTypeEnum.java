package com.timevale.infoauth.service.enums;

import com.timevale.mandarin.base.exception.BaseRuntimeException;

import java.util.Objects;

/** @Author: <PERSON> @Description: @Date: Created in 2018/3/19 @Modified By: */
public enum CertTypeEnum {
  PSN_IDENTITY_CARD("身份证", (short) 1),

  ORG_CODE_USC("统一社会信用代码", (short) 11),

  ORG_CODE_REG("工商注册号", (short) 12),

  ORG_CODE_ORG("组织机构代码", (short) 13),

  /** 第三方自定义代码 */
  CODE_THIRD_CUSTOM_CODE_1("第三方自定义代码", (short) 101),

  UNKNOWN("未知", (short) 0);

  private String name;

  private Short dbValue;

  CertTypeEnum(String name, Short dbValue) {
    this.name = name;
    this.dbValue = dbValue;
  }

  public String getName() {
    return name;
  }

  public Short dbValue() {
    return dbValue;
  }

  public static CertTypeEnum getByDbValue(Short dbValue) {

    for (CertTypeEnum certType : CertTypeEnum.values()) {
      if (Objects.equals(certType.dbValue(), dbValue)) {
        return certType;
      }
    }
    throw new BaseRuntimeException("证件类型不存在");
  }
  public static CertTypeEnum getByDbValue(Integer dbValue) {

    for (CertTypeEnum certType : CertTypeEnum.values()) {
      if (Objects.equals(certType.dbValue().intValue(), dbValue)) {
        return certType;
      }
    }
    return UNKNOWN;
  }
}
