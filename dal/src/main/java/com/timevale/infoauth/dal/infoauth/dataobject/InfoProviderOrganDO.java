package com.timevale.infoauth.dal.infoauth.dataobject;

import java.util.Date;

/**
 * The table. info_provider_organ
 *
 * <AUTHOR> Kunpeng
 */
public class InfoProviderOrganDO extends BaseProviderDO implements IInfoProvider {

  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录更新时间. */
  private Date modifyTime;
  /** id 主键. */
  private Long id;
  /** url URL. */
  private String url;
  /** disc 服务商描述. */
  private String disc;
  /** name 服务商名称. */
  private String name;
  /** secret SECRET. */
  private String secret;
  /** account ACCOUNT. */
  private String account;
  /** signKey 签名密钥. */
  private String signKey;
  /** verifyKey VERIFY_KEY. */
  private String verifyKey;
  /** level 优先级. */
  private Integer level;
  /** signType 签名方式. */
  private Integer signType;
  /** providerType 服务商接口类型 0:工商和组代 1:仅工商 2:仅组代. */
  private Integer providerType;

  /** Set createTime 记录创建时间. */
  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  /**
   * Get createTime 记录创建时间.
   *
   * @return the string
   */
  public Date getCreateTime() {
    return createTime;
  }

  /** Set modifyTime 记录更新时间. */
  public void setModifyTime(Date modifyTime) {
    this.modifyTime = modifyTime;
  }

  /**
   * Get modifyTime 记录更新时间.
   *
   * @return the string
   */
  public Date getModifyTime() {
    return modifyTime;
  }

  /** Set id 主键. */
  public void setId(Long id) {
    this.id = id;
  }

  /**
   * Get id 主键.
   *
   * @return the string
   */
  public Long getId() {
    return id;
  }

  /** Set url URL. */
  public void setUrl(String url) {
    this.url = url;
  }

  /**
   * Get url URL.
   *
   * @return the string
   */
  public String getUrl() {
    return url;
  }

  /** Set disc 服务商描述. */
  public void setDisc(String disc) {
    this.disc = disc;
  }

  /**
   * Get disc 服务商描述.
   *
   * @return the string
   */
  public String getDisc() {
    return disc;
  }

  /** Set name 服务商名称. */
  public void setName(String name) {
    this.name = name;
  }

  /**
   * Get name 服务商名称.
   *
   * @return the string
   */
  @Override
  public String getName() {
    return name;
  }

  /** Set secret SECRET. */
  public void setSecret(String secret) {
    this.secret = secret;
  }

  /**
   * Get secret SECRET.
   *
   * @return the string
   */
  public String getSecret() {
    return secret;
  }

  /** Set account ACCOUNT. */
  public void setAccount(String account) {
    this.account = account;
  }

  /**
   * Get account ACCOUNT.
   *
   * @return the string
   */
  public String getAccount() {
    return account;
  }

  /** Set signKey 签名密钥. */
  public void setSignKey(String signKey) {
    this.signKey = signKey;
  }

  /**
   * Get signKey 签名密钥.
   *
   * @return the string
   */
  public String getSignKey() {
    return signKey;
  }

  /** Set verifyKey VERIFY_KEY. */
  public void setVerifyKey(String verifyKey) {
    this.verifyKey = verifyKey;
  }

  /**
   * Get verifyKey VERIFY_KEY.
   *
   * @return the string
   */
  public String getVerifyKey() {
    return verifyKey;
  }

  /** Set level 优先级. */
  public void setLevel(Integer level) {
    this.level = level;
  }

  /**
   * Get level 优先级.
   *
   * @return the string
   */
  public Integer getLevel() {
    return level;
  }

  /** Set signType 签名方式. */
  public void setSignType(Integer signType) {
    this.signType = signType;
  }

  /**
   * Get signType 签名方式.
   *
   * @return the string
   */
  public Integer getSignType() {
    return signType;
  }

  /** Set providerType 服务商接口类型 0:工商和组代 1:仅工商 2:仅组代. */
  public void setProviderType(Integer providerType) {
    this.providerType = providerType;
  }

  /**
   * Get providerType 服务商接口类型 0:工商和组代 1:仅工商 2:仅组代.
   *
   * @return the string
   */
  public Integer getProviderType() {
    return providerType;
  }
}
