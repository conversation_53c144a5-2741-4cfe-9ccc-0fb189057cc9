package com.timevale.infoauth.dal.infoauth.cache;

import com.alibaba.fastjson.JSON;
import com.timevale.framework.tedis.util.TedisUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.support.AbstractValueAdaptingCache;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

public class MyRedisCacheManager extends AbstractValueAdaptingCache {

  private final Logger logger = LoggerFactory.getLogger(MyRedisCacheManager.class);

  private String name;

  private long expirationTime;

  private boolean l2Cache;

  public MyRedisCacheManager(boolean allowNullValues,boolean allowL2, String name, long expirationTime) {
    super(allowNullValues);
    this.name = name;
    this.expirationTime = expirationTime;
    this.l2Cache = allowL2;
  }

  @Override
  protected Object lookup(Object o) {
    if (o == null) {
      return null;
    }
    Object cache = TedisUtil.get(o.toString());
    logger.info("获取redis缓存>>>key:{},value:{}",o.toString(),JSON.toJSONString(cache));
    return cache;
  }

  @Override
  public String getName() {
    return this.name;
  }

  @Override
  public Object getNativeCache() {
    return null;
  }

  @Override
  public <T> T get(Object o, Callable<T> callable) {
    return null;
  }

  @Override
  public void put(Object key, Object value) {
    if (key == null) {
      throw new NullPointerException("the key cannot be null");
    }
    if (value == null && !isAllowNullValues()) {
      return;
    }

    // 放入一级缓存
    if(l2Cache && LocalStorageMap.getInstance().put(key.toString(), value, expirationTime)){
        logger.info("****** 放入一级缓存 ：{},{}", JSON.toJSONString(key), JSON.toJSONString(value));
        return;
    }

    logger.info("****** 放入缓存 ：{},{}", JSON.toJSONString(key), JSON.toJSONString(value));
    TedisUtil.set(key.toString(), value, expirationTime, TimeUnit.SECONDS);
  }

  @Override
  public ValueWrapper putIfAbsent(Object o, Object o1) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void evict(Object o) {
    if (o == null) {
      throw new NullPointerException();
    }
    logger.info("****** 失效缓存 ：{}", JSON.toJSONString(o));
    if(LocalStorageMap.getInstance().delete(o.toString())){
        return;
    }
    TedisUtil.delete(o.toString());
  }

  @Override
  public void clear() {
    throw new UnsupportedOperationException();
  }
}
