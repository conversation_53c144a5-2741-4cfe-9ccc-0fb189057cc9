package com.timevale.infoauth.service.agreement.impl;

import com.timevale.infoauth.dal.infoauth.dao.agreement.CommonAgreementInfoDAO;
import com.timevale.infoauth.dal.infoauth.dao.agreement.CommonAgreementReleaseRecordDAO;
import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementInfoDO;
import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementReleaseRecordDO;
import com.timevale.infoauth.service.agreement.component.CommonAgreementComponent;
import com.timevale.infoauth.service.agreement.component.tool.CommonAgreementIdTool;
import com.timevale.infoauth.service.agreement.config.DeletedEnum;
import com.timevale.infoauth.service.api.agreement.CommonAgreementManagerService;
import com.timevale.infoauth.service.enums.agreement.CommonAgreementErrorCodeEnum;
import com.timevale.infoauth.service.enums.agreement.CommonAgreementLangEnum;
import com.timevale.infoauth.service.enums.agreement.CommonAgreementReleaseStatusEnum;
import com.timevale.infoauth.service.enums.agreement.CommonAgreementStatusEnum;
import com.timevale.infoauth.service.model.CommonResultEnum;
import com.timevale.infoauth.service.model.SupportResult;
import com.timevale.infoauth.service.request.agreement.*;
import com.timevale.infoauth.service.response.agreement.AgreementReleaseRecordBO;
import com.timevale.infoauth.service.response.agreement.CommonAgreementInfoBaseResponse;
import com.timevale.infoauth.service.response.agreement.CommonAgreementPageListResponse;
import com.timevale.infoauth.service.response.agreement.CommonAgreementReleaseHistoryResponse;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.RestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 实现通用协议管理服务接口
 * 提供协议列表获取、历史发布记录查询、协议添加、修改、删除及状态变更等功能
 * <AUTHOR>
 */
@Slf4j
@RestService
public class CommonAgreementManagerServiceImpl implements CommonAgreementManagerService {

    @Autowired
    private CommonAgreementComponent commonAgreementComponent;
    @Autowired
    private CommonAgreementInfoDAO agreementInfoDAO;
    @Autowired
    private CommonAgreementReleaseRecordDAO releaseRecordDAO;

    /**
     * 获取协议分页列表
     *
     * @param request 协议列表查询请求对象，包含查询条件、分页信息等
     * @return 返回协议分页列表响应对象，包含协议信息列表、分页数据等
     */
    @Override
    public SupportResult<CommonAgreementPageListResponse> queryAgreementPageList(
            CommonAgreementListQueryRequest request) {
        request.format();
        long total = commonAgreementComponent.countAgreements(request);
        CommonAgreementPageListResponse pageListResponse = new CommonAgreementPageListResponse();
        pageListResponse.setPageIndex(request.getPageIndex());
        pageListResponse.setPageSize(request.getPageSize());
        pageListResponse.setTotal(total);
        List<CommonAgreementInfoBaseResponse> data = new ArrayList<>();
        pageListResponse.setData(data);
        List<CommonAgreementInfoDO>  list = commonAgreementComponent.getAgreementListPages(request);
        if(CollectionUtils.isEmpty(list)){
            return   SupportResult.success(pageListResponse);
        }
        for(CommonAgreementInfoDO info : list){
            CommonAgreementInfoBaseResponse baseResponse = new CommonAgreementInfoBaseResponse();
            CommonAgreementResponseConverter.convertInfoResponse(baseResponse, info);
            data.add(baseResponse);
        }

        return SupportResult.success(pageListResponse);
    }

    /**
     * 获取协议发布历史记录
     *
     * @param request 协议发布历史记录查询请求对象，包含协议ID等查询条件
     * @return 返回协议发布历史记录响应对象，包含发布记录列表等
     */
    @Override
    public SupportResult<CommonAgreementReleaseHistoryResponse> queryAgreementReleaseHistory(CommonAgreementReleaseHistoryQueryRequest request) {

        CommonAgreementReleaseHistoryResponse response = new CommonAgreementReleaseHistoryResponse();
        List<AgreementReleaseRecordBO> data = new ArrayList<>();
        response.setData(data);
        List<CommonAgreementReleaseRecordDO>  list = commonAgreementComponent.getAgreementReleaseRecords(request);
        if(CollectionUtils.isEmpty(list)){
            return SupportResult.success(response);
        }
        for(CommonAgreementReleaseRecordDO recordDO : list){
            AgreementReleaseRecordBO recordDTO = new AgreementReleaseRecordBO();
            CommonAgreementResponseConverter.convertReleasedInfo(recordDTO, recordDO);
            data.add(recordDTO);
        }

        return SupportResult.success(response);
    }

    /**
     * 添加新协议
     *
     * @param request 协议添加请求对象，包含协议基本信息
     * @return 返回添加成功后的协议ID
     */
    @Override
    public SupportResult<String> addAgreement(CommonAgreementInfoAddRequest request) {
        CommonAgreementInfoDO entityDO = new CommonAgreementInfoDO();
        entityDO.setStatus(CommonAgreementStatusEnum.AGREE_STATUS_NOT_ENABLE.getCode());
        entityDO.setDeleted(DeletedEnum.NOT_DELETED.getCode());
        entityDO.setAgreementId(CommonAgreementIdTool.markAgreementId());
        entityDO.setAgreementTitle(request.getCommonAgreementTitle());
        entityDO.setAgreementMark(request.getCommonAgreementMark());
        entityDO.setCategoryRef(request.getCategory());
        entityDO.setAgreementType(request.getCommonAgreementType());
        entityDO.setOperator(request.getOperator());
        entityDO.setCreator(request.getCreator());
        try{
            agreementInfoDAO.save(entityDO);
        } catch (Exception e){
            log.error("add agreement error",e);
            return  SupportResult.fail(CommonResultEnum.RPC_INVOKE_ERR,"addAgreement to db fail" );
        }

        return SupportResult.success(entityDO.getAgreementId());
    }

    /**
     * 删除协议
     *
     * @param request 协议删除请求对象，包含协议ID等信息
     * @return 返回删除操作结果
     */
    @Override
    public SupportResult<Boolean> delAgreement(CommonAgreementInfoDelRequest request) {
        SupportResult<CommonAgreementInfoDO> agreementResult =   checkAndGetAgreementInfo(request);
        if(agreementResult.ifFail()){
            return SupportResult.fail(agreementResult.getCode(),agreementResult.getMessage());
        }
        CommonAgreementInfoDO infoDO = agreementResult.getData();
        if(CommonAgreementStatusEnum.enable(infoDO.getStatus())){
            return SupportResult.fail(CommonAgreementErrorCodeEnum.AGR_INFO_ENABLE_CANNOT_DEL);
        }

        CommonAgreementInfoDO entityDO = new CommonAgreementInfoDO();
        entityDO.setId(infoDO.getId());
        entityDO.setDeleted(DeletedEnum.DELETED.getCode());
        entityDO.setOperator(request.getOperator());
        try{
            agreementInfoDAO.updateById(entityDO);
        }catch (Exception e){

        }

        return SupportResult.success(true);
    }

    /**
     * 修改协议基本信息
     *
     * @param request 协议修改请求对象，包含需要修改的协议信息
     * @return 返回修改操作结果
     */
    @Override
    public SupportResult<Boolean> modifyAgreement(CommonAgreementInfoModifyRequest request) {
        SupportResult<CommonAgreementInfoDO> agreementResult =   checkAndGetAgreementInfo(request);
        if(agreementResult.ifFail()){
            return SupportResult.fail(agreementResult.getCode(),agreementResult.getMessage());
        }
        CommonAgreementInfoDO infoDO = agreementResult.getData();

        CommonAgreementInfoDO entityDO = new CommonAgreementInfoDO();
        entityDO.setId(infoDO.getId());
        entityDO.setReleaseVersion(request.getReleaseVersion());
        entityDO.setAgreementTitle(request.getCommonAgreementTitle());
        entityDO.setAgreementMark(request.getCommonAgreementMark());
        entityDO.setCategoryRef(request.getCategory());
        entityDO.setAgreementType(request.getCommonAgreementType());
        entityDO.setOperator(request.getOperator());
        agreementInfoDAO.updateById(entityDO);
        return SupportResult.success(true);
    }

    /**
     * 变更协议状态
     *
     * @param request 协议状态变更请求对象，包含协议ID和新的状态信息
     * @return 返回状态变更操作结果
     */
    @Override
    public SupportResult<Boolean> changeAgreementStatus(CommonAgreementChangeStatusRequest request) {

        SupportResult<CommonAgreementInfoDO> agreementResult =   checkAndGetAgreementInfo(request);
        if(agreementResult.ifFail()){
            return SupportResult.fail(agreementResult.getCode(),agreementResult.getMessage());
        }
        CommonAgreementInfoDO infoDO = agreementResult.getData();
        if(CommonAgreementStatusEnum.enable(infoDO.getStatus())){
            return SupportResult.fail(CommonAgreementErrorCodeEnum.AGR_INFO_ENABLE_CANNOT_CHANGE);
        }
        CommonAgreementInfoDO entityDO = new CommonAgreementInfoDO();
        entityDO.setId(infoDO.getId());
        entityDO.setStatus(request.getStatus());
        entityDO.setOperator(request.getOperator());
        agreementInfoDAO.updateById(entityDO);
        return SupportResult.success(true);
    }

    @Override
    public SupportResult<String> agreementContentAdd(CommonAgreementContentAddRequest request) {
        if(request == null){
            return  SupportResult.fail(CommonResultEnum.NULL_ARGUMENT,"请求参数不能未空");
        }

        if(StringUtils.isBlank(request.getCommonAgreementId())){
            return  SupportResult.fail(CommonResultEnum.NULL_ARGUMENT,"请求协议编号参数不能未空");
        }
        if(StringUtils.isBlank(request.getReleaseVersion())){
            return  SupportResult.fail(CommonResultEnum.NULL_ARGUMENT,"请求协议发布版本参数不能未空");
        }
        if(StringUtils.isBlank(request.getLang())){
            return  SupportResult.fail(CommonResultEnum.NULL_ARGUMENT,"请求协议发布语言参数不能未空");
        }
        SupportResult<CommonAgreementInfoDO> agreementResult =   checkAndGetAgreementInfo(request);
        if(agreementResult.ifFail()){
            return SupportResult.fail(agreementResult.getCode(),agreementResult.getMessage());
        }

        CommonAgreementLangEnum langEnum = CommonAgreementLangEnum.getByLang(request.getLang());

        CommonAgreementReleaseRecordDO releaseRecordDO = new CommonAgreementReleaseRecordDO();
        String releaseRecordId = CommonAgreementIdTool.markAgreementReleaseId();
        releaseRecordDO.setEnableStatus(CommonAgreementReleaseStatusEnum.RELEASE_NOT_ENABLE.getCode());
        releaseRecordDO.setReleaseRecordId(releaseRecordId);
        releaseRecordDO.setReleaseVersion(request.getReleaseVersion());
        releaseRecordDO.setLang(langEnum.getLang());
        releaseRecordDO.setAgreementId(request.getCommonAgreementId());
        releaseRecordDO.setShortName(request.getShortName());
        releaseRecordDO.setFullName(request.getFullName());
        releaseRecordDO.setCreator(request.getCreator());
        releaseRecordDO.setOperator(request.getOperator());
        releaseRecordDO.setContent(request.getContent());
        releaseRecordDO.setContentType(request.getContentType());
        releaseRecordDO.setReleaseRemark(request.getReleaseRemark());

        try{
            releaseRecordDAO.save(releaseRecordDO);
        } catch (Exception e){
            log.error("agreementContentAdd error",e);
            return  SupportResult.fail(CommonResultEnum.RPC_INVOKE_ERR,"agreementContentAdd to db fail" );
        }

        return SupportResult.success(releaseRecordId);
    }

    @Override
    public SupportResult<Boolean> agreementContentModify(CommonAgreementContentModifyRequest request) {
        SupportResult<CommonAgreementReleaseRecordDO> agreementResult =   checkAndGetReleaseRecord(request);
        if(agreementResult.ifFail()){
            return SupportResult.fail(agreementResult.getCode(),agreementResult.getMessage());
        }
        CommonAgreementReleaseRecordDO releaseRecordDO = agreementResult.getData();
        if(CommonAgreementReleaseStatusEnum.enable(releaseRecordDO.getEnableStatus())){
            return SupportResult.fail(CommonAgreementErrorCodeEnum.AGR_RELEASE_ENABLE_CANNOT_CHANGE);
        }
        CommonAgreementReleaseRecordDO entityDO = new CommonAgreementReleaseRecordDO();
        entityDO.setId(releaseRecordDO.getId());
        entityDO.setShortName(request.getShortName());
        entityDO.setFullName(request.getFullName());
        entityDO.setOperator(request.getOperator());
        entityDO.setContent(request.getContent());
        entityDO.setContentType(request.getContentType());
        entityDO.setReleaseRemark(request.getReleaseRemark());
        releaseRecordDAO.updateById(entityDO);
        return SupportResult.success(true);
    }

    @Override
    public SupportResult<Boolean> agreementContentChangeStatus(CommonAgreementContentStatustChangeRequest request) {
        SupportResult<CommonAgreementReleaseRecordDO> releaseRecordSupportResult =   checkAndGetReleaseRecord(request);
        if(releaseRecordSupportResult.ifFail()){
            return SupportResult.fail(releaseRecordSupportResult.getCode(),
                    releaseRecordSupportResult.getMessage());
        }
        CommonAgreementReleaseRecordDO releaseRecordDO = releaseRecordSupportResult.getData();
        if(CommonAgreementReleaseStatusEnum.enable(releaseRecordDO.getEnableStatus())){
            return SupportResult.fail(CommonAgreementErrorCodeEnum.AGR_RELEASE_ENABLE_CANNOT_CHANGE);
        }
        CommonAgreementInfoDO infoDO = getOneByAgreementId(releaseRecordDO.getAgreementId());
        if(infoDO == null){
            return SupportResult.fail(CommonAgreementErrorCodeEnum.AGR_INFO_NOT_FOUND);
        }

        boolean enable = CommonAgreementReleaseStatusEnum.enable(request.getEnableStatus());
        CommonAgreementReleaseRecordDO recordEntity = new CommonAgreementReleaseRecordDO();
        recordEntity.setId(releaseRecordDO.getId());
        recordEntity.setEnableStatus(request.getEnableStatus());
        recordEntity.setOperator(request.getOperator());
        if(enable){
            recordEntity.setReleaseTime(new Date());
        }
        releaseRecordDAO.updateById(recordEntity);


        if(enable){
            CommonAgreementInfoDO entityDO = new CommonAgreementInfoDO();
            entityDO.setId(infoDO.getId());
            entityDO.setReleaseVersion(releaseRecordDO.getReleaseVersion());
            entityDO.setOperator(request.getOperator());
            agreementInfoDAO.updateById(entityDO);
        }
        return SupportResult.success(true);
    }

    private SupportResult<CommonAgreementInfoDO> checkAndGetAgreementInfo(
            CommonAgreementBaseRequest baseRequest){
        if(baseRequest == null){
            return  SupportResult.fail(CommonResultEnum.NULL_ARGUMENT,"请求参数不能未空");
        }
        String commonAgreementId = baseRequest.getCommonAgreementId();
        if(commonAgreementId == null){
            return  SupportResult.fail(CommonResultEnum.NULL_ARGUMENT,"请求协议编号参数不能未空");
        }
        CommonAgreementInfoDO infoDO = getOneByAgreementId(commonAgreementId);
        if(infoDO == null){
            return  SupportResult.fail(CommonAgreementErrorCodeEnum.AGR_INFO_NOT_FOUND);
        }
        return SupportResult.success(infoDO);
    }


    private SupportResult<CommonAgreementReleaseRecordDO> checkAndGetReleaseRecord(
            CommonAgreementContentBaseRequest baseRequest){
        if(baseRequest == null){
            return  SupportResult.fail(CommonResultEnum.NULL_ARGUMENT,"请求参数不能未空");
        }
        String releaseRecordId = baseRequest.getReleaseRecordId();
        if(releaseRecordId == null){
            return  SupportResult.fail(CommonResultEnum.NULL_ARGUMENT,"请求协议发布记录ID参数不能未空");
        }
        CommonAgreementReleaseRecordDO infoDO = getOneByReleaseRecordId(releaseRecordId);
        if(infoDO == null){
            return  SupportResult.fail(CommonAgreementErrorCodeEnum.AGR_RELEASE_VERSION_NOT_FOUND);
        }
        return SupportResult.success(infoDO);
    }


    private CommonAgreementInfoDO getOneByAgreementId(String AgreementId) {
        CommonAgreementInfoDO agreementInfoDO = agreementInfoDAO.getOneAndNotDeleted(AgreementId);
        return agreementInfoDO;
    }

    private CommonAgreementReleaseRecordDO getOneByReleaseRecordId(String ReleaseRecordId) {
        CommonAgreementReleaseRecordDO releaseRecordDO = releaseRecordDAO.getOne(ReleaseRecordId);
        return releaseRecordDO;
    }


}
