package com.timevale.infoauth.service;

import com.timevale.framework.puppeteer.enums.PropertyChangeType;
import com.timevale.framework.puppeteer.model.ConfigChange;
import com.timevale.framework.puppeteer.model.ConfigChangeEvent;
import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.impl.config.AutoConfig;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.Test;

/**
 * <AUTHOR> href="mailto:<EMAIL>">jiuchen</a>
 */
@JsonDataIgnore
public class AutoConfigTest extends ApplicationTest {

  @Autowired
  private AutoConfig autoConfig;

  @Test
  public void configListener() {
    String namespace = "application";
    Map<String, ConfigChange> changes = new HashMap<>();
    ConfigChange configChange1 =
        new ConfigChange(
            "application",
            "idcard2Element.shujubao.code.msg.mapping.stauts.failure",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            PropertyChangeType.MODIFIED);
    ConfigChange configChange2 =
        new ConfigChange(
            "application",
            "mobile3Element.jingzhong.code.msg.mapping.stauts.failure",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            PropertyChangeType.MODIFIED);
    ConfigChange configChange3 =
        new ConfigChange(
            "application",
            "mobile3Element.shujubao.code.msg.mapping.stauts.failure",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            PropertyChangeType.MODIFIED);
    ConfigChange configChange4 =
        new ConfigChange(
            "application",
            "mobile3Element.hchx.code.msg.mapping.stauts.failure",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            PropertyChangeType.MODIFIED);
    ConfigChange configChange5 =
        new ConfigChange(
            "application",
            "bankcard3Element.hchx.code.msg.mapping.stauts.failure",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            PropertyChangeType.MODIFIED);
    ConfigChange configChange6 =
        new ConfigChange(
            "application",
            "bankcard4Element.hchx.code.msg.mapping.stauts.failure",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            PropertyChangeType.MODIFIED);
    ConfigChange configChange7 =
        new ConfigChange(
            "application",
            "bankcard4Element.shujubao.code.msg.mapping.stauts.failure",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            PropertyChangeType.MODIFIED);
    ConfigChange configChange8 =
        new ConfigChange(
            "application",
            "ocr.huawei.code.msg.mapping.stauts.failure",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            PropertyChangeType.MODIFIED);
    ConfigChange configChange9 =
        new ConfigChange(
            "application",
            "ocr.tencent.code.msg.mapping.stauts.failure",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            PropertyChangeType.MODIFIED);
    ConfigChange configChange10 =
        new ConfigChange(
            "application",
            "bankcard3Element.unionpay.code.msg.mapping.stauts.failure",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            PropertyChangeType.MODIFIED);
    ConfigChange configChange11 =
        new ConfigChange(
            "application",
            "bankcard4Element.unionpay.code.msg.mapping.stauts.failure",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            PropertyChangeType.MODIFIED);
    ConfigChange configChange12 =
        new ConfigChange(
            "application",
            "bankcard4ElementInternational.unionpay.code.msg.mapping.stauts.failure",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            "2核查不一致,-255,-1,40401库中无此号",
            PropertyChangeType.MODIFIED);
    ConfigChange configChange13 =
        new ConfigChange(
            "application",
            "file.system.internal.url.switch",
            "false",
            "true",
            "2false",
            "true",
            PropertyChangeType.MODIFIED);

    changes.put("idcard2Element.shujubao.code.msg.mapping.stauts.failure", configChange1);
    changes.put("mobile3Element.jingzhong.code.msg.mapping.stauts.failure", configChange2);
    changes.put("mobile3Element.shujubao.code.msg.mapping.stauts.failure", configChange3);
    changes.put("mobile3Element.hchx.code.msg.mapping.stauts.failure", configChange4);
    changes.put("bankcard3Element.hchx.code.msg.mapping.stauts.failure", configChange5);
    changes.put("bankcard4Element.hchx.code.msg.mapping.stauts.failure", configChange6);
    changes.put("bankcard4Element.shujubao.code.msg.mapping.stauts.failure", configChange7);
    changes.put("ocr.huawei.code.msg.mapping.stauts.failure", configChange8);
    changes.put("ocr.tencent.code.msg.mapping.stauts.failure", configChange9);
    changes.put("bankcard3Element.unionpay.code.msg.mapping.stauts.failure", configChange10);
    changes.put("bankcard4Element.unionpay.code.msg.mapping.stauts.failure", configChange11);
    changes.put("bankcard4ElementInternational.unionpay.code.msg.mapping.stauts.failure", configChange12);
    changes.put("file.system.internal.url.switch", configChange13);
    ConfigChangeEvent changeEvent = new ConfigChangeEvent(namespace, changes);
    autoConfig.configListener(changeEvent);
  }
}
