package com.timevale.infoauth.service.agreement.impl;

import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementInfoDO;
import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementInfoExtendDO;
import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementReleaseRecordDO;
import com.timevale.infoauth.service.agreement.config.CommonAgreementConstant;
import com.timevale.infoauth.service.enums.agreement.CommonAgreementReleaseStatusEnum;
import com.timevale.infoauth.service.enums.agreement.CommonAgreementStatusEnum;
import com.timevale.infoauth.service.response.agreement.AgreementReleaseRecordBaseBO;
import com.timevale.infoauth.service.response.agreement.AgreementReleaseRecordBO;
import com.timevale.infoauth.service.response.agreement.CommonAgreementInfoBO;
import com.timevale.infoauth.service.response.agreement.CommonAgreementInfoExtendBO;

import java.util.Objects;

/**
 * 协议响应转换器 提供将协议相关数据对象转换为响应对象的服务
 *
 * <AUTHOR>
 */
public class CommonAgreementResponseConverter {

  /**
   * 将扩展信息数据对象转换为响应对象
   *
   * @param response 扩展信息响应对象
   * @param infoExtendDO 扩展信息数据对象
   */
  public static void convertExtendInfoResponse(
          CommonAgreementInfoExtendBO response, CommonAgreementInfoExtendDO infoExtendDO) {
    convertInfoResponse(response, infoExtendDO);
    response.setReleaseRecordId(infoExtendDO.getReleaseRecordId());
    response.setReleaseVersion(infoExtendDO.getReleaseVersion());
    response.setReleaseTime(infoExtendDO.getReleaseTime().getTime());
    response.setContentFill(Objects.equals(infoExtendDO.getContentFill(), CommonAgreementConstant.YES));
    response.setShortName(infoExtendDO.getShortName());
    response.setFullName(infoExtendDO.getFullName());
    response.setLang(infoExtendDO.getLang());
  }

  /**
   * 将基本信息数据对象转换为响应对象
   *
   * @param response 基本信息响应对象
   * @param agreementInfoDO 基本信息数据对象
   */
  public static void convertInfoResponse(
          CommonAgreementInfoBO response, CommonAgreementInfoDO agreementInfoDO) {
    response.setCommonAgreementId(agreementInfoDO.getAgreementId());
    response.setCommonAgreementTitle(agreementInfoDO.getAgreementTitle());
    response.setReleaseVersion(agreementInfoDO.getReleaseVersion());
    response.setCategory(agreementInfoDO.getCategoryRef());
    response.setCategoryDesc(agreementInfoDO.getCategoryRef());
    response.setCreateTime(agreementInfoDO.getCreateTime().getTime());
    response.setModifyTime(agreementInfoDO.getModifyTime().getTime());

    CommonAgreementStatusEnum statusEnum =
        CommonAgreementStatusEnum.getByStatus(agreementInfoDO.getStatus());
    response.setStatus(statusEnum.getCode());
    response.setStatusDesc(statusEnum.getMessage());
  }

  /**
   * 将发布记录数据对象转换为基础响应对象
   *
   * @param response 发布记录基础响应对象
   * @param recordDO 发布记录数据对象
   */
  public static void convertReleasedBase(
          AgreementReleaseRecordBaseBO response, CommonAgreementReleaseRecordDO recordDO) {
    CommonAgreementReleaseStatusEnum statusEnum =
        CommonAgreementReleaseStatusEnum.getByStatus(recordDO.getEnableStatus());
    response.setCreateTime(recordDO.getCreateTime().getTime());
    response.setReleaseRecordId(recordDO.getReleaseRecordId());
    response.setReleaseRemark(recordDO.getReleaseRemark());
    response.setReleaseVersion(recordDO.getReleaseVersion());
    response.setReleaseTime(recordDO.getReleaseTime().getTime());
    response.setContentFill(Objects.equals(recordDO.getContentFill(), CommonAgreementConstant.YES));
    response.setShortName(recordDO.getShortName());
    response.setFullName(recordDO.getFullName());
    response.setLang(recordDO.getLang());
    response.setEnableStatus(statusEnum.getCode());
    response.setEnableStatusDesc(statusEnum.getMessage());
  }

  /**
   * 将发布记录数据对象转换为详细响应对象
   *
   * @param response 发布记录详细响应对象
   * @param recordDO 发布记录数据对象
   */
  public static void convertReleasedInfo(
          AgreementReleaseRecordBO response, CommonAgreementReleaseRecordDO recordDO) {
    convertReleasedBase(response, recordDO);
    response.setContext(recordDO.getContent());
    response.setContextType(recordDO.getContentType());
  }
}
