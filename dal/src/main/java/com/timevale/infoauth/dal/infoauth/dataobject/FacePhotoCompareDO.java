package com.timevale.infoauth.dal.infoauth.dataobject;

import lombok.Data;

import java.util.Date;

/** 人脸照片比对 */
@Data
public class FacePhotoCompareDO {

  /** id 自增主键. */
  private Long id;
  /** name 姓名. */
  private String name;
  /** appId 应用id. */
  private String appId;
  /** bizId 业务id. */
  private String bizId;
  /** certNo 证件号码. */
  private String certNo;
  /** requestId 请求id 我们生成的请求id */
  private String requestId;
  /** infoauthId 信息比对id. */
  private String infoauthId;
  /** providerId 供应商. */
  private String providerId;
  /** providerResult 服务提供商返回的代码. */
  private String providerResult;
  /** result 比对结果. */
  private Integer result;
  /** bizType */
  private Integer bizType;
  /** serviceStatus SERVICE_STATUS. */
  private Integer serviceStatus;
  /** 供应商返回的 请求id */
  private String responseId;
  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录更新时间. */
  private Date modifyTime;
}
