package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.InfoErrAdapterDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/** 供应商错误码映射表 */
public interface InfoErrAdapterDAO {

  @Insert({
    "<script>",
    "INSERT INTO `info_err_adapter` (`biz_type`, `provider`, `provider_result`, `provider_err_code`, ",
    "`provider_err_msg`, `adapter_err_code`, `adapter_err_msg`, `biz_md5`, `extend`) VALUES ",
    "(#{bizType}, #{provider}, #{providerErrMsg},",
    "#{providerErrCode}, #{providerErrMsg}, #{adapterErrCode}, #{adapterErrMsg}, #{bizMd5}, #{extend})",
    "</script>"
  })
  @Options(useGeneratedKeys = true, keyColumn = "id")
  int insert(InfoErrAdapterDO entity);

  @Delete("DELETE FROM `info_err_adapter` WHERE `id` = #{id}")
  int deleteById(Long id);

  @Select(
      "SELECT `id`, `biz_type`, `provider`, `provider_err_code`,`provider_err_msg`, `adapter_err_code`, `adapter_err_msg`, `biz_md5` FROM `info_err_adapter` WHERE `id` = #{id}")
  InfoErrAdapterDO getBaseById(Long id);

  @Select(
      "SELECT `id`, `biz_type`, `provider`, `provider_result`, `provider_err_code`,`provider_err_msg`, `adapter_err_code`, `adapter_err_msg`, `biz_md5`, `extend` FROM `info_err_adapter` WHERE `id` = #{id}")
//  @Results(
//      @Result(
//          column = "provider_result",
//          property = "providerResult",
//          typeHandler = TextHandler.class))
  InfoErrAdapterDO getDetailById(Long id);

  @Select(
          "SELECT `id`, `biz_type`, `provider`, `provider_result`, `provider_err_code`,`provider_err_msg`, `adapter_err_code`, `adapter_err_msg`, `biz_md5`, `extend` FROM `info_err_adapter` WHERE `biz_md5` = #{md5}")
//  @Results(
//          @Result(
//                  column = "provider_result",
//                  property = "providerResult",
//                  typeHandler = TextHandler.class))
  InfoErrAdapterDO getDetailByMd5(String md5);

  @Update({
    "<script>",
    "UPDATE `info_err_adapter` ",
    "<set>",
    "<if test=\"adapterErrCode != null\">",
    "adapter_err_code = #{adapterErrCode},",
    "</if>",
    "<if test=\"adapterErrMsg != null\">",
    "adapter_err_msg = #{adapterErrMsg},",
    "</if>",
    "</set>",
    " WHERE `id` = #{id}",
    "</script>",
  })
  void updateAdapterMsgById(InfoErrAdapterDO entity);

  @Select({
          "<script>",
          "SELECT COUNT(*) FROM `info_err_adapter`",
          "</script>",
  })
  int getListCount();

  @Select({
    "<script>",
    "SELECT * FROM `info_err_adapter` ORDER BY id DESC LIMIT #{pageIndex},#{pageSize}",
    "</script>",
  })
//  @Results(
//      @Result(
//          column = "provider_result",
//          property = "providerResult",
//          typeHandler = TextHandler.class))
  List<InfoErrAdapterDO> getListPages(
      @Param("pageIndex") int pageIndex, @Param("pageSize") int pageSize);
}
