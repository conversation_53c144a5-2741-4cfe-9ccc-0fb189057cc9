<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.timevale.infoauth</groupId>
		<artifactId>infoauth-parent</artifactId>
		<version>1.0.0</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>infoauth-deploy</artifactId>
	<name>infoauth/deploy</name>
	<packaging>jar</packaging>

	<properties>
		<capuslue.maven.plugin.version>1.5.1</capuslue.maven.plugin.version>
		<tgtest.base.version>1.2-SNAPSHOT</tgtest.base.version>
        <aspectj.version>1.9.4</aspectj.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>infoauth-service</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>monitor-core</artifactId>
			<version>${project.version}</version>
		</dependency>


		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>common-agreement</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- ???? -->
		<dependency>
			<groupId>com.timevale</groupId>
			<artifactId>capsule-linux-daemon</artifactId>
		</dependency>
		<!--????-->
		<dependency>
			<groupId>tgtest</groupId>
			<artifactId>tgtest-base</artifactId>
			<version>${tgtest.base.version}</version>

			<exclusions>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-lang3</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>mysql-connector-java</artifactId>
					<groupId>mysql</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
			</exclusions>
			<scope>test</scope>
		</dependency>


		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<version>4.3.20.RELEASE</version>
		</dependency>

	</dependencies>

	<build>
        <finalName>infoauth-service-capsule</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>1.5.10.RELEASE</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <mainClass>com.timevale.infoauth.deploy.Application</mainClass>
                </configuration>
            </plugin>
            
			
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.0</version>
				<executions>
					<execution>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>report</id>
						<phase>test</phase>
						<goals>
							<goal>report-aggregate</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<propertyName>surefireArgLine</propertyName>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.0.0-M3</version>
				<configuration>
					<suiteXmlFiles>
						<suiteXmlFile>src/test/resources/testng.xml</suiteXmlFile>
					</suiteXmlFiles>
					<argLine>
						-javaagent:"${settings.localRepository}/org/aspectj/aspectjweaver/${aspectj.version}/aspectjweaver-${aspectj.version}.jar"
					</argLine>
					<argLine>${surefireArgLine}</argLine>
					<parallel>classes</parallel>
					<threadCount>4</threadCount>
				</configuration>
			</plugin>
		</plugins>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
				<excludes>
					<exclude>**/env/**</exclude>
					<exclude>application.yml</exclude>
					<exclude>systemconfig.properties</exclude>
					<exclude>application.properties</exclude>
				</excludes>
			</resource>
		</resources>
	</build>
	<profiles>
		<profile>
			<id>dev</id>
			<build>
				<resources>
					<resource>
						<directory>src/main/resources/env/dev</directory>
					</resource>
				</resources>
			</build>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile>
			<id>test</id>
			<build>
				<resources>
					<resource>
						<directory>src/main/resources/env/test</directory>
					</resource>
				</resources>
			</build>
		</profile>
		<profile>
			<id>pre</id>
			<build>
				<resources>
					<resource>
						<directory>src/main/resources/env/pre</directory>
					</resource>
				</resources>
			</build>
		</profile>
		<profile>
			<id>prod</id>
			<build>
				<resources>
					<resource>
						<directory>src/main/resources/env/prod</directory>
					</resource>
				</resources>
			</build>
		</profile>
	</profiles>
</project>