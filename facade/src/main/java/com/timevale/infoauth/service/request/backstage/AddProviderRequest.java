package com.timevale.infoauth.service.request.backstage;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AddProviderRequest extends ToString {
    private String url;
    private String disc;
    private String name;
    private String secret;
    private String account;
    private String signKey;
    private String verifyKey;
    private Integer level;
    private Integer signType;
    private Integer type;
    private Integer status;
    private Integer weight;
}
