package com.timevale.infoauth.service.api;

import com.alibaba.fastjson.JSON;
import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.dal.infoauth.dao.InfoServiceDAO;
import com.timevale.infoauth.dal.infoauth.dao.InfoServiceIdnoauthDAO;
import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceDO;
import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceIdnoauthDO;
import com.timevale.infoauth.dal.infoauth.facade.AuthResultQueryRecordDTO;
import com.timevale.infoauth.model.enums.ObjectType;
import com.timevale.infoauth.model.input.IdnoAuthInput;
import com.timevale.infoauth.model.output.IdnoAuthOutput;
import com.timevale.infoauth.service.enums.InfoAuthServiceType;
import com.timevale.infoauth.service.impl.biz.impl.details.IdnoAuthServiceImpl;
import com.timevale.infoauth.service.impl.common.dto.Provout;
import com.timevale.infoauth.service.impl.constants.ProviderEnum;
import com.timevale.infoauth.service.impl.dto.PersonProvin;
import com.timevale.infoauth.service.impl.dto.PersonProvout;
import com.timevale.infoauth.service.impl.dto.TelecomauthProvin;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.Psn2Adaptors;
import com.timevale.infoauth.service.impl.provider.biz.adaptors.TelecomAdaptors;
import com.timevale.infoauth.service.impl.provider.biz.api.XReceiver;
import com.timevale.infoauth.service.impl.provider.client.chuanglan.ChuanglanIdCard2ElementClient;
import com.timevale.infoauth.service.impl.provider.client.shumai.ShumaiIdCard2ElementClient;
import com.timevale.infoauth.service.impl.utils.UUIDUtil;
import com.timevale.infoauth.utils.CollectionUtils;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2020/4/16 下午5:28
 */
@JsonDataIgnore
public class IdnoAuthServiceImplTest extends ApplicationTest {

  @Resource IdnoAuthServiceImpl idnoAuthService;

  @Resource InfoServiceIdnoauthDAO infoServiceIdnoauthDAO;

  @Resource InfoServiceDAO infoServiceDAO;

  @Test
  public void doAuthCache() {
    RequestContext.setTransactionId(UUID.randomUUID().toString());
    IdnoAuthInput input = new IdnoAuthInput();
    input.setIdno("******************");
    input.setName("姜娇");
    input.setInfoauthId(UUIDUtil.generateUUID());
    input.setObjectType(ObjectType.PSN_IDCARD);
    input.setServiceType(InfoAuthServiceType.IDCARD_2_ELEMENT);

    AuthResultQueryRecordDTO authResultQueryRecordDTO = new AuthResultQueryRecordDTO();
    authResultQueryRecordDTO.setPageIndex(0);
    authResultQueryRecordDTO.setPageSize(1);
    authResultQueryRecordDTO.setCertNo(input.getIdno());
    List<InfoServiceIdnoauthDO> serviceList =
        infoServiceIdnoauthDAO.getIdnoAuthByAuthResultQuery(authResultQueryRecordDTO);
    Assert.assertNotNull(serviceList);
    Assert.assertTrue(CollectionUtils.isNotEmpty(serviceList));

    if (CollectionUtils.isNotEmpty(serviceList)) {
      InfoServiceIdnoauthDO infoServiceIdnoauthDO = serviceList.get(0);
      InfoServiceDO infoServiceDO =
          infoServiceDAO.getByInfoAuthId(infoServiceIdnoauthDO.getInfoauthId());
      input.setInfoauthId(infoServiceDO.getInfoauthId());
      input.setInfoServiceDO(infoServiceDO);

      IdnoAuthOutput out = idnoAuthService.doAuth(input);
      Assert.assertEquals(out.isPass(), true);
    }
  }

  @Test
  public void testBindingAppIdDoAuthCache() {
    RequestContext.setTransactionId(UUID.randomUUID().toString());
    IdnoAuthInput input = new IdnoAuthInput();
    input.setBizAppId("1111563841");
    //    input.setBizAppId("7876547810");
    input.setIdno("******************");
    input.setName("姜娇");
    input.setInfoauthId(UUIDUtil.generateUUID());
    input.setObjectType(ObjectType.PSN_IDCARD);
    input.setServiceType(InfoAuthServiceType.IDCARD_2_ELEMENT);

    AuthResultQueryRecordDTO authResultQueryRecordDTO = new AuthResultQueryRecordDTO();
    authResultQueryRecordDTO.setPageIndex(0);
    authResultQueryRecordDTO.setPageSize(1);
    authResultQueryRecordDTO.setCertNo(input.getIdno());
    List<InfoServiceIdnoauthDO> serviceList =
        infoServiceIdnoauthDAO.getIdnoAuthByAuthResultQuery(authResultQueryRecordDTO);
    Assert.assertNotNull(serviceList);
    Assert.assertTrue(CollectionUtils.isNotEmpty(serviceList));

    if (CollectionUtils.isNotEmpty(serviceList)) {
      InfoServiceIdnoauthDO infoServiceIdnoauthDO = serviceList.get(0);
      InfoServiceDO infoServiceDO =
              infoServiceDAO.getByInfoAuthId(infoServiceIdnoauthDO.getInfoauthId());
      input.setInfoauthId(infoServiceDO.getInfoauthId());
      input.setInfoServiceDO(infoServiceDO);

      IdnoAuthOutput out = idnoAuthService.doAuth(input);
      Assert.assertEquals(out.isPass(), true);
    }
  }


  @Test
  public void testCLIdentityauthValid() {


    Psn2Adaptors psn2Adaptors = new Psn2Adaptors();
    XReceiver clIdentityauthValid = psn2Adaptors.CL_identityauthValid;

    PersonProvin input = new PersonProvin();
    input.setCertNumber("******************test");
    input.setName("姜娇");
    input.setProviderName(ProviderEnum.CHUANGLAN.name());
    input.setServiceType(InfoAuthServiceType.IDCARD_2_ELEMENT);

    PersonProvout apply = (PersonProvout) clIdentityauthValid.apply(input);

    logger.info(" testCLIdentityauthValid apply: {}" + JSON.toJSONString(apply));


    ChuanglanIdCard2ElementClient.handleIdentityAuthResponse("{\"chargeStatus\":0,\"message\":\"校验异常：身份证格式不正确\",\"code\":\"400001\"}", apply);
    Assert.assertEquals(apply.getFee(), 0);

    ChuanglanIdCard2ElementClient.handleIdentityAuthResponse("{\"chargeStatus\":1,\"message\":\"成功\",\"data\":{\"orderNo\":\"021713172566292509\",\"handleTime\":\"\",\"result\":\"01\",\"remark\":\"一致\",\"province\":\"江西省\",\"city\":\"宜春地区\",\"country\":\"丰城市\",\"birthday\":\"19950930\",\"age\":\"29\",\"gender\":\"1\"},\"code\":\"200000\"}",
            apply);
    ChuanglanIdCard2ElementClient.handleIdentityAuthResponse("{\"chargeStatus\":1,\"message\":\"成功\",\"data\":{\"orderNo\":\"021713172566292509\",\"handleTime\":\"\",\"result\":\"02\",\"remark\":\"一致\",\"province\":\"江西省\",\"city\":\"宜春地区\",\"country\":\"丰城市\",\"birthday\":\"19950930\",\"age\":\"29\",\"gender\":\"1\"},\"code\":\"200000\"}",
            apply);
    ChuanglanIdCard2ElementClient.handleIdentityAuthResponse("{\"chargeStatus\":1,\"message\":\"成功\",\"data\":{\"orderNo\":\"021713172566292509\",\"handleTime\":\"\",\"result\":\"03\",\"remark\":\"一致\",\"province\":\"江西省\",\"city\":\"宜春地区\",\"country\":\"丰城市\",\"birthday\":\"19950930\",\"age\":\"29\",\"gender\":\"1\"},\"code\":\"200000\"}",
            apply);
    ChuanglanIdCard2ElementClient.handleIdentityAuthResponse("{\"chargeStatus\":1,\"message\":\"成功\",\"data\":{\"orderNo\":\"021713172566292509\",\"handleTime\":\"\",\"result\":\"04\",\"remark\":\"一致\",\"province\":\"江西省\",\"city\":\"宜春地区\",\"country\":\"丰城市\",\"birthday\":\"19950930\",\"age\":\"29\",\"gender\":\"1\"},\"code\":\"200000\"}",
            apply);
    ChuanglanIdCard2ElementClient.handleIdentityAuthResponse("{\"chargeStatus\":1,\"message\":\"成功\",\"data\":{\"orderNo\":\"021713172566292509\",\"handleTime\":\"\",\"result\":\"066\",\"remark\":\"一致\",\"province\":\"江西省\",\"city\":\"宜春地区\",\"country\":\"丰城市\",\"birthday\":\"19950930\",\"age\":\"29\",\"gender\":\"1\"},\"code\":\"200000\"}",
            apply);
    Assert.assertEquals(apply.getFee(), 1);


  }

  @Test
  public void testSM_identityauthValid() {
    Psn2Adaptors psn2Adaptors = new Psn2Adaptors();
    XReceiver clIdentityauthValid = psn2Adaptors.SM_identityauthValid;

    PersonProvin input = new PersonProvin();
    input.setCertNumber("******************test");
    input.setName("姜娇");
    input.setProviderName(ProviderEnum.SHUMAI.name());
    input.setServiceType(InfoAuthServiceType.IDCARD_2_ELEMENT);

    PersonProvout apply = (PersonProvout) clIdentityauthValid.apply(input);

    logger.info(" SM_identityauthValid apply: {}" + JSON.toJSONString(apply));


    ShumaiIdCard2ElementClient.handleIdentityAuthResponse("{\"msg\":\"appid 不能为空\",\"success\":false,\"code\":400,\"data\":null}", apply);
    ShumaiIdCard2ElementClient.handleIdentityAuthResponse("{\"msg\":\"成功\",\"success\":true,\"code\":200,\"data\":{\"result\":1,\"order_no\":\"011790641461604495\",\"desc\":\"一致\",\"sex\":\"男\",\"birthday\":\"19950930\",\"address\":\"江西省宜春地区丰城市\"}}", apply);
    ShumaiIdCard2ElementClient.handleIdentityAuthResponse("{\"msg\":\"成功\",\"success\":true,\"code\":200,\"data\":{\"result\":2,\"order_no\":\"011790641461604495\",\"desc\":\"一致\",\"sex\":\"男\",\"birthday\":\"19950930\",\"address\":\"江西省宜春地区丰城市\"}}", apply);
    ShumaiIdCard2ElementClient.handleIdentityAuthResponse("{\"msg\":\"成功\",\"success\":true,\"code\":200,\"data\":{\"result\":3,\"order_no\":\"011790641461604495\",\"desc\":\"一致\",\"sex\":\"男\",\"birthday\":\"19950930\",\"address\":\"江西省宜春地区丰城市\"}}", apply);
    ShumaiIdCard2ElementClient.handleIdentityAuthResponse("{\"msg\":\"成功\",\"success\":true,\"code\":200,\"data\":{\"result\":0,\"order_no\":\"011790641461604495\",\"desc\":\"一致\",\"sex\":\"男\",\"birthday\":\"19950930\",\"address\":\"江西省宜春地区丰城市\"}}", apply);

    Assert.assertEquals(apply.getFee(), 1);
  }




    @Test
    public void testPsn2AdaptorsXM() {
        Psn2Adaptors psn2Adaptors = new Psn2Adaptors();
        XReceiver clIdentityauthValid = psn2Adaptors.XM_identityauthValid;

        PersonProvin input = new PersonProvin();
        input.setCertNumber("******************test");
        input.setName("姜娇");
        input.setProviderName(ProviderEnum.SHUMAI.name());
        input.setServiceType(InfoAuthServiceType.IDCARD_2_ELEMENT);

        PersonProvout apply = (PersonProvout) clIdentityauthValid.apply(input);

        Assert.assertEquals(apply.getFee(), 0);
        logger.info(" SM_identityauthValid apply: {}" + JSON.toJSONString(apply));

    }


    @Test
    public void testTelecomAdaptorsXM() {
        TelecomAdaptors adaptors = new TelecomAdaptors();

        XReceiver clIdentityauthValid = adaptors.XUANMING_auth;

        TelecomauthProvin input = new TelecomauthProvin();
        input.setCertNumber("******************test");
        input.setName("姜娇");
        input.setMobile("13123919619");
        input.setProviderName(ProviderEnum.SHUMAI.name());
        input.setServiceType(InfoAuthServiceType.IDCARD_2_ELEMENT);

        Provout apply = (Provout) clIdentityauthValid.apply(input);

        Assert.assertEquals(apply.getFee(), 0);
        logger.info(" SM_identityauthValid apply: {}" + JSON.toJSONString(apply));
    }
}
