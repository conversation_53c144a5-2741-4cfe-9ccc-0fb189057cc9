package com.timevale.infoauth.service.api;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.dto.PSelectDTO;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
@JsonDataIgnore
public class ProviderSelectServiceTest extends ApplicationTest {

  @Resource private ProviderSelectService providerSelectService;

  @Test
  public void saveTest() {
    List<PSelectDTO> list = providerSelectService.querySelectAll();
    Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    PSelectDTO pSelectDTO = new PSelectDTO();
    pSelectDTO.setId(list.get(0).getId());
    pSelectDTO.setWeight(75);
    providerSelectService.save(pSelectDTO);
    Assert.assertTrue(true);
  }

  @Test
  public void getSelectByIdTest() {
    List<PSelectDTO> list = providerSelectService.querySelectAll();
    Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    Assert.assertNotNull(providerSelectService.getSelectById(list.get(0).getId()));
  }

  @Test
  public void querySelectAllTest() {
    List<PSelectDTO> list = providerSelectService.querySelectAll();
    Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    Assert.assertTrue(true);
  }
}
