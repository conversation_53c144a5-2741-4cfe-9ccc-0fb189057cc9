package com.timevale.infoauth.service.request.query;

import com.google.common.collect.Sets;
import com.timevale.infoauth.service.enums.InfoAuthServiceType;
import com.timevale.infoauth.service.exception.InfoAuthBizRuntimeException;
import com.timevale.infoauth.service.model.CommonResultEnum;
import com.timevale.infoauth.service.model.IResultEnum;
import com.timevale.infoauth.service.model.SupportResult;
import com.timevale.infoauth.service.request.base.ReqHandler;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/08/05
 */
@Data
public class OrgAuthInfoRecordQuery extends ToString implements ReqHandler {

  private static final Set<Integer> SUPPORT_TYPES = Sets.newHashSet(
          InfoAuthServiceType.ENTERPRISE_2_ELEMENT.dbValue(),
          InfoAuthServiceType.ENTERPRISE_3_ELEMENT.dbValue(),
          InfoAuthServiceType.ENTERPRISE_4_ELEMENT.dbValue());
  /**
   * 信息比对类型。
   *  1=企业三要素
   *  2=企业二要素
   *  3=企业四要素
   *
   * @see InfoAuthServiceType infoAuthServiceType
   * */
  Integer infoAuthServiceType;

  /**
   * appId（非必填）
   * */
  String appId;

  /**
   * 名称（必填）
   * */
  String name;

  /**
   * 证件号（必填）
   * */
  String certNo;

  /**
   * 法人名称（必填）
   * */
  String legalName;

  /**
   * 法人证件号（必填）
   * */
  String legalCertNo;

  @Override
  public SupportResult validate() {
    if(infoAuthServiceType == null || !SUPPORT_TYPES.contains(infoAuthServiceType)){
      return SupportResult.fail(CommonResultEnum.PARAM_WRONG, "信息比对类型不支持查询");
    }

    if (StringUtils.isBlank(name) || StringUtils.isBlank(certNo)) {
      return SupportResult.fail(CommonResultEnum.PARAM_WRONG, "企业名称或企业证件号不能为空");
    }

    if (Objects.equals(infoAuthServiceType,InfoAuthServiceType.ENTERPRISE_3_ELEMENT.dbValue())) {
      if(StringUtils.isBlank(legalName)){
        return SupportResult.fail(CommonResultEnum.PARAM_WRONG, "法人名称不能为空");
      }

    }
    if (Objects.equals(infoAuthServiceType,InfoAuthServiceType.ENTERPRISE_4_ELEMENT.dbValue())) {
      if(StringUtils.isBlank(legalName)){
        return SupportResult.fail(CommonResultEnum.PARAM_WRONG, "法人名称不能为空");
      }
      if(StringUtils.isBlank(legalCertNo)){
        return SupportResult.fail(CommonResultEnum.PARAM_WRONG, "法人证件号不能为空");
      }
    }

    return SupportResult.success();
  }

  @Override
  public ReqHandler format() {

    return this;
  }
}
