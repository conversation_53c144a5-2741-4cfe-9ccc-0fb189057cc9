package com.timevale.infoauth.service.api;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.dto.PRetryDTO;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
@JsonDataIgnore
public class ProviderRetryServiceTest extends ApplicationTest {

  @Resource private ProviderRetryService providerRetryService;

  @Test
  public void saveTest() {
    List<PRetryDTO> list = providerRetryService.queryRetryAll();
    Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    PRetryDTO pRetryDTO = new PRetryDTO();
    pRetryDTO.setId(list.get(0).getId());
    pRetryDTO.setExtendJson(
        "{\"manager\":{},\"client\":{\"desc\":\"客户端子域\",\"val\":[{\"desc\":\"库照比对-海鑫-比对通过-分数下限\",\"key\":\"compare.without.source.haixin.pass.score\",\"val\":\"60\"}]},\"select\":{},\"retry\":{}}");
    providerRetryService.save(pRetryDTO);
    Assert.assertTrue(true);
  }

  @Test
  public void getRetryByIdTest() {
    List<PRetryDTO> list = providerRetryService.queryRetryAll();
    Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    Assert.assertNotNull(providerRetryService.getRetryById(list.get(0).getId()));
  }

  @Test
  public void queryRetryAllTest() {
    List<PRetryDTO> list = providerRetryService.queryRetryAll();
    Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    Assert.assertTrue(true);
  }
}
