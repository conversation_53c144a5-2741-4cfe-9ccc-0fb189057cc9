package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceOrgLicenseOcrDO;
import org.apache.ibatis.annotations.*;

public interface InfoServiceOrgLicenseOcrDAO {

  @Insert(
      "INSERT INTO info_service_org_license_ocr "
          + "(`id`, `infoauth_id`, `img_key`, `create_time`, `modify_time`) "
          + "VALUES "
          + "(#{id}, #{infoauthId}, #{imgKey}, now(), now())")
  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  int insert(InfoServiceOrgLicenseOcrDO entity);

  @Update("update info_service_org_license_ocr set service_status=#{status} where id = #{id}")
  void updateStatus(@Param("id") Integer id, @Param("status") Integer status);

  @Update(
      "update info_service_org_license_ocr set "
          + "ocr_name=#{ocrName}, "
          + "ocr_cert_no=#{ocrCertNo}, "
          + "ocr_type=#{ocrType}, "
          + "ocr_address=#{ocrAddress}, "
          + "ocr_legal_rep_name=#{ocrLegalRepName}, "
          + "ocr_capital=#{ocrCapital}, "
          + "ocr_establish_date=#{ocrEstablishDate}, "
          + "ocr_valid_term=#{ocrValidTerm}, "
          + "ocr_scope=#{ocrScope}, "
          + "`result`=#{result}, "
          + "service_status=#{serviceStatus}, "
          + "provider_id=#{providerId} "
          + "where id = #{id}")
  void updateOrgLicenseOcrResult(InfoServiceOrgLicenseOcrDO entity);

  @Select("select * from info_service_org_license_ocr where infoauth_id = #{infoauthId}")
  InfoServiceOrgLicenseOcrDO queryByInfoAuthId(@Param("infoauthId") String infoauthId);
}
