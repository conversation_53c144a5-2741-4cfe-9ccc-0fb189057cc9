package com.timevale.infoauth.dal.infoauth.facade;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrgAuthQueryDTO {

  /**
   * appId
   * */
  String appId;

  /**
   * 名称
   * */
  String name;
  /**
   * 证件号
   * */
   String codeORG;

   String codeREG;

  String codeUSC;


  /**
   * 法人名称
   * */
  String legalName;

  /**
   * 法人证件号
   * */
  String legalCertNo;


  /**
   * 核验状态: ServiceStatus
   * */
  Integer serviceStatus;

  /**
   * 分页信息
   * */
  int pageIndex, pageSize;
}
