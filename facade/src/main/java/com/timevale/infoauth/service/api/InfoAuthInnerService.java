package com.timevale.infoauth.service.api;

import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.infoauth.service.request.InfoAuthCheckRequest;
import com.timevale.infoauth.service.request.InfoAuthCheckResponse;
import com.timevale.mandarin.common.annotation.RestClient;

@RestClient(serviceId = "infoauth-service")
public interface InfoAuthInnerService {


    /**
     * 指定供应商信息比对 - 运营支撑使用 不要对外
     * @param request
     * @return
     */
    RpcOutput<InfoAuthCheckResponse> infoCheckDesignateProvider(InfoAuthCheckRequest request);


}
