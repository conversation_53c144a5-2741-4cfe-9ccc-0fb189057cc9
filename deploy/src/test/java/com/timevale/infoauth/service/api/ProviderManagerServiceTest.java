package com.timevale.infoauth.service.api;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.dto.PDomainDTO;
import com.timevale.infoauth.service.dto.PManagerDTO;
import com.timevale.infoauth.service.enums.InfoAuthServiceType;
import com.timevale.infoauth.service.enums.ProviderQualityTagEnum;
import com.timevale.infoauth.service.impl.constants.ProviderEnum;
import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
@JsonDataIgnore
public class ProviderManagerServiceTest extends ApplicationTest {

  @Resource private ProviderManagerService providerManagerService;

//  @Test
//  public void createDomainTest() {
//    PDomainDTO pDomainDTO = new PDomainDTO();
//    pDomainDTO.setName(ProviderEnum.ZXJK.dbKey());
//    pDomainDTO.setType(InfoAuthServiceType.ENTERPRISE_2_ELEMENT.dbValue());
//    pDomainDTO.setDesc("中新金科");
//    pDomainDTO.setUrl("cn-north-4.myZXJKcloud.com");
//    pDomainDTO.setAccount("yinjiaqi");
//    pDomainDTO.setConnectTimeout(2000);
//    pDomainDTO.setReadTimeout(5000);
//    pDomainDTO.setTokenTtl(84000);
//    pDomainDTO.setMockUrl("mock://url");
//    pDomainDTO.setWeight(50);
//    providerManagerService.createDomain(pDomainDTO);
//    Assert.assertTrue(true);
//    Assert.assertEquals(pDomainDTO.getName(), ProviderEnum.ZXJK.dbKey());
//  }

  @Test
  public void removeDomainTest() {
    List<PManagerDTO> list = providerManagerService.queryManagerAll();
    Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    for (PManagerDTO pManagerDTO : list) {
      if (InfoAuthServiceType.BANKCARD_4_ELEMENT.dbValue() == pManagerDTO.getType()) {
        Assert.assertThrows(() -> providerManagerService.removeDomain(pManagerDTO.getId()));
        break;
      }
    }
    Assert.assertTrue(true);
  }

  @Test
  public void saveTest() {
    List<PManagerDTO> list = providerManagerService.queryManagerAll();
    Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    PManagerDTO pManagerDTO = new PManagerDTO();
    pManagerDTO.setId(list.get(0).getId());
    pManagerDTO.setType(InfoAuthServiceType.ENTERPRISE_3_ELEMENT.dbValue());
    pManagerDTO.setName(ProviderEnum.BAIDU.dbKey());
    providerManagerService.save(pManagerDTO);
    Assert.assertTrue(true);
  }

  @Test
  public void onOfflineTest() {
    List<PManagerDTO> list = providerManagerService.queryManagerAll();
    Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    for (PManagerDTO pManagerDTO : list) {
      if (InfoAuthServiceType.BANKCARD_4_ELEMENT.dbValue() == pManagerDTO.getType()) {
        Assert.assertThrows(() -> providerManagerService.onOffline(pManagerDTO.getId()));
        break;
      }
    }
    Assert.assertTrue(true);
  }

  @Test
  public void onOffMockTest() {
    List<PManagerDTO> list = providerManagerService.queryManagerAll();
    Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    providerManagerService.onOffMock(list.get(0).getId());
    Assert.assertTrue(true);
  }

  @Test
  public void getManagerByIdTest() {
    List<PManagerDTO> list = providerManagerService.queryManagerAll();
    Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    Assert.assertNotNull(providerManagerService.getManagerById(list.get(0).getId()));
  }

  @Test
  public void queryManagerAllTest() {
    List<PManagerDTO> list = providerManagerService.queryManagerAll();
    Assert.assertTrue(true);
    Assert.assertTrue(CollectionUtils.isNotEmpty(list));
  }

  @Test
  public void refreshTokenTest() {
    List<PManagerDTO> list = providerManagerService.queryManagerAll();
    Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    for (PManagerDTO pManagerDTO : list) {
      if (InfoAuthServiceType.IDCARD_OCR.dbValue() == pManagerDTO.getType()) {
        providerManagerService.refreshToken(pManagerDTO.getId());
      }
    }
    Assert.assertTrue(true);
  }


  @Test(expectedExceptions = BaseBizRuntimeException.class)
  public void createDomainCheckTest() {
    PDomainDTO pDomainDTO = new PDomainDTO();
    pDomainDTO.setId(756);
    pDomainDTO.setName("test001");
    pDomainDTO.setType(98);
    pDomainDTO.setQualityTag(ProviderQualityTagEnum.HIGH.name());
    providerManagerService.createDomain(pDomainDTO);

  }
}
