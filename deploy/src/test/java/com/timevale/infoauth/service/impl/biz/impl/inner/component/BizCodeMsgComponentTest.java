package com.timevale.infoauth.service.impl.biz.impl.inner.component;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.dal.infoauth.dao.ProviderErrCodeEnumShadowDAO;
import com.timevale.infoauth.service.enums.InfoAuthServiceType;
import com.timevale.infoauth.service.impl.biz.common.event.dto.ProviderErrCodeEventDTO;
import com.timevale.infoauth.service.impl.config.AutoConfig;
import com.timevale.infoauth.service.impl.utils.UUIDUtil;
import com.timevale.mandarin.base.security.MD5Utils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @since 2022/8/15 10:39
 */
@JsonDataIgnore
public class BizCodeMsgComponentTest extends ApplicationTest {

    @Autowired
    BizCodeMsgComponent msgComponent;
    @Autowired
    ProviderErrCodeEnumShadowDAO errCodeEnumShadowDAO;

    @Test
    public void providerErrCodeProcess() {
        boolean oldHandleEnabled = AutoConfig.monitorAutoHandleEnabled;
        ProviderErrCodeEventDTO eventDTO = ProviderErrCodeEventDTO.builder()
                .code("MOCK_02")
                .trackId(UUIDUtil.generateUUID())
                .msg("MOCK_02")
                .pMsg("欠费")
                .pCode("MOCK_07")
                .provider("MOCK")
                 .serviceType(InfoAuthServiceType.IDCARD_2_ELEMENT)
                .infoauthId(UUIDUtil.generateUUID()).build();

        try {
            AutoConfig.monitorAutoHandleEnabled = true;
            msgComponent.providerErrCodeProcess(eventDTO);
        }catch (Exception e){
            Assert.assertTrue(false);
            return;
        }finally {
            AutoConfig.monitorAutoHandleEnabled = oldHandleEnabled;
            String md5 =
                    md5(
                            eventDTO.getServiceType().dbValue(),
                            eventDTO.getProvider(),
                            eventDTO.getPCode(),
                            eventDTO.getPMsg());
            int original = errCodeEnumShadowDAO.deleteByMd5(md5);
            Assert.assertEquals(original, 1);
        }


        Assert.assertTrue(true);
    }

    private String md5(Integer serviceType, String provider, String pCode, String pMsg) {
        return MD5Utils.md5(serviceType + provider + pCode + pMsg);
    }
}
