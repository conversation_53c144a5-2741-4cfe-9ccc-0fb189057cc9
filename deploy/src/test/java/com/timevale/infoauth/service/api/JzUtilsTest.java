package com.timevale.infoauth.service.api;

import com.timevale.infoauth.service.enums.ProvResult;
import com.timevale.infoauth.service.impl.provider.constants.JzBasicConstants;
import com.timevale.infoauth.service.impl.utils.JzUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @since 2020/4/16 下午5:36
 */
@JsonDataIgnore
public class JzUtilsTest {

  @Test
  public void recognizeCode() {
    Assert.assertEquals(JzUtils.recognizeCode(JzBasicConstants.CODE_RIGHT), ProvResult.SUCCESS);
    Assert.assertEquals(JzUtils.recognizeCode(JzBasicConstants.CODE_WRONG), ProvResult.FAILURE);
    Assert.assertEquals(
        JzUtils.recognizeCode(JzBasicConstants.CODE_MATCH_FAILURE), ProvResult.FAILURE);
    Assert.assertEquals(
        JzUtils.recognizeCode(JzBasicConstants.CODE_PARAM_WRONG), ProvResult.FAILURE);
    Assert.assertEquals(JzUtils.recognizeCode(JzBasicConstants.CODE_CANTAUTH), ProvResult.UNKNOWN);
    Assert.assertEquals(JzUtils.recognizeCode("11"), ProvResult.UNKNOWN);
  }
}
