package com.timevale.infoauth.service;

import com.alibaba.fastjson.JSON;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.dal.infoauth.enums.ServiceStatus;
import com.timevale.infoauth.service.api.InfoQueryService;
import com.timevale.infoauth.service.enums.InfoAuthServiceType;
import com.timevale.infoauth.service.impl.biz.impl.InfoQueryServiceImpl;
import com.timevale.infoauth.service.model.SupportResult;
import com.timevale.infoauth.service.request.query.AccurateQueryEnterpriseInformationRequest;
import com.timevale.infoauth.service.request.query.AuthInfoRecordQuery;
import com.timevale.infoauth.service.request.query.AuthResultRequest;
import com.timevale.infoauth.service.request.query.FuzzyQueryEnterpriseInformationRequest;
import com.timevale.infoauth.service.request.query.OrgAuthInfoRecordQuery;
import com.timevale.infoauth.service.request.query.PsnAuthCacheQueryRequest;
import com.timevale.infoauth.service.response.query.OrgAuthInfoQueryRecordResponse;
import com.timevale.infoauth.service.response.query.OrgInfoQueryResponse;
import com.timevale.infoauth.service.response.query.OrgInfoResponse;
import com.timevale.infoauth.service.response.query.PsnInfoQueryResponse;
import com.timevale.infoauth.service.response.query.PsnInfoResponse;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @author: jiuchen
 * @since: 2020-05-25 11:12
 */
@JsonDataIgnore
@Slf4j
public class QueryServiceTest extends ApplicationTest {

  @Resource InfoQueryService infoQueryService;
  @Resource InfoQueryServiceImpl infoQueryServiceImpl;

  @Test
  private void queryPsnInfoTestBank4() {
    try {
      AuthResultRequest request = new AuthResultRequest();
      request.setInfoAuthId("027c910f-2e7f-43e7-b7b8-12e2083bc121");
      RpcOutput<PsnInfoResponse> result = infoQueryService.queryPsnInfo(request);
      Assert.assertNotNull(result);
      Assert.assertNotNull(result.getData());
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  private void queryPsnInfoTestBank3() {
    try {
      AuthResultRequest request = new AuthResultRequest();
      request.setInfoAuthId("ddcac04e-522e-4768-9d90-ed6558b2da66");
      Long startTime = System.currentTimeMillis();
      RpcOutput<PsnInfoResponse> result = infoQueryService.queryPsnInfo(request);
      Assert.assertNotNull(result);
      Assert.assertTrue(result.isSuccess());
      System.out.println("queryPsnInfoTestBank3 result  " + JSON.toJSONString(result));
      Long endTime = System.currentTimeMillis();
      System.out.println("cost=======" + (endTime - startTime));
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  private void queryPsnInfoTestTele3() {
    try {
      AuthResultRequest request = new AuthResultRequest();
      request.setInfoAuthId("b1d0ff0e-a540-4b40-8a95-9bb0a82e94e1");
      Long startTime = System.currentTimeMillis();
      RpcOutput<PsnInfoResponse> result = infoQueryService.queryPsnInfo(request);
      Assert.assertNotNull(result.getData());
      Assert.assertTrue(result.isSuccess());
      System.out.println("queryPsnInfoTestBank3 result  " + JSON.toJSONString(result));
      Long endTime = System.currentTimeMillis();
      System.out.println("cost=======" + (endTime - startTime));
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  private void queryPsnInfoTestIdNo2() {
    try {
      AuthResultRequest request = new AuthResultRequest();
      request.setInfoAuthId("b03aaf58-6fff-4af4-962c-6841f0fcd3ad");
      Long startTime = System.currentTimeMillis();
      RpcOutput<PsnInfoResponse> result = infoQueryService.queryPsnInfo(request);
      Assert.assertNotNull(result);
      Assert.assertTrue(result.isSuccess());
      System.out.println("queryPsnInfoTestBank3 result  " + JSON.toJSONString(result));
      Long endTime = System.currentTimeMillis();
      System.out.println("cost=======" + (endTime - startTime));
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  private void orgInfoQueryForRPCTest() {
    AuthInfoRecordQuery request = new AuthInfoRecordQuery();
    Long startTime = System.currentTimeMillis();

    /**
     * {"appId":"**********","startTime":*************,"endTime":*************,"pageSize":25,"pageIndex":0,"queryType":"org","serviceStatus":2}
     */
    request.setAppId("**********");
    request.setStartTime(new Date(1077517459000L));
    request.setEndTime(new Date(1711175059000L));
    request.setPageIndex(0);
    request.setPageSize(10);
    request.setQueryType("org");
    request.setServiceStatus(2);
    RpcOutput<OrgInfoQueryResponse> result = infoQueryService.orgInfoQueryForRPC(request);
    System.out.println("OrgInfoQueryResponse result  " + JSON.toJSONString(result));
    Long endTime = System.currentTimeMillis();
    System.out.println("cost=======" + (endTime - startTime));
    Assert.assertNotNull(result);
    Assert.assertTrue(result.isSuccess());
  }

  @Test
  private void orgAuthLastSuccessInfoQuery() {
    OrgAuthInfoRecordQuery request = new OrgAuthInfoRecordQuery();

    request.setAppId("1111563841");
    request.setInfoAuthServiceType(3);
    request.setName("esigntest春风十里");
    request.setCertNo("913434565663645343");
    request.setLegalName("黄振衡");
    request.setLegalCertNo("372922199405121737");
    SupportResult<OrgAuthInfoQueryRecordResponse> result = infoQueryService.orgAuthLastSuccessInfoQuery(request);

    Assert.assertNotNull(result);
    Assert.assertTrue(result.ifSuccess());
    Assert.assertEquals(result.getData().getName(), request.getName());
  }

  @Test
  private void orgInfoQueryForRPCTestTimeNull() {
    try {
      AuthInfoRecordQuery request = new AuthInfoRecordQuery();
      request.setAppId("**********");
      request.setCertName("杭州天谷信息科技有限公司");
      request.setCertNo("913301087458306077");
      request.setServiceStatus(ServiceStatus.FINISHED.dbValue());
      request.setPageIndex(0);
      request.setPageSize(5);
      Long startTime = System.currentTimeMillis();
      RpcOutput<OrgInfoQueryResponse> result = infoQueryService.orgInfoQueryForRPC(request);
      Assert.assertNotNull(result);
      Assert.assertNotNull(result.getData());
      System.out.println("OrgInfoQueryResponse result  " + JSON.toJSONString(result));
      Long endTime = System.currentTimeMillis();
      System.out.println("cost=======" + (endTime - startTime));
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  private void orgInfoQueryForRPCTestAppIdNull() {
    try {
      AuthInfoRecordQuery request = new AuthInfoRecordQuery();
      request.setCertName("杭州天谷信息科技有限公司");
      request.setCertNo("913301087458306077");
      request.setServiceStatus(ServiceStatus.FINISHED.dbValue());
      request.setStartTime(new Date(1591328102000L));
      request.setEndTime(new Date());
      request.setPageIndex(0);
      request.setPageSize(5);
      Long startTime = System.currentTimeMillis();
      RpcOutput<OrgInfoQueryResponse> result = infoQueryService.orgInfoQueryForRPC(request);
      Assert.assertNotNull(result);
      Assert.assertNotNull(result.getData());
      System.out.println("OrgInfoQueryResponse result  " + JSON.toJSONString(result));
      Long endTime = System.currentTimeMillis();
      System.out.println("cost=======" + (endTime - startTime));
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  private void psnQueryInfoForRPCTestBank4() {
    AuthInfoRecordQuery request = new AuthInfoRecordQuery();
    request.setAppId("**********");
    request.setCertName("陈威宇");
    request.setCertNo("362302198609175011");
    request.setInfoAuthServiceType(InfoAuthServiceType.BANKCARD_4_ELEMENT.dbValue());
    request.setServiceStatus(ServiceStatus.FINISHED.dbValue());
    request.setStartTime(new Date(1591266767000L));
    request.setEndTime(new Date());
    request.setPageIndex(0);
    request.setPageSize(5);
    Long startTime = System.currentTimeMillis();
    RpcOutput<PsnInfoQueryResponse> result = infoQueryService.psnQueryInfoForRPC(request);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getData());
    System.out.println("psnQueryInfoForRPCTestBank4 result  " + JSON.toJSONString(result));
    Long endTime = System.currentTimeMillis();
    System.out.println("cost=======" + (endTime - startTime));
  }

  @Test
  private void psnQueryInfoForRPCTestBank4TypeNull() {
    try {
      AuthInfoRecordQuery request = new AuthInfoRecordQuery();
      request.setAppId("**********");
      request.setCertName("陈威宇");
      request.setCertNo("362302198609175011");
      request.setServiceStatus(ServiceStatus.FINISHED.dbValue());
      request.setStartTime(new Date(1591266767000L));
      request.setEndTime(new Date());
      request.setPageIndex(0);
      request.setPageSize(5);
      Long startTime = System.currentTimeMillis();
      RpcOutput<PsnInfoQueryResponse> result = infoQueryService.psnQueryInfoForRPC(request);
      Assert.assertNotNull(result);
      Assert.assertNotNull(result.getData());
      System.out.println("psnQueryInfoForRPCTestBank4 result  " + JSON.toJSONString(result));
      Long endTime = System.currentTimeMillis();
      System.out.println("cost=======" + (endTime - startTime));
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  private void psnQueryInfoForRPCTestBank3() {
    AuthInfoRecordQuery request = new AuthInfoRecordQuery();
    request.setAppId("**********");
    request.setCertName("姜娇");
    request.setCertNo("******************");
    request.setInfoAuthServiceType(InfoAuthServiceType.BANKCARD_3_ELEMENT.dbValue());
    request.setStartTime(new Date(1591007567000L));
    request.setEndTime(new Date());
    request.setPageIndex(0);
    request.setPageSize(5);
    Long startTime = System.currentTimeMillis();
    RpcOutput<PsnInfoQueryResponse> result = infoQueryService.psnQueryInfoForRPC(request);
    System.out.println("psnQueryInfoForRPCTestBank3 result  " + JSON.toJSONString(result));
    Long endTime = System.currentTimeMillis();
    System.out.println("cost=======" + (endTime - startTime));
    Assert.assertNotNull(result);
    Assert.assertTrue(result.isSuccess());
  }

  @Test
  private void psnQueryInfoForRPCTestTele3() {
    AuthInfoRecordQuery request = new AuthInfoRecordQuery();
    request.setAppId("**********");
    request.setCertName("郭民意");
    request.setCertNo("******************");
    request.setInfoAuthServiceType(InfoAuthServiceType.MOBILE_3_ELEMENT.dbValue());
    request.setStartTime(new Date(1591007567000L));
    request.setEndTime(new Date());
    request.setPageIndex(0);
    request.setPageSize(5);
    Long startTime = System.currentTimeMillis();
    RpcOutput<PsnInfoQueryResponse> result = infoQueryService.psnQueryInfoForRPC(request);
    System.out.println("psnQueryInfoForRPCTestTele3 result  " + JSON.toJSONString(result));
    Long endTime = System.currentTimeMillis();
    System.out.println("cost=======" + (endTime - startTime));
    Assert.assertNotNull(result);
    Assert.assertTrue(result.isSuccess());
  }

  @Test
  private void psnQueryInfoForRPCTestIdNo2() {
    AuthInfoRecordQuery request = new AuthInfoRecordQuery();
    request.setAppId("**********");
    request.setCertName("郭民意");
    request.setCertNo("******************");
    request.setInfoAuthServiceType(InfoAuthServiceType.IDCARD_2_ELEMENT.dbValue());
    request.setServiceStatus(ServiceStatus.FINISHED.dbValue());
    request.setStartTime(new Date(1591007567000L));
    request.setEndTime(new Date());
    request.setPageIndex(0);
    request.setPageSize(5);
    Long startTime = System.currentTimeMillis();
    RpcOutput<PsnInfoQueryResponse> result = infoQueryService.psnQueryInfoForRPC(request);
    System.out.println("psnQueryInfoForRPCTestIdNo2 result  " + JSON.toJSONString(result));
    Long endTime = System.currentTimeMillis();
    System.out.println("cost=======" + (endTime - startTime));
    Assert.assertNotNull(result);
    Assert.assertTrue(result.isSuccess());
  }

  @Test
  private void queryOrgInfoTest() {
    try {
      AuthResultRequest request = new AuthResultRequest();
      request.setInfoAuthId("b109d8a3-c3a8-4d3b-a8a4-5fce1dddec95");
      Long startTime = System.currentTimeMillis();
      RpcOutput<OrgInfoResponse> result = infoQueryService.queryOrgInfo(request);
      Assert.assertNotNull(result);
      Assert.assertNotNull(result.getData());
      System.out.println("queryOrgInfoTest result  " + JSON.toJSONString(result));
      Long endTime = System.currentTimeMillis();
      System.out.println("cost=======" + (endTime - startTime));
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  public void fuzzyQueryEnterpriseInformation() {
    Assert.assertThrows(() -> infoQueryService.fuzzyQueryEnterpriseInformation(null));
    FuzzyQueryEnterpriseInformationRequest request = new FuzzyQueryEnterpriseInformationRequest();
    request.setPageIndex(1);
    Assert.assertThrows(() -> infoQueryService.fuzzyQueryEnterpriseInformation(request));
    request.setAppId("**********");
    Assert.assertThrows(() -> infoQueryService.fuzzyQueryEnterpriseInformation(request));
    request.setKeyword("杭州天谷信息科技有限公司");
    Assert.assertNotNull(infoQueryService.fuzzyQueryEnterpriseInformation(request).getData());
  }
//
//  @Test
//  public void qiChaChaFuzzyQueryEnterpriseInformation() {
//    Assert.assertThrows(() -> infoQueryServiceImpl.qiChaChaFuzzyQueryEnterpriseInformation(null));
//    FuzzyQueryEnterpriseInformationRequest request = new FuzzyQueryEnterpriseInformationRequest();
//    request.setPageIndex(1);
//    Assert.assertThrows(() -> infoQueryServiceImpl.qiChaChaFuzzyQueryEnterpriseInformation(request));
//    request.setAppId("**********");
//    Assert.assertThrows(() -> infoQueryServiceImpl.qiChaChaFuzzyQueryEnterpriseInformation(request));
//    request.setKeyword("杭州天谷信息科技有限公司");
//    Assert.assertNotNull(infoQueryServiceImpl.qiChaChaFuzzyQueryEnterpriseInformation(request).getData());
//    request.setKeyword("1231011206091228XR");
//    Assert.assertNotNull(infoQueryServiceImpl.qiChaChaFuzzyQueryEnterpriseInformation(request).getData());
//  }

  @Test
  public void accurateQueryEnterpriseInformation() {
    Assert.assertThrows(() -> infoQueryService.accurateQueryEnterpriseInformation(null));
    AccurateQueryEnterpriseInformationRequest request = new AccurateQueryEnterpriseInformationRequest();
    request.setAppId("**********");
    Assert.assertThrows(() -> infoQueryService.accurateQueryEnterpriseInformation(request));
    request.setKeyword("杭州天谷信息科技有限公司");
    Assert.assertNotNull(infoQueryService.accurateQueryEnterpriseInformation(request).getData());
    request.setKeyword("913301087458306077");
    Assert.assertNotNull(infoQueryService.accurateQueryEnterpriseInformation(request).getData());
  }

  @Test
  public void psnInfoCacheQueryForRPC() {
    PsnAuthCacheQueryRequest request = new PsnAuthCacheQueryRequest();
    request.setName("郭民意");
    request.setCertNumber("******************");
    request.setMobile("17788889999");
    request.setCardno("******************");
    request.setUserCenterCertType(1);
    Assert.assertNotNull(infoQueryService.psnInfoCacheQueryForRPC(request).getData());
  }
}
