package com.timevale.infoauth.dal.infoauth.dataobject;

import lombok.Data;

import java.util.Date;

@Data
public class InfoProviderDrivinglicenceOcrDO {
    /** createTime 记录创建时间. */
    private Date createTime;
    /** modifyTime 记录更新时间. */
    private Date modifyTime;
    /** id 主键. */
    private Long id;
    /** url URL. */
    private String url;
    /** disc 服务商描述. */
    private String disc;
    /** name 服务商名称. */
    private String name;
    /** secret SECRET. */
    private String secret;
    /** account ACCOUNT. */
    private String account;
    /** signKey 签名密钥. */
    private String signKey;
    /** verifyKey VERIFY_KEY. */
    private String verifyKey;
    /** level 优先级. */
    private Integer level;
    /** signType 签名方式. */
    private Integer signType;
}
