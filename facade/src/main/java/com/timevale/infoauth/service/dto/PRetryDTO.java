package com.timevale.infoauth.service.dto;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PRetryDTO extends ToString {

  private Integer id;

  private Map<String, String> notFound;

  private Map<String, String> errCode;

  private String extendJson;
}
