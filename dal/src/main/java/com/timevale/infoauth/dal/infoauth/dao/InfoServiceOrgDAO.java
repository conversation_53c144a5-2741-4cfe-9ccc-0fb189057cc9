package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceOrgDO;
import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceOrgExtendDO;
import com.timevale.infoauth.dal.infoauth.facade.AuthInfoQueryRequestDTO;
import com.timevale.infoauth.dal.infoauth.facade.AuthResultQueryRecordDTO;
import com.timevale.infoauth.dal.infoauth.facade.OrgAuthQueryDTO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * The Table info_service_org. 企业实名认证入参表
 *
 * <AUTHOR> Kunpeng
 */
public interface InfoServiceOrgDAO {

    /**
     * desc:插入表:info_service_org.<br>
     *
     * @param entity entity
     * @return int
     */
    @Insert(
            "INSERT INTO `info_service_org` "
                    + "(`id`,`app_id`, `infoauth_id`, `name`, `codeORG`, `codeUSC`, `codeREG`, `legal_name`, `legal_cert_type`, `legal_idno` ,`legal_area`, `result`, `service_status`, `timestamp_id`, `billorder_id`, `create_time`, `modify_time`) "
                    + "VALUES "
                    + "(#{id},#{appId},  #{infoauthId}, #{name}, #{codeorg}, #{codeusc}, #{codereg}, #{legalName}, #{legalCertType}, #{legalIdno} ,#{legalArea},#{result}, #{serviceStatus}, #{billorderId}, #{timestampId}, now(), now())")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insert(InfoServiceOrgDO entity);

    /**
     * desc:批量插入表:info_service_org.<br>
     *
     * @param list list
     * @return int
     */
    int insertBatch(List<InfoServiceOrgDO> list);

    /**
     * desc:根据主键删除数据:info_service_org.<br>
     *
     * @param id id
     * @return int
     */
    int deleteById(Integer id);

    /**
     * desc:根据主键获取数据:info_service_org.<br>
     *
     * @param id id
     * @return InfoServiceOrgDO
     */
    InfoServiceOrgDO getById(Integer id);

    /**
     * desc:根据普通索引InfoauthId获取数据:info_service_org.<br>
     *
     * @param infoauthId infoauthId
     * @return List<InfoServiceOrgDO>
     */
    List<InfoServiceOrgDO> queryByInfoauthId(String infoauthId);

    /**
     * desc:根据普通索引Name获取数据:info_service_org.<br>
     *
     * @param name name
     * @return List<InfoServiceOrgDO>
     */
    List<InfoServiceOrgDO> queryByName(String name);

    @Update(
            "update info_service_org set `result` = #{result},service_status = #{serviceStatus},modify_time = now() where infoauth_id = #{infoauthId}")
    void updateResultAndServiceStatus(
            @Param("infoauthId") String infoauthId, @Param("result") int result, @Param("serviceStatus") int serviceStatus);

    @Select("select * from info_service_org where infoauth_id = #{infoAuthId}")
    InfoServiceOrgDO getByInfoAuthId(@Param("infoAuthId") String infoAuthId);

    /**
     * desc:专为快捷签出证查询使用
     *
     * @param authInfoQueryRequestDTO
     * @return List<InfoServiceIdnoauthDO>
     */
    @Select({
            "<script>",
            "select * from info_service_org t1 inner join info_service t2 on t1.infoauth_id = t2.infoauth_id",
            "where  1=1 ",
            "<if test='infoAuthId != null'> AND t1.infoauth_id = #{infoAuthId} </if>",
            "<if test='appId != null'> AND t2.app_id= #{appId} </if>",
            "<if test='serviceStatus != null'> AND t1.service_status = #{serviceStatus} </if>",
            "<if test='updateTime != null'> AND t1.modify_time between 0 AND #{updateTime} </if>",
            "<if test='codeORG != null||codeUSC != null||codeREG != null'> AND </if>",
            "<trim prefix='(' prefixOverrides='OR'>",
            "<if test='codeORG != null'> OR t1.codeORG = #{codeORG} </if>",
            "<if test='codeUSC != null'> OR t1.codeUSC = #{codeUSC} </if>",
            "<if test='codeREG != null'> OR t1.codeREG = #{codeREG} </if>",
            "<if test='codeORG != null||codeUSC != null||codeREG != null'> ) </if>",
            "</trim>",
            "order by t1.modify_time desc limit #{currIndex},#{pageSize}",
            "</script>"})
    List<InfoServiceOrgExtendDO> queryByUpdateTimeAndIdNo(AuthInfoQueryRequestDTO authInfoQueryRequestDTO);

    /**
     * 快捷签查询企业的实名认证记录
     * @param authInfoQueryRequestDTO 请求参数
     * @return 实名认证记录
     */
    @Select({
            "<script>",
            "select * from info_service_org t1 inner join info_service t2 on t1.infoauth_id = t2.infoauth_id",
            "where  1=1 ",
            "<if test='infoAuthId != null'> AND t1.infoauth_id = #{infoAuthId} </if>",
            "<if test='appId != null'> AND t2.app_id= #{appId} </if>",
            "<if test='serviceStatus != null'> AND t1.service_status = #{serviceStatus} </if>",
            "<if test='modifyTime != null'> AND t1.modify_time between 0 AND #{modifyTime} </if>",
            "<if test='codeORG != null||codeUSC != null||codeREG != null'> AND </if>",
            "<trim prefix='(' prefixOverrides='OR'>",
            "<if test='codeORG != null'> OR t1.codeORG = #{codeORG} </if>",
            "<if test='codeUSC != null'> OR t1.codeUSC = #{codeUSC} </if>",
            "<if test='codeREG != null'> OR t1.codeREG = #{codeREG} </if>",
            "<if test='codeORG != null||codeUSC != null||codeREG != null'> ) </if>",
            "</trim>",
            "order by t1.modify_time desc limit #{currIndex},#{pageSize}",
            "</script>"})
    List<InfoServiceOrgExtendDO> quickSignQueryListByInfoServiceOrg(AuthInfoQueryRequestDTO authInfoQueryRequestDTO);

    /**
     * desc:专为信息比对记录查询使用
     *
     * @param authInfoQueryRequestDTO
     * @return List<InfoServiceIdnoauthDO>
     */
    @Select({
            "<script>",
            "select * from info_service_org t1 inner join info_service t2 on t1.infoauth_id = t2.infoauth_id",
            "where  1=1 ",
            "<if test='infoAuthId != null'> AND t1.infoauth_id = #{infoAuthId} </if>",
            "<if test='appId != null'> AND t2.app_id= #{appId} </if>",
            "<if test='serviceStatus != null'> AND t1.service_status = #{serviceStatus} </if>",
            "<if test='startTime != null'> and t1.modify_time between #{startTime} and #{endTime} </if>",
            "<if test='codeORG != null||codeUSC != null||codeREG != null'> AND </if>",
            "<trim prefix='(' prefixOverrides='OR'>",
            "<if test='codeORG != null'> OR t1.codeORG = #{codeORG} </if>",
            "<if test='codeUSC != null'> OR t1.codeUSC = #{codeUSC} </if>",
            "<if test='codeREG != null'> OR t1.codeREG = #{codeREG} </if>",
            "<if test='codeORG != null||codeUSC != null||codeREG != null'> ) </if>",
            "</trim>",
            "order by t1.modify_time desc limit #{currIndex},#{pageSize}",
            "</script>"})
    List<InfoServiceOrgExtendDO> queryForInfoAuthRecord(AuthInfoQueryRequestDTO authInfoQueryRequestDTO);

    @Update(
            "UPDATE `info_service_org` SET err_id = #{errId} WHERE `infoauth_id` = #{infoauthId}")
    void bindErrId(@Param("infoauthId") String infoauthId, @Param("errId") Long errId);

    /**
     * desc:根据条件查询企业信息比对记录
     *
     * @return InfoServiceDO
     */
    @Select({
            "<script>",
            "select * from info_service t1 inner join info_service_org t2 on t1.infoauth_id = t2.infoauth_id",
            "where  1=1 ",
            "<if test='certName != null'> and t2.name = #{certName} </if>",
            "<if test='certNo != null'> AND (t2.codeORG = #{certNo} OR t2.codeUSC = #{certNo} OR t2.codeREG = #{certNo}) </if>",
            "<if test='infoAuthServiceType != null'> and t1.type = #{infoAuthServiceType} </if>",
            "<if test='serviceStatus != null'> and t2.service_status = #{serviceStatus} </if>",
            "<if test='appId != null'> AND t1.app_id = #{appId} </if>",
            "<if test='startTime != null'> AND t1.modify_time between #{startTime} and #{endTime} </if>",
            "<if test='infoAuthId != null'> AND t1.infoauth_id = #{infoAuthId} </if>",
            "order by t1.modify_time desc limit #{pageIndex},#{pageSize}",
            "</script>"})
    List<InfoServiceOrgDO> getOrgByAuthResultQuery(AuthResultQueryRecordDTO authResultQueryRecordDTO);


    /**
     * desc:根据条件查询企业信息比对记录
     *
     * @return InfoServiceDO
     */
    @Select({
            "<script>",
            "select count(*) from info_service t1 inner join info_service_org t2 on t1.infoauth_id = t2.infoauth_id",
            "where  1=1 ",
            "<if test='certName != null'> and t2.name = #{certName} </if>",
            "<if test='certNo != null'> AND (t2.codeORG = #{certNo} OR t2.codeUSC = #{certNo} OR t2.codeREG = #{certNo}) </if>",
            "<if test='infoAuthServiceType != null'> and t1.type = #{infoAuthServiceType} </if>",
            "<if test='serviceStatus != null'> and t2.service_status = #{serviceStatus} </if>",
            "<if test='appId != null'> AND t1.app_id = #{appId} </if>",
            "<if test='startTime != null'> AND t1.modify_time between #{startTime} and #{endTime} </if>",
            "<if test='infoAuthId != null'> AND t1.infoauth_id = #{infoAuthId} </if>",
            "</script>"})
    int getOrgByAuthResultQueryCount(AuthResultQueryRecordDTO authResultQueryRecordDTO);


    /**
     * desc:根据条件查询企业信息比对记录
     *
     * @return InfoServiceDO
     */
    @Select({
            "<script>",
            "select * from  info_service_org t2  ",
            "where  1=1 ",
            "<if test='name != null'> and t2.name = #{name} </if>",
            "<if test='codeUSC != null'> and t2.codeUSC = #{codeUSC} </if>",
            "<if test='codeREG != null'> and t2.codeREG = #{codeREG} </if>",
            "<if test='codeORG != null'> and t2.codeORG = #{codeORG} </if>",
            "<if test='legalName != null'> and t2.legal_name = #{legalName} </if>",
            "<if test='legalCertNo != null'> and t2.legal_idno = #{legalCertNo} </if>",
            "<if test='serviceStatus != null'> and t2.service_status = #{serviceStatus} </if>",
            "<if test='appId != null'> AND t2.app_id = #{appId} </if>",
            "order by t2.id desc limit 1",
            "</script>"})
    InfoServiceOrgDO getOrgAuthQuery(OrgAuthQueryDTO queryDTO);

}