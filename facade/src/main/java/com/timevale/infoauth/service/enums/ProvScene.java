package com.timevale.infoauth.service.enums;

public enum ProvScene {

    SUCCESS("000","信息匹配",false),

    USC_CODE_ERROR("001","统一社会信用代码填错",false),

    LEGA_NAME_ERROR("002","法人信息填错",false),

    LEGA_ID_NO_ERROR("003","法人证件号填错",false),

    ORG_NAME_ERROR("004","企业名称填错/未更新",true),

    LEGA_INFO_ERROR("005","法人信息填错/未更新",true),

    ORG_NAME_LEGA_INFO_ERROR("006","统一社会信用代码填错/企业名称或者法人信息变更",true),

    USC_CODE_LEGA_INFO_UNKNOW_ERROR("007","企业信息填错且统代填错",false),

    ORG_NAME_LEGA_INFO_UNKNOW_ERROR("008","企业名称变更且法人信息填错",false),

    INFO_ALL_ERROR("009","新注册企业数据未更新/数据填写错误",true),

    INFO_NOT_EXISTS("100","新注册企业数据未更新/数据填写错误",true),

    UNKNOWN("101","供应商接口异常/供应商错误码未适配/默认",false);

    ProvScene(String code, String desc,boolean retry) {
        this.code = code;
        this.dec = desc;
        this.retry = retry;
    }

    private String code;

    private String dec;

    private boolean retry;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDec() {
        return dec;
    }

    public void setDec(String dec) {
        this.dec = dec;
    }

    public boolean isRetry() {
        return retry;
    }
}
