package com.timevale.infoauth.service.enums.agreement;

import java.util.Objects;

/**
 * 协议内容存储类型
 *  htmlContent=html文本 htmlUrl=http地址 pdfUrl=pdf地址 zeyuanUrl=泽元系统Url
 * <AUTHOR>
 * @since 2024/12/4 21:04
 */
public enum CommonAgreementContentTypeEnum {


    HTML_CONTENT("htmlContent", "html文本"),

    HTML_URL("htmlUrl", "http地址"),

    PDF_URL("pdfUrl", "pdf地址"),

    HTML_ZEYUAN_CONTENT("htmlZeyuanContent", "泽元系统html文本"),
    ZEYUAN_URL("zeyuanUrl", "泽元系统Url");


    private String code;
    private String message;

    CommonAgreementContentTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }


}
