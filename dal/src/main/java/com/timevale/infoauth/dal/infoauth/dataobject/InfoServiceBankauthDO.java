package com.timevale.infoauth.dal.infoauth.dataobject;

import java.util.Date;

/**
 * The table. info_service_bankauth
 *
 * <AUTHOR> Kunpeng
 */
public class InfoServiceBankauthDO extends InfoServiceAuthDO{

  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;
  /** id 自增主键. */
  private Long id;
  /** bank 银行卡号所属银行卡. */
  private String bank;
  /** idno 身份证号. */
  private String idno;
  /** 证件类型*/
  private Integer userCenterCertType;
  /** name 姓名. */
  private String name;
  /** cardno 银行卡号. */
  private String cardno;
  /** mobile 用户手机. */
  private String mobile;
  /** infoauthId 业务主键. */
  private String infoauthId;
  /** providerId 4要素服务提供商. */
  private String providerId;
  /** billorderId 计费订单ID. */
  private String billorderId;
  /** timestampId 时间戳记录ID. */
  private String timestampId;
  /** providerResult 服务商处理结果. */
  private String providerResult;
  /** result 适配结果. */
  private Integer result;
  /** serviceStatus 记录状态. */
  private Integer serviceStatus;
  private Long errId;

  /** Set createTime 记录创建时间. */
  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  /**
   * Get createTime 记录创建时间.
   *
   * @return the string
   */
  public Date getCreateTime() {
    return createTime;
  }

  /** Set modifyTime 记录修改时间. */
  public void setModifyTime(Date modifyTime) {
    this.modifyTime = modifyTime;
  }

  /**
   * Get modifyTime 记录修改时间.
   *
   * @return the string
   */
  public Date getModifyTime() {
    return modifyTime;
  }

  /** Set id 自增主键. */
  public void setId(Long id) {
    this.id = id;
  }

  /**
   * Get id 自增主键.
   *
   * @return the string
   */
  public Long getId() {
    return id;
  }

  /** Set bank 银行卡号所属银行卡. */
  public void setBank(String bank) {
    this.bank = bank;
  }

  /**
   * Get bank 银行卡号所属银行卡.
   *
   * @return the string
   */
  public String getBank() {
    return bank;
  }

  /** Set idno 身份证号. */
  public void setIdno(String idno) {
    this.idno = idno;
  }

  /**
   * Get idno 身份证号.
   *
   * @return the string
   */
  public String getIdno() {
    return idno;
  }

  /** Set name 姓名. */
  public void setName(String name) {
    this.name = name;
  }

  /**
   * Get name 姓名.
   *
   * @return the string
   */
  public String getName() {
    return name;
  }

  /** Set cardno 银行卡号. */
  public void setCardno(String cardno) {
    this.cardno = cardno;
  }

  /**
   * Get cardno 银行卡号.
   *
   * @return the string
   */
  public String getCardno() {
    return cardno;
  }

  /** Set mobile 用户手机. */
  public void setMobile(String mobile) {
    this.mobile = mobile;
  }

  /**
   * Get mobile 用户手机.
   *
   * @return the string
   */
  public String getMobile() {
    return mobile;
  }

  /** Set infoauthId 业务主键. */
  public void setInfoauthId(String infoauthId) {
    this.infoauthId = infoauthId;
  }

  /**
   * Get infoauthId 业务主键.
   *
   * @return the string
   */
  public String getInfoauthId() {
    return infoauthId;
  }

  /** Set providerId 4要素服务提供商. */
  public void setProviderId(String providerId) {
    this.providerId = providerId;
  }

  /**
   * Get providerId 4要素服务提供商.
   *
   * @return the string
   */
  public String getProviderId() {
    return providerId;
  }

  /** Set billorderId 计费订单ID. */
  public void setBillorderId(String billorderId) {
    this.billorderId = billorderId;
  }

  /**
   * Get billorderId 计费订单ID.
   *
   * @return the string
   */
  public String getBillorderId() {
    return billorderId;
  }

  /** Set timestampId 时间戳记录ID. */
  public void setTimestampId(String timestampId) {
    this.timestampId = timestampId;
  }

  /**
   * Get timestampId 时间戳记录ID.
   *
   * @return the string
   */
  public String getTimestampId() {
    return timestampId;
  }

  /** Set providerResult 服务商处理结果. */
  public void setProviderResult(String providerResult) {
    this.providerResult = providerResult;
  }

  /**
   * Get providerResult 服务商处理结果.
   *
   * @return the string
   */
  public String getProviderResult() {
    return providerResult;
  }

  /** Set result 适配结果. */
  public void setResult(Integer result) {
    this.result = result;
  }

  /**
   * Get result 适配结果.
   *
   * @return the string
   */
  public Integer getResult() {
    return result;
  }

  /** Set serviceStatus 记录状态. */
  public void setServiceStatus(Integer serviceStatus) {
    this.serviceStatus = serviceStatus;
  }

  /**
   * Get serviceStatus 记录状态.
   *
   * @return the string
   */
  public Integer getServiceStatus() {
    return serviceStatus;
  }

  public Long getErrId() {
    return errId;
  }

  public void setErrId(Long errId) {
    this.errId = errId;
  }

  public int getUserCenterCertType() {
    return userCenterCertType;
  }

  public void setUserCenterCertType(int userCenterCertType) {
    this.userCenterCertType = userCenterCertType;
  }
}
