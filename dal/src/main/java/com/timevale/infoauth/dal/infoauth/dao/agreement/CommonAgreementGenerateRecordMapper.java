package com.timevale.infoauth.dal.infoauth.dao.agreement;

import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementGenerateRecordDO;
import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementInfoDO;
import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementReleaseRecordDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 通用协议发布记录的Mapper接口
 * 负责对common_agreement_generate_record表进行数据库操作
 */
@Mapper
public interface CommonAgreementGenerateRecordMapper {

    /**
     * 插入一条协议生成记录
     *
     * @param recordDO 协议发布记录对象，包含要插入的记录的各种信息
     * @return 插入记录的ID，表示插入操作的结果
     */
    @Insert(
            "<script>INSERT INTO common_agreement_generate_record(generate_record_id,release_record_id,content_fill_param,extend"
                    + ") VALUES(\n"
                    + "             #{generateRecordId,jdbcType=VARCHAR}\n"
                    + "            , #{releaseRecordId,jdbcType=VARCHAR}\n"
                    + "            , #{contentFillParam,jdbcType=VARCHAR}\n"
                    + "            , #{extend,jdbcType=VARCHAR}\n"
                    + ")"
                    + "</script>")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(CommonAgreementGenerateRecordDO recordDO);


    /**
     * 根据发布记录ID获取协议生成记录
     *
     * @param generateRecordId 发布记录ID
     * @return 匹配的协议发布记录
     */
    @Select("select * from common_agreement_generate_record where   generate_record_id = #{generateRecordId}  limit 1 ")
    CommonAgreementGenerateRecordDO getByGenerateRecordId(@Param("generateRecordId") String generateRecordId);


}
