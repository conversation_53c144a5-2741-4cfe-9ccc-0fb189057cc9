package com.timevale.infoauth.deploy.callback;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.timevale.infoauth.service.enums.InfoAuthServiceType;
import com.timevale.infoauth.service.impl.boost.LogSupoort;
import com.timevale.infoauth.service.impl.boost.MonitorAlertResultSupport;
import com.timevale.infoauth.service.impl.config.AutoConfig;
import com.timevale.infoauth.service.impl.constants.ProviderEnum;
import com.timevale.infoauth.service.impl.utils.HttpClient;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMethod;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Optional;

/**
 * 信息比对服务 - 不涉及刷脸
 * <AUTHOR> href="mailto:<EMAIL>">jiuchen</a>
 */
@Slf4j
@Api(tags = "回调")
@ExternalService
@RestMapping(path = "/infoauth")
public class LogMonitorCallback {

  /** 供应商监控触发告警后回调地址 */
  @ApiOperation(value = "供应商监控触发告警后回调地址", httpMethod = "POST")
  @RestMapping(
      path = "/callback/monitor",
      method = {RequestMethod.POST})
  public void monitorCallback(String content) {
    LogSupoort.monitorAlertInfo(log, "monitorCallback content : {}", content);
    if (StringUtils.isBlank(content)) return;

    JSONObject jsonObject = JSON.parseObject(content);
    String condition = jsonObject.getString("condition");
    JSONObject annotations = jsonObject.getJSONObject("annotations");

    if (null == annotations
        || StringUtils.isBlank(condition)
        || !AutoConfig.monitorAutoHandleEnabled) {
      LogSupoort.monitorAlertInfo(log, "monitorCallback break point1");
      return;
    }

    String desc = annotations.getString("desc");
    InfoAuthServiceType serviceType =
        Optional.ofNullable(annotations.getString("biz_type"))
            .map(InfoAuthServiceType::valueOf)
            .orElse(null);
    ProviderEnum provider =
        Optional.ofNullable(annotations.getString("provider"))
            .map(ProviderEnum::valueOf)
            .orElse(null);
    if (null == serviceType || null == provider) {
      LogSupoort.monitorAlertInfo(log, "monitorCallback break point2");
      return;
    }

    Long markTemporaryOffline =
        MonitorAlertResultSupport.markTemporaryOffline(serviceType, provider);
    Long markPermanentOffline =
        MonitorAlertResultSupport.markPermanentOffline(serviceType, provider);
    if (null == markTemporaryOffline || null == markPermanentOffline) {
      LogSupoort.monitorAlertInfo(log, "monitorCallback break point3");
      return;
    }

    if (MonitorAlertResultSupport.checkProviderIsOffLine(serviceType, provider)) {
      // 2024/10/16   供应商已经下线
      log.info("{} 供应商已经被永久下线 ,不发送消息通知  {}次 {} {}", desc, markTemporaryOffline, serviceType, provider);
    } else if (singleAvailableProvider(serviceType, provider)) {
      sendMsg("【告警：" + desc + "】" + AutoConfig.monitorAutoNoCanDowngradeProviderWarnMessage,
              desc,
              serviceType,
              provider);
    } else if (markPermanentOffline == AutoConfig.monitorAutoPermanentOfflineFireCount) {
      sendMsg(
          "【告警："
              + desc
              + "】"
              + AutoConfig.monitorAutoPermanentOfflineWatchDuration
              + "分钟内告警超过"
              + AutoConfig.monitorAutoPermanentOfflineFireCount
              + "次，已被系统永久下架",
          desc,
          serviceType,
          provider);

      // 2024/10/15   执行下线供应商操作  下线完成后缓存标记会自动清 0
      MonitorAlertResultSupport.offlineSubsidiaryAlarm(serviceType, provider);
    } else if (markTemporaryOffline == AutoConfig.monitorAutoTemporaryOfflineFireCount) {
      sendMsg(
          "【告警："
              + desc
              + "】已被系统自动下架，如无人操作将在"
              + AutoConfig.monitorAutoTemporaryOfflineTimeout
              + "分钟后自动上架",
          desc,
          serviceType,
          provider);
    } else if (markTemporaryOffline == 1) {
      sendMsg(
          "【告警："
              + desc
              + "】已被系统打标，如"
              + AutoConfig.monitorAutoTemporaryOfflineWatchDuration
              + "分钟内触发"
              + AutoConfig.monitorAutoTemporaryOfflineFireCount
              + "次告警，系统将自动下架"
              + AutoConfig.monitorAutoTemporaryOfflineTimeout
              + "分钟",
          desc,
          serviceType,
          provider);
    }else {
      log.info("{} 供应商第 {} 下线告警 ,不发送消息通知  {} {}", desc, markTemporaryOffline, serviceType, provider);
    }
  }

  /**
   * @return 只存在单个可活供应商
   */
  private boolean singleAvailableProvider(InfoAuthServiceType serviceType, ProviderEnum provider){
    try {
      return MonitorAlertResultSupport.singleAvailableProvider(serviceType, provider);
    } catch (Exception e) {
      LogSupoort.monitorAlertInfo(log, "判断只存在单个可活供应商失败 : {}", provider);
    }
    return false;
  }

  private void sendMsg(
      String content, String desc, InfoAuthServiceType serviceType, ProviderEnum providerEnum) {
    JSONArray jsonArray = new JSONArray();

    String[] usernames =
        CollectionUtils.isNotEmpty(AutoConfig.monitorAutoNotifyUsernames)
            ? AutoConfig.monitorAutoNotifyUsernames.toArray(new String[0])
            : new String[] {"jiuchen"};

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
    String fireTime = sdf.format(new Date());

    JSONObject jsonObject = new JSONObject();
    jsonObject.put("from", AutoConfig.monitorAutoNotifyFrom);
    jsonObject.put("eventId", 1234);
    jsonObject.put("subject", "监控平台告警");
    jsonObject.put("app", "infoauth");
    jsonObject.put("type", "DING_CROP_CONVERSATION");
    jsonObject.put("usernames", usernames);
    jsonObject.put("content", content + "，触发时间：" + fireTime);
    jsonArray.add(jsonObject);

    try {
      if (AutoConfig.monitorAutoNotifyDingCropConversationEnabled) {
        // DING_CROP_CONVERSATION
        HttpClient.postJson(AutoConfig.monitorAutoNotifyUrl, jsonArray.toJSONString());
        LogSupoort.monitorAlertInfo(log, "send DING_CROP_CONVERSATION");
      }

      if (AutoConfig.monitorAutoNotifySmsEnabled) {
        // SMS
        jsonObject.put("type", "SMS");
        HttpClient.postJson(AutoConfig.monitorAutoNotifyUrl, jsonArray.toJSONString());
        LogSupoort.monitorAlertInfo(log, "send SMS");
      }

      if (AutoConfig.monitorAutoNotifyDingGroupRobotEnabled) {
        // DING_ROBOT
        jsonObject.put("type", "DING_ROBOT");
        jsonObject.put(
            "content",
            jsonObject.getString("content")
                + "，【告警排查业务日志SQL】"
                + "app:infoauth and datasource : \"/k8s/logs/provider.log\" | select json_extract_scalar(json, '$.biz_type_title') as biz_type_title,json_extract_scalar(json, '$.provider_title') as provider_title,json_extract_scalar(json, '$.biz_status') as biz_status,json_extract_scalar(json, '$.provider_msg') as provider_msg,count(1) as total   from ( select  json_parse(regexp_extract(Content, 'providerLog\\(\\d+\\) - ({.*}) \\[Trac', 1)) as json from log ) group by biz_type_title,provider_title,biz_status,provider_msg having biz_type_title = '"
                + serviceType.getDescription()
                + "' and provider_title='"
                + providerEnum.desc()
                + "'  order by total desc limit 1000");

        JSONObject robotParamsJSONObject = new JSONObject();
        robotParamsJSONObject.put(
            "robotTokens", new String[] {AutoConfig.monitorAutoNotifyRobotToken});
        robotParamsJSONObject.put(
            "atUserNames", new ArrayList<>(jsonObject.getJSONArray("usernames")));

        jsonObject.put("robotParams", robotParamsJSONObject);
        HttpClient.postJson(AutoConfig.monitorAutoNotifyUrl, jsonArray.toJSONString());
        LogSupoort.monitorAlertInfo(log, "send DING_ROBOT");
      }
    } catch (Exception e) {
      e.printStackTrace();
    }
  }
}
