package com.timevale.infoauth.service.request.agreement;

import lombok.Data;

/**
 * 通用协议新增信息
 *
 * <AUTHOR>
 */
@Data
public class CommonAgreementContentAddRequest extends CommonAgreementBaseRequest {

  /** 协议发布版本. */
  private String releaseVersion;

  /**
   * 协议语言.
   * @see com.timevale.infoauth.service.enums.agreement.CommonAgreementLangEnum
   * */
  private String lang;

  /** 协议简称. */
  private String shortName;

  /** 协议全称. */
  private String fullName;

  /** 协议发布备注. */
  private String releaseRemark;

  /** 协议内容. */
  private String content;
  /**
   * 协议内容存储类型 htmlContent=html文本 htmlUrl=http地址 pdfUrl=pdf地址 zeyuanUrl=泽元系统Url.
   * @see com.timevale.infoauth.service.enums.agreement.CommonAgreementContentTypeEnum
   * */
  private String contentType;
  /** 操作人. */
  private String operator;
  /** 创建人. */
  private String creator;

}
