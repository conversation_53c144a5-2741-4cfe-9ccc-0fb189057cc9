package com.timevale.infoauth.service.agreement.component;

import com.timevale.infoauth.dal.infoauth.dao.agreement.CommonAgreementInfoMapper;
import com.timevale.infoauth.dal.infoauth.dao.agreement.CommonAgreementReleaseRecordMapper;
import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementInfoDO;
import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementInfoExtendDO;
import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementReleaseRecordDO;
import com.timevale.infoauth.service.enums.agreement.CommonAgreementLangEnum;
import com.timevale.infoauth.service.request.agreement.CommonAgreementBatchQueryRequest;
import com.timevale.infoauth.service.request.agreement.CommonAgreementListQueryRequest;
import com.timevale.infoauth.service.request.agreement.CommonAgreementQueryRequest;
import com.timevale.infoauth.service.request.agreement.CommonAgreementReleaseHistoryQueryRequest;
import com.timevale.mandarin.base.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/12/4 20:27
 */
@Component
public class CommonAgreementComponent {



    @Autowired
    private CommonAgreementInfoMapper agreementInMapper;

    @Autowired
    private CommonAgreementReleaseRecordMapper agreementReleaseRecordMapper;



    public List<CommonAgreementInfoExtendDO> getBatchAgreementIdOnRelease(CommonAgreementBatchQueryRequest request) {

        List<String> commonAgreementIds = request.getCommonAgreementIds();
        if(CollectionUtils.isEmpty(commonAgreementIds)){
            return new ArrayList<>();
        }
        return agreementInMapper.getBatchAgreementIdOnRelease(commonAgreementIds);
    }


    public List<CommonAgreementInfoDO> getAgreementListPages(CommonAgreementListQueryRequest request) {
        Integer pageIndex = request.getPageIndex();
        Integer pageSize = request.getPageSize();
        int currIndex = pageIndex < 1 ? 0: (pageIndex-1) * pageSize;
        int currSize =   pageSize < 1 ? 20: pageSize;
        return agreementInMapper.getListPages(currIndex, currSize);
    }



    public long countAgreements(CommonAgreementListQueryRequest request) {
        return agreementInMapper.count();
    }

    public List<CommonAgreementReleaseRecordDO> getAgreementReleaseRecords(CommonAgreementReleaseHistoryQueryRequest request) {

        String commonAgreementId = request.getCommonAgreementId();
        int currIndex = 0;
        int currSize =   20;
        return agreementReleaseRecordMapper.getListPages(commonAgreementId, currIndex, currSize);
    }

    public Pair<CommonAgreementInfoDO,CommonAgreementReleaseRecordDO> getAgreementInfoBase(
            CommonAgreementQueryRequest request) {
        String agreementId = request.getCommonAgreementId();
        CommonAgreementInfoDO agreementInfoDO =
                agreementInMapper.getByAgreementId(agreementId);
        if(agreementInfoDO == null){
             return null;
        }
        String queryReleaseVersion = request.getReleaseVersion();
        if (StringUtils.isBlank(queryReleaseVersion)) {
            queryReleaseVersion = agreementInfoDO.getReleaseVersion();
        }
        List<CommonAgreementReleaseRecordDO>  records =
                agreementReleaseRecordMapper.getByAgreementIdAndVersion(agreementId, queryReleaseVersion);
        if(CollectionUtils.isEmpty(records)){
            return null;
        }

        CommonAgreementLangEnum langEnum =
                CommonAgreementLangEnum.getByLang(request.getLang());
        CommonAgreementReleaseRecordDO record =
                records.stream()
                        .filter(item-> Objects.equals(item.getLang(), langEnum.getLang()))
                        .findFirst()
                        .orElse(null);
        return Pair.of(agreementInfoDO, record);

    }

    public CommonAgreementReleaseRecordDO getByReleaseRecordId(String releaseRecordId){
        return agreementReleaseRecordMapper.getByReleaseRecordId(releaseRecordId);
    }


}
