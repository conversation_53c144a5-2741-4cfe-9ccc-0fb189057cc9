package com.timevale.infoauth.service.api;

import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.request.InfoAuthCheckRequest;
import com.timevale.infoauth.service.request.InfoAuthCheckResponse;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;

@JsonDataIgnore
public class InnerInfoAuthServiceTest extends ApplicationTest {

    @Resource
    private InfoAuthInnerService infoAuthInnerService;


    @Test
    public void testEnterprise2Element() {
        InfoAuthCheckRequest request = new InfoAuthCheckRequest();
        request.setServiceType("ENTERPRISE_2_ELEMENT");
        request.setProviderName("MOCK");
        request.setContext("{\"codeUSC\":\"91231100MA18Y8GU0M\",\"name\":\"esigntest黑河市永发煤矿一\",\"ocrSence\":\"DEFAULT\"}");
        request.setBizAppId("**********");
        RpcOutput<InfoAuthCheckResponse> responseRpcOutput = infoAuthInnerService.infoCheckDesignateProvider(request);
        Assert.assertNotNull(responseRpcOutput);

    }

    @Test
    public void testEnterprise3Element() {
        InfoAuthCheckRequest request = new InfoAuthCheckRequest();
        request.setServiceType("ENTERPRISE_3_ELEMENT");
        request.setProviderName("MOCK");
        request.setContext("{\"codeUSC\":\"9100000068388888FL\",\"name\":\"esigntest芒草改名测试112341746677253564\",\"ocrSence\":\"DEFAULT\",\"ocrSence\":\"DEFAULT\",\"legalName\":\"熊文\"}");
        request.setBizAppId("**********");
        RpcOutput<InfoAuthCheckResponse> responseRpcOutput = infoAuthInnerService.infoCheckDesignateProvider(request);
        Assert.assertNotNull(responseRpcOutput);
    }


    @Test
    public void testLawfirm2Element() {
        InfoAuthCheckRequest request = new InfoAuthCheckRequest();
        request.setServiceType("LAWFIRM_2_ELEMENT");
        request.setProviderName("MOCK");
        request.setContext("{\"codeUSC\":\"31320000MD01904540\",\"name\":\"北京市中银1\",\"ocrSence\":\"DEFAULT\",\"ocrSence\":\"DEFAULT\",\"legalName\":\"熊文\"}");
        request.setBizAppId("**********");
        RpcOutput<InfoAuthCheckResponse> responseRpcOutput = infoAuthInnerService.infoCheckDesignateProvider(request);
        Assert.assertNotNull(responseRpcOutput);
    }

    @Test
    public void testLawfirm3Element() {
        InfoAuthCheckRequest request = new InfoAuthCheckRequest();
        request.setServiceType("LAWFIRM_3_ELEMENT");
        request.setProviderName("MOCK");
        request.setContext("{\"codeUSC\":\"31320000MD01904540\",\"name\":\"北京市中银1\",\"ocrSence\":\"DEFAULT\",\"ocrSence\":\"DEFAULT\",\"legalName\":\"熊文\"}");
        request.setBizAppId("**********");
        RpcOutput<InfoAuthCheckResponse> responseRpcOutput = infoAuthInnerService.infoCheckDesignateProvider(request);
        Assert.assertNotNull(responseRpcOutput);
    }


    @Test
    public void testSocialorg2Element() {
        InfoAuthCheckRequest request = new InfoAuthCheckRequest();
        request.setServiceType("SOCIALORG_2_ELEMENT");
        request.setProviderName("MOCK");
        request.setContext("{\"codeUSC\":\"31320000MD01904540\",\"name\":\"北京市中银1\",\"ocrSence\":\"DEFAULT\",\"ocrSence\":\"DEFAULT\",\"legalName\":\"熊文\"}");
        request.setBizAppId("**********");
        RpcOutput<InfoAuthCheckResponse> responseRpcOutput = infoAuthInnerService.infoCheckDesignateProvider(request);
        Assert.assertNotNull(responseRpcOutput);
    }

    @Test
    public void testSocialorg3Element() {
        InfoAuthCheckRequest request = new InfoAuthCheckRequest();
        request.setServiceType("SOCIALORG_3_ELEMENT");
        request.setProviderName("MOCK");
        request.setContext("{\"codeUSC\":\"31320000MD01904540\",\"name\":\"北京市中银1\",\"ocrSence\":\"DEFAULT\",\"ocrSence\":\"DEFAULT\",\"legalName\":\"熊文\"}");
        request.setBizAppId("**********");
        RpcOutput<InfoAuthCheckResponse> responseRpcOutput = infoAuthInnerService.infoCheckDesignateProvider(request);
        Assert.assertNotNull(responseRpcOutput);
    }


    @Test
    public void testEnterprise_4_element() {
        InfoAuthCheckRequest request = new InfoAuthCheckRequest();
        request.setServiceType("ENTERPRISE_4_ELEMENT");
        request.setProviderName("MOCK");
        request.setContext("{\"codeUSC\":\"9100000068388888FL\",\"name\":\"esigntest芒草改名测试112341746677253564\",\"legalName\":\"熊文\",\"legalCertNo\":\"36220219950930353X\"}");
        request.setBizAppId("**********");
        RpcOutput<InfoAuthCheckResponse> responseRpcOutput = infoAuthInnerService.infoCheckDesignateProvider(request);
        Assert.assertNotNull(responseRpcOutput);
    }
}
