<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="face_photo_compare" physicalName="face_photo_compare" remark="人脸照片比对">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,NAME,APP_ID,BIZ_ID,CERT_NO,REQUEST_ID,INFOAUTH_ID,PROVIDER_ID,PROVIDER_RESULT,RESULT,SUB_TYPE,SERVICE_STATUS,CREATE_TIME,MODIFY_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.NAME,sf.APP_ID,sf.BIZ_ID,sf.CERT_NO,sf.REQUEST_ID,sf.INFOAUTH_ID,sf.PROVIDER_ID,sf.PROVIDER_RESULT,sf.RESULT,sf.SUB_TYPE,sf.SERVICE_STATUS,sf.CREATE_TIME,sf.MODIFY_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:face_photo_compare">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO face_photo_compare(
            ID
            ,NAME
            ,APP_ID
            ,BIZ_ID
            ,CERT_NO
            ,REQUEST_ID
            ,INFOAUTH_ID
            ,PROVIDER_ID
            ,PROVIDER_RESULT
            ,RESULT
            ,SUB_TYPE
            ,SERVICE_STATUS
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES(
             null
            , #{name,jdbcType=VARCHAR}
            , #{appId,jdbcType=VARCHAR}
            , #{bizId,jdbcType=VARCHAR}
            , #{certNo,jdbcType=VARCHAR}
            , #{requestId,jdbcType=VARCHAR}
            , #{infoauthId,jdbcType=VARCHAR}
            , #{providerId,jdbcType=VARCHAR}
            , #{providerResult,jdbcType=VARCHAR}
            , #{result,jdbcType=INTEGER}
            , #{subType,jdbcType=TINYINT}
            , #{serviceStatus,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{modifyTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:face_photo_compare">
        INSERT INTO face_photo_compare(
            ID
            ,NAME
            ,APP_ID
            ,BIZ_ID
            ,CERT_NO
            ,REQUEST_ID
            ,INFOAUTH_ID
            ,PROVIDER_ID
            ,PROVIDER_RESULT
            ,RESULT
            ,SUB_TYPE
            ,SERVICE_STATUS
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.name,jdbcType=VARCHAR}
                , #{item.appId,jdbcType=VARCHAR}
                , #{item.bizId,jdbcType=VARCHAR}
                , #{item.certNo,jdbcType=VARCHAR}
                , #{item.requestId,jdbcType=VARCHAR}
                , #{item.infoauthId,jdbcType=VARCHAR}
                , #{item.providerId,jdbcType=VARCHAR}
                , #{item.providerResult,jdbcType=VARCHAR}
                , #{item.result,jdbcType=INTEGER}
                , #{item.subType,jdbcType=TINYINT}
                , #{item.serviceStatus,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.modifyTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:face_photo_compare">
        <![CDATA[
        UPDATE face_photo_compare
        SET
            NAME            = #{name,jdbcType=VARCHAR}
            ,APP_ID          = #{appId,jdbcType=VARCHAR}
            ,BIZ_ID          = #{bizId,jdbcType=VARCHAR}
            ,CERT_NO         = #{certNo,jdbcType=VARCHAR}
            ,REQUEST_ID      = #{requestId,jdbcType=VARCHAR}
            ,INFOAUTH_ID     = #{infoauthId,jdbcType=VARCHAR}
            ,PROVIDER_ID     = #{providerId,jdbcType=VARCHAR}
            ,PROVIDER_RESULT = #{providerResult,jdbcType=VARCHAR}
            ,RESULT          = #{result,jdbcType=INTEGER}
            ,SUB_TYPE        = #{subType,jdbcType=TINYINT}
            ,SERVICE_STATUS  = #{serviceStatus,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,MODIFY_TIME     = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:face_photo_compare">
        <![CDATA[
        DELETE FROM face_photo_compare
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:face_photo_compare">
        SELECT *
        FROM face_photo_compare
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
</table>
