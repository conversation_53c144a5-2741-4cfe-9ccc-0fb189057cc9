package com.timevale.infoauth.dal.infoauth.dataobject;

import lombok.Data;

@Data
public class InfoServiceCommonOcrDO {
  //主键ID
  private Integer id;

  //应用id
  private String appId;

  //信息比对类型
  private Integer type;

  //服务ID
  private String infoauthId;

  //业务请求ID
  private String requestId;

  //服务提供商
  private String providerId;

  //服务提供商请求ID
  private String providerRequestId;

  //证件自拍正面照
  private String photoIdcardFace;


  //证件自拍国徽照
  private String photoIdcardEmblem;

  //OCR识别结果（适配后）
  private Integer result;

  //比对结果
  private Integer serviceStatus;

  //OCR识别结果(第三方)
  private String providerResult;

  //OCR识别姓名
  private String ocrName;

  //OCR识别身份证
  private String ocrIdno;

  //OCR识别出生
  private String ocrBirth;

  //OCR识别住址
  private String ocrAddress;

  //OCR识别民族/国家
  private String ocrNation;

  //性别
  private String ocrSex;

  //OCR识别证件有效期起始时间
  private String ocrStartTime;

  //OCR识别证件有效期截止时间
  private String ocrEndTime;

  //OCR识别发证机关
  private String ocrOrgan;

  //OCR识别结果照，身份证头像照
  private String ocrExtend;


  //数据创建时间
  private String createTime;

  //数据修改时间
  private String modifyTime;


}
