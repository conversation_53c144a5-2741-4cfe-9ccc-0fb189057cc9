package com.timevale.infoauth.service.model;

import com.timevale.infoauth.service.enums.OcrSenceEnum;
import com.timevale.infoauth.service.utils.ValidationUtil;
import com.timevale.mandarin.base.exception.BaseRuntimeException;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/** @Author: <PERSON> @Description: @Date: Created in 2018/6/14 @Modified By: */
@Data
public class BaseModel extends ToString {

  /**
   * 业务appId
   */
  private String bizAppId;


  /**
   * ocr场景
   */
  private String ocrSence = OcrSenceEnum.DEFAULT.name() ;

  public BaseModel() {}

  public void valid() throws BaseRuntimeException {
    ValidationUtil.validateBean(this);
  }
}