package com.timevale.infoauth.service.enums;

import com.timevale.mandarin.base.exception.BaseRuntimeException;

/** @Author: <PERSON> @Description: @Date: Created in 2018/3/19 @Modified By: */
public enum UnionpayUserCertTypeEnum {
  /**大陆身份证*/
  CRED_PSN_CH_IDCARD("大陆身份证", 1, "大陆身份证", "01"),
  /**台湾来往大陆通行证*/
  CRED_PSN_CH_TWCARD("台湾来往大陆通行证", 4, "台胞证", "05"),
  /**澳门来往大陆通行证*/
  CRED_PSN_CH_MACAO("澳门来往大陆通行证", 11, "回乡证", "04"),
  /**香港来往大陆通行证*/
  CRED_PSN_CH_HONGKONG("香港来往大陆通行证", 12, "回乡证", "04"),
  /**护照*/
  CRED_PSN_PASSPORT("护照", 2, "护照", "03"),
  /**未知*/
  UNKNOWN("未知", 0, "未知", "0");

  private String name;

  private int dbValue;

  private String idType;

  private String idTypeCode;

  UnionpayUserCertTypeEnum(String name, int dbValue, String idType, String idTypeCode) {
    this.name = name;
    this.dbValue = dbValue;
    this.idType = idType;
    this.idTypeCode = idTypeCode;
  }

  public String getName() {
    return name;
  }

  public int dbValue() {
    return dbValue;
  }

  public String getIdType() {
    return idType;
  }

  public String getIdTypeCode() {
    return idTypeCode;
  }

  public static UnionpayUserCertTypeEnum getByDbValue(int dbValue) throws BaseRuntimeException{

    for (UnionpayUserCertTypeEnum certType : UnionpayUserCertTypeEnum.values()) {
      if (certType.dbValue() == dbValue) {
        return certType;
      }
    }
    throw new BaseRuntimeException("证件类型不存在");
  }
}
