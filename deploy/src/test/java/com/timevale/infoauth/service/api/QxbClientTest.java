package com.timevale.infoauth.service.api;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.enums.InfoAuthServiceType;
import com.timevale.infoauth.service.impl.common.dto.OrgProvout;
import com.timevale.infoauth.service.impl.common.dto.OrganProvin;
import com.timevale.infoauth.service.impl.constants.ProviderEnum;
import com.timevale.infoauth.service.impl.provider.client.qixinbao.QixinbaoOrgClient;
import com.timevale.infoauth.service.impl.provider.manager.manual.application.assembler.ProviderClientAssembler;
import com.timevale.infoauth.service.impl.provider.manager.manual.domain.client.service.ClientDomainService;
import com.timevale.infoauth.service.impl.provider.manager.manual.domain.manager.domain.primitive.BizType;
import com.timevale.infoauth.service.impl.provider.manager.manual.domain.manager.domain.primitive.ProviderName;
import com.timevale.infoauth.service.impl.provider.manager.manual.domain.manager.service.ManagerDomainService;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
@JsonDataIgnore
public class QxbClientTest extends ApplicationTest {

  @Resource private ManagerDomainService managerDomainService;

  @Resource private ClientDomainService clientDomainService;

  @Test
  public void org2QueryTest() {
    OrganProvin input = new OrganProvin();
    input.setName("913301087458306077");
    OrgProvout orgProvout = QixinbaoOrgClient.org2Query(
        input,
        ProviderClientAssembler.toConfig(
            managerDomainService.getByNameAndType(
                new ProviderName(ProviderEnum.QIXINBAO.dbKey()),
                new BizType(InfoAuthServiceType.ENTERPRISE_2_ELEMENT.dbValue())),
            clientDomainService.getByNameAndType(
                new ProviderName(ProviderEnum.QIXINBAO.dbKey()),
                new BizType(InfoAuthServiceType.ENTERPRISE_2_ELEMENT.dbValue()))));
    Assert.assertNotNull(orgProvout);
    Assert.assertTrue(true);
  }

  @Test
  public void org2QueryNoFoundTest() {
    OrganProvin input = new OrganProvin();
    input.setName("91360902MA7CFPWEXL");
    OrgProvout orgProvout = QixinbaoOrgClient.org2Query(
        input,
        ProviderClientAssembler.toConfig(
            managerDomainService.getByNameAndType(
                new ProviderName(ProviderEnum.QIXINBAO.dbKey()),
                new BizType(InfoAuthServiceType.ENTERPRISE_2_ELEMENT.dbValue())),
            clientDomainService.getByNameAndType(
                new ProviderName(ProviderEnum.QIXINBAO.dbKey()),
                new BizType(InfoAuthServiceType.ENTERPRISE_2_ELEMENT.dbValue()))));
    Assert.assertNotNull(orgProvout);
    Assert.assertTrue(true);
  }

  @Test
  public void org3QueryTest() {
    OrganProvin input = new OrganProvin();
    input.setName("913301087458306077");
    OrgProvout orgProvout = QixinbaoOrgClient.org3Query(
        input,
        ProviderClientAssembler.toConfig(
            managerDomainService.getByNameAndType(
                new ProviderName(ProviderEnum.QIXINBAO.dbKey()),
                new BizType(InfoAuthServiceType.ENTERPRISE_3_ELEMENT.dbValue())),
            clientDomainService.getByNameAndType(
                new ProviderName(ProviderEnum.QIXINBAO.dbKey()),
                new BizType(InfoAuthServiceType.ENTERPRISE_3_ELEMENT.dbValue()))));
    Assert.assertNotNull(orgProvout);
    Assert.assertTrue(true);
  }

  @Test
  public void org3QueryNotFoundTest() {
    OrganProvin input = new OrganProvin();
    input.setName("91360902MA7CFPWEXL");
    OrgProvout orgProvout = QixinbaoOrgClient.org3Query(
        input,
        ProviderClientAssembler.toConfig(
            managerDomainService.getByNameAndType(
                new ProviderName(ProviderEnum.QIXINBAO.dbKey()),
                new BizType(InfoAuthServiceType.ENTERPRISE_3_ELEMENT.dbValue())),
            clientDomainService.getByNameAndType(
                new ProviderName(ProviderEnum.QIXINBAO.dbKey()),
                new BizType(InfoAuthServiceType.ENTERPRISE_3_ELEMENT.dbValue()))));
    Assert.assertNotNull(orgProvout);
    Assert.assertTrue(true);
  }

  @Test
  public void socialOrganizationQueryTest() {
    OrganProvin input = new OrganProvin();
    input.setName("12100000401359321Q");
    OrgProvout orgProvout = QixinbaoOrgClient.socialOrganizationQuery(
        input,
        ProviderClientAssembler.toConfig(
            managerDomainService.getByNameAndType(
                new ProviderName(ProviderEnum.QIXINBAO.dbKey()),
                new BizType(InfoAuthServiceType.SOCIALORG_3_ELEMENT.dbValue())),
            clientDomainService.getByNameAndType(
                new ProviderName(ProviderEnum.QIXINBAO.dbKey()),
                new BizType(InfoAuthServiceType.SOCIALORG_3_ELEMENT.dbValue()))));
    Assert.assertNotNull(orgProvout);
    Assert.assertTrue(true);
  }

  @Test
  public void socialOrganizationNotFoundQueryTest() {
    OrganProvin input = new OrganProvin();
    input.setName("92230223MA1923BW8Q");
    OrgProvout orgProvout = QixinbaoOrgClient.socialOrganizationQuery(
        input,
        ProviderClientAssembler.toConfig(
            managerDomainService.getByNameAndType(
                new ProviderName(ProviderEnum.QIXINBAO.dbKey()),
                new BizType(InfoAuthServiceType.SOCIALORG_3_ELEMENT.dbValue())),
            clientDomainService.getByNameAndType(
                new ProviderName(ProviderEnum.QIXINBAO.dbKey()),
                new BizType(InfoAuthServiceType.SOCIALORG_3_ELEMENT.dbValue()))));
    Assert.assertNotNull(orgProvout);
    Assert.assertTrue(true);
  }

  @Test
  public void lawFirmQueryTest() {
    OrganProvin input = new OrganProvin();
    input.setName("31370000693763173G");
    OrgProvout orgProvout = QixinbaoOrgClient.lawFirmQuery(
        input,
        ProviderClientAssembler.toConfig(
            managerDomainService.getByNameAndType(
                new ProviderName(ProviderEnum.QIXINBAO.dbKey()),
                new BizType(InfoAuthServiceType.LAWFIRM_3_ELEMENT.dbValue())),
            clientDomainService.getByNameAndType(
                new ProviderName(ProviderEnum.QIXINBAO.dbKey()),
                new BizType(InfoAuthServiceType.LAWFIRM_3_ELEMENT.dbValue()))));
    Assert.assertNotNull(orgProvout);
    Assert.assertTrue(true);
  }

  @Test
  public void lawFirmQueryNotFoundTest() {
    OrganProvin input = new OrganProvin();
    input.setName("31320000MD0174665W");
    OrgProvout orgProvout = QixinbaoOrgClient.lawFirmQuery(
        input,
        ProviderClientAssembler.toConfig(
            managerDomainService.getByNameAndType(
                new ProviderName(ProviderEnum.QIXINBAO.dbKey()),
                new BizType(InfoAuthServiceType.LAWFIRM_3_ELEMENT.dbValue())),
            clientDomainService.getByNameAndType(
                new ProviderName(ProviderEnum.QIXINBAO.dbKey()),
                new BizType(InfoAuthServiceType.LAWFIRM_3_ELEMENT.dbValue()))));
    Assert.assertNotNull(orgProvout);
    Assert.assertTrue(true);
  }
}
