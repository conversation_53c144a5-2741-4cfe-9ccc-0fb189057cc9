package com.timevale.infoauth.service.enums;

public enum Coder implements IStringCoder {
  BASE64(new Base64Coder()),
  BINARY(new BinaryCoder());

  private IStringCoder coder;

  private Coder(IStringCoder coder) {
    this.coder = coder;
  }

  public String encode(byte[] data) {
    try {
      return this.coder.encode(data);
    } catch (Exception var3) {
      return null;
    }
  }

  public byte[] decode(String data) {
    try {
      return this.coder.decode(data);
    } catch (Exception var3) {
      return null;
    }
  }
}
