package com.timevale.infoauth.service.enums;

public enum OrgbsStatus {
  CAPMING((short) 1),

  CANCEL((short) 2),

  WITHDRAW((short) 3),

  OTHER((short) 4),

  UNKNOWN((short) 0),
  ;

  public short dbValue() {
    return dbValue;
  }

  OrgbsStatus(short dbValue) {
    this.dbValue = dbValue;
  }

  private short dbValue;

  public static OrgbsStatus from(short dbValue) {

    for (OrgbsStatus i : OrgbsStatus.values()) {
      if (i.dbValue == dbValue) {
        return i;
      }
    }

    return UNKNOWN;
  }
}
