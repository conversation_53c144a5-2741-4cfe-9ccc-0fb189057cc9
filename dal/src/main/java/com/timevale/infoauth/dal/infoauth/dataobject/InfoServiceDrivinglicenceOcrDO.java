package com.timevale.infoauth.dal.infoauth.dataobject;

import com.timevale.infoauth.dal.infoauth.utils.StringUtil;
import lombok.Getter;

import java.util.Date;

@Getter
public class InfoServiceDrivinglicenceOcrDO {
  private int id;
  private String infoauthId;
  private String requestId;
  private String name;
  private String number;
  private String sex;
  private String address;
  private String nationality;
  private String validTime;
  private String birth;
  private String certType;
  private String driveType;
  private String providerId;
  private String providerSeqNo;
  private Date createTime;
  private Date modifyTime;
  private Integer result;
  private Integer serviceStatus;

  public void setId(int id) {
    this.id = id;
  }

  public void setInfoauthId(String infoauthId) {
    this.infoauthId = infoauthId;
  }

  public void setRequestId(String requestId) {
    this.requestId = requestId;
  }

  public void setName(String name) {
    this.name = StringUtil.truncation(name, 32);
  }

  public void setNumber(String number) {
    this.number = StringUtil.truncation(number, 64);
  }

  public void setSex(String sex) {
    this.sex = StringUtil.truncation(sex, 16);
  }

  public void setAddress(String address) {
    this.address = StringUtil.truncation(address, 32);
  }

  public void setNationality(String nationality) {
    this.nationality = StringUtil.truncation(nationality, 32);
  }

  public void setValidTime(String validTime) {
    this.validTime = StringUtil.truncation(validTime, 32);
  }

  public void setBirth(String birth) {
    this.birth = StringUtil.truncation(birth, 32);
  }

  public void setCertType(String certType) {
    this.certType = StringUtil.truncation(certType, 16);
  }

  public void setDriveType(String driveType) {
    this.driveType = StringUtil.truncation(driveType, 36);
  }

  public void setProviderId(String providerId) {
    this.providerId = providerId;
  }

  public void setProviderSeqNo(String providerSeqNo) {
    this.providerSeqNo = StringUtil.truncation(providerSeqNo, 64);
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public void setModifyTime(Date modifyTime) {
    this.modifyTime = modifyTime;
  }

  public void setResult(Integer result) {
    this.result = result;
  }

  public void setServiceStatus(Integer serviceStatus) {
    this.serviceStatus = serviceStatus;
  }
}
