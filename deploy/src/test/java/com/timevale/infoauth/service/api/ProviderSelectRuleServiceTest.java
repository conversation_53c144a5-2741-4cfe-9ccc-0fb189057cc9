package com.timevale.infoauth.service.api;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.HeaderUtils;
import com.timevale.infoauth.service.request.ProviderSelectServiceRequest;
import com.timevale.infoauth.service.response.ProviderSelectServiceResponse;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import javax.annotation.Resource;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @since 2023/12/13 16:44
 */
@JsonDataIgnore
public class ProviderSelectRuleServiceTest extends ApplicationTest {

  @Resource
  private ProviderSelectRuleService providerSelectRuleService;

  @Test
  public void testProviderSelectOnlyWithoutAppId() {
    HeaderUtils.setHeader();
    ProviderSelectServiceRequest requestWithoutAppId =
        ProviderSelectServiceRequest.builder().
            serviceTypeName("ENTERPRISE_3_ELEMENT")
            .build();
    ProviderSelectServiceResponse responseWithoutAppId = providerSelectRuleService.providerSelectOnly(
        requestWithoutAppId);
    Assert.assertNotNull(requestWithoutAppId);
    Assert.assertNotNull(responseWithoutAppId.getSelectedProvider());
  }

  @Test
  public void testProviderSelectOnly() {
    HeaderUtils.setHeader();
    ProviderSelectServiceRequest request =
        ProviderSelectServiceRequest.builder()
                .serviceTypeName("ENTERPRISE_3_ELEMENT")
            .bizAppId("**********")
            .mainChannelKey("organlistmain")
            .build();
    ProviderSelectServiceResponse response = providerSelectRuleService.providerSelectOnly(
        request);
    Assert.assertNotNull(response);
    Assert.assertNotNull(response.getSelectedProvider());
  }
}
