package com.timevale.infoauth.dal.infoauth.cache;

public interface CacheConstant {
  String PREFIX = "infoauth:";
  String CACHE_NAME = "do_cache";
  String CACHE_NAME_PROVIDER = "do_cache_provider";
  long expiration = 5 * 60L;
  String CACHE_PROVIDER_HEADER = "provider_cache";
  String CACHE_INFO_PROVIDER_DAO = "cache_info_provider_dao";
  String CACHE_AVAILABLE_PROVIDERS = "available_providers";
  String CACHE_ALL_PROVIDERS = "all_providers";
  String CACHE_PROVIDER_CONFIGS = "provider_configs";

  // 缓存表
  String CACHE_TABLE_INFO_BACKSTAGE_ROUTE = "backstage_table";
  // 缓存行
  String CACHE_RECORD_INFO_BACKSTAGE_ROUTE = "backstage_record";

  String CACHE_BREAKER_ACTUATOR_TIMEOUT_TAG_PROVIDER_TIMESTAMP = PREFIX + "actuator_timeout_timestamp:";
  String CACHE_BREAKER_ACTUATOR_NOTFOUND_TAG_PROVIDER = PREFIX +"actuator_notfound_provider:";
  String CACHE_ONLINE_PROVIDER_CHANGE_TIMESTAMP = PREFIX + "online_provider_change_timestamp";
}
