package com.timevale.infoauth.service.api;

import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.request.query.AuthInfoQueryRequest;
import com.timevale.infoauth.service.response.QuickSignAuthInfoQueryResponse;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Date;

@JsonDataIgnore
public class QuickSignQueryServiceTest extends ApplicationTest {

  @Resource private InfoAuthQuickSignQueryService infoAuthQuickSignQueryService;

    @Test
    public void psn4Test() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setIdno("362428198711110010");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("1578461255000")));
        authInfoQueryRequest.setBizAppId("**********");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryPsnInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void quickSignQueryOrgInfoCodeORG() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setCodeORG("123");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("1536114315000")));
        authInfoQueryRequest.setBizAppId("1");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryOrgInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void quickSignQueryOrgInfoCodeREG() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setCodeREG("456");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("1536114315000")));
        authInfoQueryRequest.setBizAppId("1");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryOrgInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void quickSignQueryOrgInfoCodeUSC() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setCodeUSC("913301087458306077");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("1536064855000")));
        authInfoQueryRequest.setBizAppId("1");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryOrgInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void psnZhiMaTest() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setIdno("362428198711110010");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("1578461453000")));
        authInfoQueryRequest.setBizAppId("**********");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryPsnInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void psn3Test() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setIdno("362428198711110010");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("1578461385000")));
        authInfoQueryRequest.setBizAppId("**********");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryPsnInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void psn2Test() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setIdno("452502197608103890");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("1608795009000")));
        authInfoQueryRequest.setBizAppId("3876543983");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryPsnInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void psnManualTest() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setIdno("362428198711110010");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("1578461678000")));
        authInfoQueryRequest.setBizAppId("**********");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryPsnInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void orgAuthTest() {
        //企业信息比对
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = null;
        try {

            authInfoQueryRequest.setCodeUSC("362428198711110010");
            authInfoQueryRequest.setCodeORG("362428198711110010");
            authInfoQueryRequest.setCodeREG("362428198711110010");
            authInfoQueryRequest.setCurrIndex(0);
            authInfoQueryRequest.setPageSize(10);
            authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("1578461255000")));
            authInfoQueryRequest.setBizAppId("**********");
            responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryOrgInfo(authInfoQueryRequest);

        } catch (Exception e) {
            e.printStackTrace();
        }
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void orgTransferTest() {
        //企业随机打款
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = null;
        try {

            authInfoQueryRequest.setCodeUSC("91320506573772470Y");
            authInfoQueryRequest.setCodeORG("91320506573772470Y");
            authInfoQueryRequest.setCodeREG("91320506573772470Y");
            authInfoQueryRequest.setCurrIndex(0);
            authInfoQueryRequest.setPageSize(10);
            authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("1582271373470")));
            authInfoQueryRequest.setBizAppId("3438757903");
            responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryOrgInfo(authInfoQueryRequest);

        } catch (Exception e) {
            e.printStackTrace();
        }
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void orgZhiMaTest() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = null;
        try {

            authInfoQueryRequest.setCodeUSC("913301087458306077");
            authInfoQueryRequest.setCodeORG("913301087458306077");
            authInfoQueryRequest.setCodeREG("913301087458306077");
            authInfoQueryRequest.setCurrIndex(0);
            authInfoQueryRequest.setPageSize(10);
            authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("1582008438000")));
            authInfoQueryRequest.setBizAppId("3438757553");
            responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryOrgInfo(authInfoQueryRequest);

        } catch (Exception e) {
            e.printStackTrace();
        }
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void orgFrsignTest() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = null;
        try {

            authInfoQueryRequest.setCodeUSC("92222403MA14UDL80B");
            authInfoQueryRequest.setCodeORG("92222403MA14UDL80B");
            authInfoQueryRequest.setCodeREG("92222403MA14UDL80B");
            authInfoQueryRequest.setCurrIndex(0);
            authInfoQueryRequest.setPageSize(10);
            authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("1564140166000")));
            authInfoQueryRequest.setBizAppId("3438756698");
            responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryOrgInfo(authInfoQueryRequest);

        } catch (Exception e) {
            e.printStackTrace();
        }
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void authInfoRecordPsn4Test() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setIdno("362428198711110010");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setEndTime(new Date(Long.parseLong("1578461255000")));
        authInfoQueryRequest.setStartTime(new Date(Long.parseLong("1578455182000")));
        authInfoQueryRequest.setBizAppId("**********");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.authInfoRecordQueryPsnInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void authInfoRecordQuickSignQueryOrgInfoCodeORG() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setCodeORG("123");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setEndTime(new Date(Long.parseLong("1536114315000")));
        authInfoQueryRequest.setStartTime(new Date(Long.parseLong("1536114305000")));
        authInfoQueryRequest.setBizAppId("1");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.authInfoRecordQueryOrgInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void authInfoRecordQuickSignQueryOrgInfoCodeREG() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setCodeREG("456");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setEndTime(new Date(Long.parseLong("1536114315000")));
        authInfoQueryRequest.setStartTime(new Date(Long.parseLong("1536114305000")));
        authInfoQueryRequest.setBizAppId("1");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.authInfoRecordQueryOrgInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void authInfoRecordQuickSignQueryOrgInfoCodeUSC() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setCodeUSC("913301087458306077");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setEndTime(new Date(Long.parseLong("1536064855000")));
        authInfoQueryRequest.setStartTime(new Date(Long.parseLong("1536064851000")));
        authInfoQueryRequest.setBizAppId("1");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.authInfoRecordQueryOrgInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void quickSignQueryPsnAllListInfo(){
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setIdno("362428198711110010");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("1578461250000")));
        authInfoQueryRequest.setBizAppId("**********");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryPsnListInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void quickSignQueryListPsn2Info(){
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setIdno("362428198711110010");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("*************")));
        authInfoQueryRequest.setBizAppId("**********");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryPsnListInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void quickSignQueryListBankThreeInfo(){
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setIdno("362302198609175011");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("*************")));
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryPsnListInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void quickSignQueryListPsn4Info(){
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setIdno("14273019911003072X");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("*************")));
        authInfoQueryRequest.setBizAppId("**********");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryPsnListInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void quickSignQueryListPsn3Info(){
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setIdno("14273019911003072X");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("1536141616000")));
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryPsnListInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void quickSignQueryListByOrg4Info(){
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setCodeUSC("913301087458306077");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("1582529994000")));
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryOrgListInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void quickSignQueryListByOrg3Info(){
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setCodeUSC("53100000500021676K");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setModifyTime(new Timestamp(Long.parseLong("1585194019000")));
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.quickSignQueryOrgListInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void authInfoRecordPsnZhiMaTest() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setIdno("362428198711110010");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setEndTime(new Date(Long.parseLong("1578461453000")));
        authInfoQueryRequest.setStartTime(new Date(Long.parseLong("1578461451000")));
        authInfoQueryRequest.setBizAppId("**********");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.authInfoRecordQueryPsnInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void authInfoRecordPsn3Test() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setIdno("362428198711110010");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setEndTime(new Date(Long.parseLong("1578461385000")));
        authInfoQueryRequest.setStartTime(new Date(Long.parseLong("1578461381000")));
        authInfoQueryRequest.setBizAppId("**********");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.authInfoRecordQueryPsnInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void authInfoRecordPsnManualTest() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        authInfoQueryRequest.setIdno("362428198711110010");
        authInfoQueryRequest.setCurrIndex(0);
        authInfoQueryRequest.setPageSize(10);
        authInfoQueryRequest.setEndTime(new Date(Long.parseLong("1578461678000")));
        authInfoQueryRequest.setStartTime(new Date(Long.parseLong("1578461671000")));
        authInfoQueryRequest.setBizAppId("**********");
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = infoAuthQuickSignQueryService.authInfoRecordQueryPsnInfo(authInfoQueryRequest);
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void authInfoRecordOrgAuthTest() {
        //企业信息比对
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = null;
        try {

            authInfoQueryRequest.setCodeUSC("362428198711110010");
            authInfoQueryRequest.setCodeORG("362428198711110010");
            authInfoQueryRequest.setCodeREG("362428198711110010");
            authInfoQueryRequest.setCurrIndex(0);
            authInfoQueryRequest.setPageSize(10);
            authInfoQueryRequest.setEndTime(new Date(Long.parseLong("1578461255000")));
            authInfoQueryRequest.setStartTime(new Date(Long.parseLong("1578461251000")));
            authInfoQueryRequest.setBizAppId("**********");
            responseRpcOutput = infoAuthQuickSignQueryService.authInfoRecordQueryOrgInfo(authInfoQueryRequest);

        } catch (Exception e) {
            e.printStackTrace();
        }
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void authInfoRecordOrgTransferTest() {
        //企业随机打款
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = null;
        try {

            authInfoQueryRequest.setCodeUSC("91320506573772470Y");
            authInfoQueryRequest.setCodeORG("91320506573772470Y");
            authInfoQueryRequest.setCodeREG("91320506573772470Y");
            authInfoQueryRequest.setCurrIndex(0);
            authInfoQueryRequest.setPageSize(10);
            authInfoQueryRequest.setEndTime(new Date(Long.parseLong("1582271373470")));
            authInfoQueryRequest.setStartTime(new Date(Long.parseLong("1582271371470")));
            authInfoQueryRequest.setBizAppId("3438757903");
            responseRpcOutput = infoAuthQuickSignQueryService.authInfoRecordQueryOrgInfo(authInfoQueryRequest);

        } catch (Exception e) {
            e.printStackTrace();
        }
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void authInfoRecordOrgZhiMaTest() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = null;
        try {

            authInfoQueryRequest.setCodeUSC("913301087458306077");
            authInfoQueryRequest.setCodeORG("913301087458306077");
            authInfoQueryRequest.setCodeREG("913301087458306077");
            authInfoQueryRequest.setCurrIndex(0);
            authInfoQueryRequest.setPageSize(10);
            authInfoQueryRequest.setEndTime(new Date(Long.parseLong("1582008438000")));
            authInfoQueryRequest.setStartTime(new Date(Long.parseLong("1582008431000")));
            authInfoQueryRequest.setBizAppId("3438757553");
            responseRpcOutput = infoAuthQuickSignQueryService.authInfoRecordQueryOrgInfo(authInfoQueryRequest);

        } catch (Exception e) {
            e.printStackTrace();
        }
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }

    @Test
    public void authInfoRecordOrgFrsignTest() {
        AuthInfoQueryRequest authInfoQueryRequest = new AuthInfoQueryRequest();
        RpcOutput<QuickSignAuthInfoQueryResponse> responseRpcOutput = null;
        try {

            authInfoQueryRequest.setCodeUSC("92222403MA14UDL80B");
            authInfoQueryRequest.setCodeORG("92222403MA14UDL80B");
            authInfoQueryRequest.setCodeREG("92222403MA14UDL80B");
            authInfoQueryRequest.setCurrIndex(0);
            authInfoQueryRequest.setPageSize(10);
            authInfoQueryRequest.setEndTime(new Date(Long.parseLong("1564140166000")));
            authInfoQueryRequest.setStartTime(new Date(Long.parseLong("1564140161000")));
            authInfoQueryRequest.setBizAppId("3438756698");
            responseRpcOutput = infoAuthQuickSignQueryService.authInfoRecordQueryOrgInfo(authInfoQueryRequest);

        } catch (Exception e) {
            e.printStackTrace();
        }
        Assert.assertNotNull(responseRpcOutput);
        Assert.assertTrue(responseRpcOutput.isSuccess());
    }
}
