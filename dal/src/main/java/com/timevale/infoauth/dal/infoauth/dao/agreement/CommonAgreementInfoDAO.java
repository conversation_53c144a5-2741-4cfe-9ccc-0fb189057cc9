package com.timevale.infoauth.dal.infoauth.dao.agreement;

import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementInfoDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 通用协议信息DB访问DAO
 *
 * <AUTHOR> Kunpeng
 */
@Repository
public class CommonAgreementInfoDAO  {

    @Autowired
    private CommonAgreementInfoMapper infoMapper;

    /**
     * 插入一个通用协议信息对象到数据库中
     *
     * @param infoDO 通用协议信息对象，包含要插入的协议信息
     * @return 如果插入成功返回true，否则返回false
     */
    public boolean save(CommonAgreementInfoDO infoDO){
        return infoMapper.insert(infoDO) > 0;
    }

    /**
     * 根据ID更新通用协议信息
     *
     * @param infoDO 包含更新信息的通用协议信息对象
     * @return 总是返回true，表示更新操作执行完毕
     */
    public boolean updateById(CommonAgreementInfoDO infoDO){
          infoMapper.updateById(infoDO);
          return Boolean.TRUE;
    }

    /**
     * 根据协议ID获取未删除的通用协议信息
     *
     * @param agreementId 协议ID，用于查询特定的协议信息
     * @return 返回查询到的通用协议信息对象，如果没有找到则返回null
     */
    public CommonAgreementInfoDO getOneAndNotDeleted(String agreementId){
        return infoMapper.getByAgreementId(agreementId);
    }
}
