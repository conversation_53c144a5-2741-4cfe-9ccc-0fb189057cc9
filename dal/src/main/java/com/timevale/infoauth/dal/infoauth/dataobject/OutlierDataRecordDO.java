package com.timevale.infoauth.dal.infoauth.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OutlierDataRecordDO  extends InfoServiceAuthDO{
  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;
  /** id 自增主键. */
  private Long id;
  /** infoauthId 业务主键. */
  private String infoauthId;
  /** @See com.timevale.infoauth.service.enums.InfoAuthServiceType */
  private Integer type;
  /** 操作类型:A-add;U-update;D-delete */
  private String eventType;
  /** 操作异常数据 */
  private String requestDate;
  /** 状态0-未补偿;1-已经补偿 */
  private Integer status;

}
