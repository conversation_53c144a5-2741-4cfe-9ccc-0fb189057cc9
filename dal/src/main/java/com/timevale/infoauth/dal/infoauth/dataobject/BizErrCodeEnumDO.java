package com.timevale.infoauth.dal.infoauth.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 信息比对业务错误码枚举
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BizErrCodeEnumDO{

    /**
     * createTime 记录创建时间.
     */
    private Date createTime;
    /**
     * modifyTime 记录更新时间.
     */
    private Date modifyTime;
    /**
     * id 自增主键.
     */
    private Long id;
    /**
     * msg 错误信息.
     */
    private String msg;
    /**
     * code 错误码.
     */
    private String code;
    /**
     * extend 扩展字段.
     */
    private String extend;
    /**
     * bizCodeId 业务主键.
     */
    private String bizCodeId;
}
