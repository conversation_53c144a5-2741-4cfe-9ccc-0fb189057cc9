package com.timevale.infoauth.service.api;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.enums.ProvResult;
import com.timevale.infoauth.service.impl.common.dto.Provout;
import com.timevale.infoauth.service.impl.config.AutoConfig;
import com.timevale.infoauth.service.impl.dto.TelecomauthProvin;
import com.timevale.infoauth.service.impl.provider.manager.retry.breaker.actuator.impl.DataSourceNotFoundBreakerActuator;
import com.timevale.infoauth.service.impl.provider.manager.retry.breaker.actuator.impl.DefaultBreakerActuator;
import com.timevale.infoauth.service.impl.provider.manager.retry.breaker.actuator.impl.ErrorCodeBreakerActuator;
import com.timevale.infoauth.service.impl.provider.manager.retry.breaker.actuator.impl.TimeoutBreakerActuator;
import com.timevale.infoauth.service.impl.provider.manager.retry.breaker.support.BusWrapper;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import org.testng.Assert;
import org.testng.annotations.Test;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
@JsonDataIgnore
public class CircuitBreakerTest extends ApplicationTest {

  @Test
  public void timeoutBreakerActuatorTest()
      throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    long threshold = AutoConfig.actuatorTimeoutAllowThreshold;
    AutoConfig.actuatorTimeoutAllowThreshold = 0;
    TimeoutBreakerActuator timeoutBreakerActuator = new TimeoutBreakerActuator();
    TelecomauthProvin input = new TelecomauthProvin();
    input.setBizAppId("**********");
    input.setProviderName("JINGZHONG");
    input.setName("黄珂");
    input.setMobile("13032956508");
    input.setCertNumber("610112199309060526");
    BusWrapper busWrapper = new BusWrapper(input);
    Method checkCurrentProviderAlive =
        timeoutBreakerActuator
            .getClass()
            .getDeclaredMethod("checkCurrentProviderAlive", BusWrapper.class);
    checkCurrentProviderAlive.setAccessible(true);
    Assert.assertThrows(() -> checkCurrentProviderAlive.invoke(timeoutBreakerActuator, busWrapper));

    busWrapper.setProvout(Provout.builder().provResult(ProvResult.TIMEOUT).build());
    Method checkProvoutMatched =
        timeoutBreakerActuator
            .getClass()
            .getDeclaredMethod("checkProvoutMatched", BusWrapper.class);
    checkProvoutMatched.setAccessible(true);
    Assert.assertThrows(() -> checkProvoutMatched.invoke(timeoutBreakerActuator, busWrapper));

    Method switchableProviders =
        timeoutBreakerActuator
            .getClass()
            .getDeclaredMethod("switchableProviders", BusWrapper.class);
    switchableProviders.setAccessible(true);
    Assert.assertNotNull(switchableProviders.invoke(timeoutBreakerActuator, busWrapper));

    AutoConfig.actuatorTimeoutAllowThreshold = threshold;
  }

  @Test
  public void DataSourceNotFoundBreakerActuatorTest()
      throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    DataSourceNotFoundBreakerActuator dataSourceNotFoundBreakerActuator =
        new DataSourceNotFoundBreakerActuator();
    TelecomauthProvin input = new TelecomauthProvin();
    input.setBizAppId("**********");
    input.setProviderName("JINGZHONG");
    input.setName("黄珂");
    input.setMobile("13032956508");
    input.setCertNumber("610112199309060526");
    BusWrapper busWrapper = new BusWrapper(input);

    busWrapper.setProvout(Provout.builder().rawErrorCode("503").build());
    Method checkProvoutMatched =
        dataSourceNotFoundBreakerActuator
            .getClass()
            .getDeclaredMethod("checkProvoutMatched", BusWrapper.class);
    checkProvoutMatched.setAccessible(true);
    Assert.assertThrows(
        () -> checkProvoutMatched.invoke(dataSourceNotFoundBreakerActuator, busWrapper));

    Method switchableProviders =
        dataSourceNotFoundBreakerActuator
            .getClass()
            .getDeclaredMethod("switchableProviders", BusWrapper.class);
    switchableProviders.setAccessible(true);
    Assert.assertNotNull(switchableProviders.invoke(dataSourceNotFoundBreakerActuator, busWrapper));

    Method checkCurrentProviderAlive =
        dataSourceNotFoundBreakerActuator
            .getClass()
            .getDeclaredMethod("checkCurrentProviderAlive", BusWrapper.class);
    checkCurrentProviderAlive.setAccessible(true);
    Assert.assertThrows(
        () -> checkCurrentProviderAlive.invoke(dataSourceNotFoundBreakerActuator, busWrapper));
  }

  @Test
  public void ErrorCodeBreakerActuatorTest()
      throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    ErrorCodeBreakerActuator errorCodeBreakerActuator = new ErrorCodeBreakerActuator();
    TelecomauthProvin input = new TelecomauthProvin();
    input.setBizAppId("**********");
    input.setProviderName("JINGZHONG");
    input.setName("黄珂");
    input.setMobile("13032956508");
    input.setCertNumber("610112199309060526");
    BusWrapper busWrapper = new BusWrapper(input);

    busWrapper.setProvout(Provout.builder().rawErrorCode("400").build());
    Method checkProvoutMatched =
        errorCodeBreakerActuator
            .getClass()
            .getDeclaredMethod("checkProvoutMatched", BusWrapper.class);
    checkProvoutMatched.setAccessible(true);
    Assert.assertThrows(() -> checkProvoutMatched.invoke(errorCodeBreakerActuator, busWrapper));

    Method switchableProviders =
        errorCodeBreakerActuator
            .getClass()
            .getDeclaredMethod("switchableProviders", BusWrapper.class);
    switchableProviders.setAccessible(true);
    Assert.assertNotNull(switchableProviders.invoke(errorCodeBreakerActuator, busWrapper));

    Method checkCurrentProviderAlive =
        errorCodeBreakerActuator
            .getClass()
            .getDeclaredMethod("checkCurrentProviderAlive", BusWrapper.class);
    checkCurrentProviderAlive.setAccessible(true);
    checkCurrentProviderAlive.invoke(errorCodeBreakerActuator, busWrapper);
  }

  @Test
  public void DefaultBreakerActuatorTest()
      throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    DefaultBreakerActuator defaultBreakerActuator = new DefaultBreakerActuator();
    TelecomauthProvin input = new TelecomauthProvin();
    input.setBizAppId("**********");
    input.setProviderName("JINGZHONG");
    input.setName("黄珂");
    input.setMobile("13032956508");
    input.setCertNumber("610112199309060526");
    BusWrapper busWrapper = new BusWrapper(input);

    Method checkProvoutMatched =
        defaultBreakerActuator
            .getClass()
            .getDeclaredMethod("checkProvoutMatched", BusWrapper.class);
    checkProvoutMatched.setAccessible(true);
    checkProvoutMatched.invoke(defaultBreakerActuator, busWrapper);
    Assert.assertTrue(true);

    Method switchableProviders =
        defaultBreakerActuator
            .getClass()
            .getDeclaredMethod("switchableProviders", BusWrapper.class);
    switchableProviders.setAccessible(true);
    switchableProviders.invoke(defaultBreakerActuator, busWrapper);
    Assert.assertTrue(true);

    Method checkCurrentProviderAlive =
        defaultBreakerActuator
            .getClass()
            .getDeclaredMethod("checkCurrentProviderAlive", BusWrapper.class);
    checkCurrentProviderAlive.setAccessible(true);
    checkCurrentProviderAlive.invoke(defaultBreakerActuator, busWrapper);
    Assert.assertTrue(true);
  }

//  @Test
//  public void callResultSupportClearFailedMark() {
//    PersonProvin personProvin = new PersonProvin();
//    personProvin.setName("纳什均衡");
//    personProvin.setCertNumber("******************");
//    CallResultSupport.clearFailedMark(InfoAuthServiceType.IDCARD_2_ELEMENT, personProvin);
//    Assert.assertEquals(personProvin.getCertNumber(), "******************");
//    Assert.assertTrue(true);
//  }
}
