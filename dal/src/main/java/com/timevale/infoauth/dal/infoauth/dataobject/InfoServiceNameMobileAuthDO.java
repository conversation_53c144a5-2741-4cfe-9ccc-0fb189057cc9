package com.timevale.infoauth.dal.infoauth.dataobject;

import lombok.Data;

import java.util.Date;

/**
 * The table.info_service_name_mobile_auth
 */
@Data
public class InfoServiceNameMobileAuthDO extends InfoServiceAuthDO {

  /**
   * id 主键.
   */
  private Long id;

  /**
   * name 待检查个人姓名.
   */
  private String name;

  /**
   * mobile 待检查个人手机号.
   */
  private String mobile;

  /**
   * providerId 企业查询服务提供商.
   */
  private String providerId;

  /**
   * providerResult 服务提供商返回的代码.
   */
  private String providerResult;

  /**
   * result 比对结果.
   */
  private Integer result;

  /**
   * serviceStatus SERVICE_STATUS.
   */
  private Integer serviceStatus;

  /**
   * timestampId 时间戳.
   */
  private String timestampId;

  /**
   * createTime 记录创建时间.
   */
  private Date createTime;

  /**
   * modifyTime 记录修改时间.
   */
  private Date modifyTime;

  /**
   * errId
   */
  private Long errId;
}