package com.timevale.infoauth.service.request.query;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class AuthInfoRecordQuery extends ToString {
  /**
   * 信息比对id
   * */
  String infoAuthId;
  /**
   * 信息比对类型 InfoAuthServiceType infoAuthServiceType
   * */
  Integer infoAuthServiceType;
  /**
   * appId
   * */
  String appId;
  /**
   * 证件号
   * */
  String certNo;
  /**
   * 证件类型 CertTypeEnum
   * */
  int certType;
  /**
   * 查询类型: org(企业)/psn(个人)
   * */
  String queryType;
  /**
   * 名称
   * */
  String certName;
  /**
   * 核验状态: ServiceStatus
   * */
  Integer serviceStatus;
  /**
   * 开始日期
   * */
  Date startTime;
  /**
   * 结束日期
   * */
  Date endTime;
  /**
   * 分页信息
   * */
  int pageIndex, pageSize;

}
