package com.timevale.infoauth.service;

import com.timevale.mandarin.common.idgenerator.GlobalIdGenerator;
import com.timevale.mandarin.weaver.utils.RequestContext;

import java.util.HashMap;
import java.util.Map;

public class HeaderUtils {
  public static final String APP_ID = "3876543983";

  public static void setHeader() {
    Map<String, Object> contextMap = new HashMap<>();
    contextMap.put("X-Tsign-Open-App-Id", APP_ID);
    contextMap.put("trace-id", GlobalIdGenerator.generate().toString());
    RequestContext.setContextMap(contextMap);
  }
}
