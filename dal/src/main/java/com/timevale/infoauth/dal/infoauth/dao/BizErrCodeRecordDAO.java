package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.BizErrCodeRecordDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;

/** 错误原因流水表（固化错误原因） */
public interface BizErrCodeRecordDAO {

  @Insert({
    "<script>",
    "INSERT INTO `biz_err_code_record` (`infoauth_id`, `biz_type`, `provider`, `track_id`",
    "<if test=\"pCode != null and pCode != '' \"> <![CDATA[, p_code]]> </if> ",
    "<if test=\"pMsg != null and pMsg != '' \"> <![CDATA[, p_msg]]> </if> ",
    "<if test=\"code != null and code != '' \"> <![CDATA[, code]]> </if> ",
    "<if test=\"msg != null and msg != '' \"> <![CDATA[, msg]]> </if> ",
    "<if test=\"extend != null and extend != '' \"> <![CDATA[, extend]]> </if> ",
    ") VALUES (#{infoauthId}, #{bizType}, #{provider}, #{trackId}",
    "<if test=\"pCode != null and pCode != '' \"> <![CDATA[, #{pCode}]]> </if> ",
    "<if test=\"pMsg != null and pMsg != '' \"> <![CDATA[, #{pMsg}]]> </if> ",
    "<if test=\"code != null and code != '' \"> <![CDATA[, #{code}]]> </if> ",
    "<if test=\"msg != null and msg != '' \"> <![CDATA[, #{msg}]]> </if> ",
    "<if test=\"extend != null and extend != '' \"> <![CDATA[, #{extend}]]> </if> ",
    ")",
    "</script>"
  })
  @Options(useGeneratedKeys = true, keyColumn = "id")
  int insert(BizErrCodeRecordDO entity);
}
