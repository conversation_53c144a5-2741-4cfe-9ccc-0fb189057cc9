package com.timevale.infoauth.dal.infoauth.enums;

/** @Author: <PERSON> @Description: 认证状态枚举 @Date: Created in 2018/7/17 @Modified By: */
public enum ServiceStatus {

  /** 认证中 */
  PROCESSING(1),

  /** 认证成功 */
  FINISHED(2),

  /** 认证失败 */
  FAILAED(3),

  /** 认证异常 */
  EXCEPTION(4),
  ;

  public int dbValue() {
    return dbValue;
  }

  ServiceStatus(int dbValue) {
    this.dbValue = dbValue;
  }

  private int dbValue;
}
