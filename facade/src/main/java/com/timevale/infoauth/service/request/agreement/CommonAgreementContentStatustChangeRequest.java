package com.timevale.infoauth.service.request.agreement;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 通用协议状态修改
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommonAgreementContentStatustChangeRequest extends CommonAgreementContentBaseRequest {




  /**
   * 发布状态。0=待发布 1=发布成功 2=作废.
   * @see com.timevale.infoauth.service.enums.agreement.CommonAgreementReleaseStatusEnum
   * */
  private Integer enableStatus;

  /** 操作人. */
  private String operator;

}
