package com.timevale.infoauth.service.api;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.impl.monitor.spi.loader.MonitorExtensionLoader;
import com.timevale.infoauth.service.impl.monitor.spi.statistics.Counter;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
@JsonDataIgnore
public class MonitorExtensionLoaderTest extends ApplicationTest {

  @Test
  public void testShuheTelecomAdaptors() {
    String metric = "infoauth:年-月-日-时-分";
    Counter defaultExtension =
        MonitorExtensionLoader.getExtensionLoader(Counter.class).getDefaultExtension();
    defaultExtension.incrementAndGet(metric);
    Assert.assertTrue(true);

    Counter logCounter =
        MonitorExtensionLoader.getExtensionLoader(Counter.class).getExtension("logCounter");
    logCounter.incrementAndGet(metric);
    Assert.assertTrue(true);
  }
}
