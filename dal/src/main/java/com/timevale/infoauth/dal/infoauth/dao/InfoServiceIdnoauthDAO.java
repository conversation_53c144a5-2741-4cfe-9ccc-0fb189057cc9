package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceIdnoauthDO;
import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceIdnoauthExtendDO;
import com.timevale.infoauth.dal.infoauth.facade.AuthInfoQueryRequestDTO;
import com.timevale.infoauth.dal.infoauth.facade.AuthResultQueryRecordDTO;
import org.apache.ibatis.annotations.*;

import java.sql.Timestamp;
import java.util.List;

/**
 * The Table info_service_idnoAuth. info_service_idnoAuth
 *
 * <AUTHOR> Kunpeng
 */
public interface InfoServiceIdnoauthDAO {

  /**
   * desc:插入表:info_service_idnoAuth.<br>
   *
   * @param entity entity
   * @return int
   */
  @Insert(
      "INSERT INTO `info_service_idnoAuth` "
          + "(`id`, `infoauth_id`, `name`, `idno`, `provider_id`, `provider_result`, `result`, `service_status`, `timestamp_id`, `create_time`, `modify_time`) "
          + "VALUES (#{id}, #{infoauthId}, #{name}, #{idno}, #{providerId}, #{providerResult}, #{result}, #{serviceStatus}, #{timestampId}, now(), now())")
  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  int insert(InfoServiceIdnoauthDO entity);

  /**
   * desc:批量插入表:info_service_idnoAuth.<br>
   *
   * @param list list
   * @return int
   */
  int insertBatch(List<InfoServiceIdnoauthDO> list);

  /**
   * desc:根据主键删除数据:info_service_idnoAuth.<br>
   *
   * @param id id
   * @return int
   */
  int deleteById(String id);

  /**
   * desc:根据主键获取数据:info_service_idnoAuth.<br>
   *
   * @param id id
   * @return InfoServiceIdnoauthDO
   */
  InfoServiceIdnoauthDO getById(String id);

  @Update(
      "update info_service_idnoAuth set `provider_id` = #{providerId}, `provider_result` = #{providerResult}, `result` = #{result}, `service_status` = #{serviceStatus},`modify_time` = now()"
          + " where id = #{id}")
  int updateResult(InfoServiceIdnoauthDO infoServiceIdnoauthDO);

  @Select("select * from info_service_idnoAuth where infoauth_id = #{infoAuthId}")
  InfoServiceIdnoauthDO getByInfoAuthId(@Param("infoAuthId") String infoAuthId);

  @Select("select * from info_service_idnoAuth where idno = #{idno} and modify_time<=#{modifyTime} order by id desc limit #{currIndex},#{pageSize}")
  List<InfoServiceIdnoauthDO> getByUpdateTimeAndIdNo(@Param("idno") String idno, @Param("modifyTime") Timestamp modifyTime, @Param("currIndex") Integer currIndex, @Param("pageSize") Integer pageSize);

  /**
   * desc:专为快捷签出证查询使用
   *
   * @param authInfoQueryRequestDTO
   * @return List<InfoServiceIdnoauthDO>
   */
  @Select({
          "<script>",
          "select * from info_service_idnoAuth t1 inner join info_service t2 on t1.infoauth_id = t2.infoauth_id",
          "where  1=1 ",
          "<if test='infoAuthId != null'> and t1.infoauth_id = #{infoAuthId} </if>",
          "<if test='appId != null'> and t2.app_id= #{appId} </if>",
          "<if test='idno != null'> and t1.idno = #{idno} </if>",
          "<if test='serviceStatus != null'> and t1.service_status = #{serviceStatus} </if>",
          "<if test='updateTime != null'> and t1.modify_time between 0 and #{updateTime} </if>",
//          "<if test='modifyTime != null'> and modify_time &lt;= #{modifyTime} </if>",
          "order by t1.modify_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<InfoServiceIdnoauthExtendDO> queryByUpdateTimeAndIdNo(AuthInfoQueryRequestDTO authInfoQueryRequestDTO);

  /**
   * desc: 专为快捷签出证查询List使用
   * @param authInfoQueryRequestDTO 查询条件
   * @return 个人认证二要素记录
   */
  @Select({
          "<script>",
          "select * from info_service_idnoAuth t1 inner join info_service t2 on t1.infoauth_id = t2.infoauth_id",
          "where  1=1 ",
          "<if test='infoAuthId != null'> and t1.infoauth_id = #{infoAuthId} </if>",
          "<if test='appId != null'> and t2.app_id= #{appId} </if>",
          "<if test='idno != null'> and t1.idno = #{idno} </if>",
          "<if test='serviceStatus != null'> and t1.service_status = #{serviceStatus} </if>",
          "<if test='modifyTime != null'> and t1.modify_time between 0 and #{modifyTime} </if>",
          "order by t1.modify_time desc limit #{currIndex},#{pageSize}",
          "</script>"
  })
  List<InfoServiceIdnoauthExtendDO> queryListByUpDateTimeAndIdNO(AuthInfoQueryRequestDTO authInfoQueryRequestDTO);

  /**
   * desc:专为信息比对记录查询使用
   *
   * @param authInfoQueryRequestDTO
   * @return List<InfoServiceIdnoauthDO>
   */
  @Select({
          "<script>",
          "select * from info_service_idnoAuth t1 inner join info_service t2 on t1.infoauth_id = t2.infoauth_id",
          "where  1=1 ",
          "<if test='infoAuthId != null'> and t1.infoauth_id = #{infoAuthId} </if>",
          "<if test='appId != null'> and t2.app_id= #{appId} </if>",
          "<if test='idno != null'> and t1.idno = #{idno} </if>",
          "<if test='serviceStatus != null'> and t1.service_status = #{serviceStatus} </if>",
          "<if test='startTime != null'> and t1.modify_time between #{startTime} and #{endTime} </if>",
          "order by t1.modify_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<InfoServiceIdnoauthExtendDO> queryForInfoAuthRecord(AuthInfoQueryRequestDTO authInfoQueryRequestDTO);

  @Update(
          "UPDATE `info_service_idnoAuth` SET err_id = #{errId} WHERE `infoauth_id` = #{infoauthId}")
  void bindErrId(@Param("infoauthId") String infoauthId, @Param("errId") Long errId);

  /**
   * desc:根据条件查询企业信息比对记录
   *
   * @return InfoServiceDO
   */
  @Select({
          "<script>",
          "select * from info_service t1 inner join info_service_idnoAuth t2 on t1.infoauth_id = t2.infoauth_id",
          "where  1=1 ",
          "<if test='certName != null'> and t2.name = #{certName} </if>",
          "<if test='serviceStatus != null'> and t2.service_status = #{serviceStatus} </if>",
          "<if test='certNo != null'> and t2.idno = #{certNo} </if>",
          "<if test='infoAuthServiceType != null'> and t1.type = #{infoAuthServiceType} </if>",
          "<if test='appId != null'> AND t1.app_id = #{appId} </if>",
          "<if test='startTime != null'> AND t1.modify_time between #{startTime} and #{endTime} </if>",
          "<if test='infoAuthId != null'> AND t1.infoauth_id = #{infoAuthId} </if>",
          "order by t1.modify_time desc limit #{pageIndex},#{pageSize}",
          "</script>"})
  List<InfoServiceIdnoauthDO> getIdnoAuthByAuthResultQuery(AuthResultQueryRecordDTO authResultQueryRecordDTO);

  /**
   * desc:根据条件查询企业信息比对记录
   *
   * @return InfoServiceDO
   */
  @Select({
          "<script>",
          "select count(*) from info_service t1 force INDEX(idx_app_id) inner join info_service_idnoAuth t2 on t1.infoauth_id = t2.infoauth_id",
          "where  1=1 ",
          "<if test='appId != null'> AND t1.app_id = #{appId} </if>",
          "<if test='startTime != null'> AND t1.modify_time between #{startTime} and #{endTime} </if>",
          "<if test='certName != null'> and t2.name = #{certName} </if>",
          "<if test='serviceStatus != null'> and t2.service_status = #{serviceStatus} </if>",
          "<if test='certNo != null'> and t2.idno = #{certNo} </if>",
          "<if test='infoAuthServiceType != null'> and t1.type = #{infoAuthServiceType} </if>",
          "<if test='infoAuthId != null'> AND t1.infoauth_id = #{infoAuthId} </if>",
          "</script>"})
  int getIdnoAuthByAuthResultQueryCount(AuthResultQueryRecordDTO authResultQueryRecordDTO);
}
