package com.timevale.infoauth.dal.infoauth.dataobject;

import lombok.Data;

import java.util.Date;

@Data
public class InfoServiceFaceDO {
  private Integer id;
  private String infoauthId;
  private String name;
  private String idno;
  private String selfKey;
  private String captureFacePackageKey;
  private String idcardFacePackageKey;
  private String pizId;
  private String providerId;
  private Date createTime;
  private Date modifyTime;
  private Integer compareFailure;
  private Integer result;
  private Integer serviceStatus;
}
