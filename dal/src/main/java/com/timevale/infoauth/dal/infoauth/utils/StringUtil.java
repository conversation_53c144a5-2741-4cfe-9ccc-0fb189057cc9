package com.timevale.infoauth.dal.infoauth.utils;

import com.timevale.mandarin.base.util.StringUtils;

public class StringUtil {

  public static String truncation(String name, int length) {
    if (StringUtils.isNotBlank(name)) {
      return name.substring(0, Math.min(length, name.length()));
    }
    return name;
  }

  public static String format(String sourceText, Object ... var1){
    return String.format(sourceText, var1);
  }


}
