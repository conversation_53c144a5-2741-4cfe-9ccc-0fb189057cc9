```markdown
# 运营商三要素校验技术规范文档 v1.0

## 1.1 文档简介

本文档是运营商三要素校验服务接口规范，主要为该服务的需求客户描述接口的连接方式、通讯协议、报文格式等技术约定，以及相关的数据产品定义和使用说明。

## 1.2 接口详情

- **请求地址**: `https://123.56.221.164/api/operatorverify`
- **请求方法**: `POST`
- **请求及响应报文类型**: `application/json`

## 1.3 请求参数

| 参数名  | 类型   | 是否必填 | 参数说明 |
|---------|--------|----------|----------|
| APPID   | string | 是       | 12 位账号（平台发放） |
| type    | string | 是       | 请求类型，详见 1.5 请求类型 |
| name    | string | 是       | 姓名 |
| idcard  | string | 是       | 身份证号 |
| mobile  | string | 是       | 手机号 |

## 1.4 请求示例

```
json
{
"APPID": "************",
"type": "0501",
"name": "张三",
"idcard": "350011************",
"mobile": "138********"
}
```
## 1.5 请求类型

| 请求类型 | 类型编码 | 校验要素 |
|----------|----------|----------|
| 三网合一 | 0501     | 姓名 + 身份证号 + 手机号 |

## 1.6 响应报文参数

| 参数名   | 类型   | 参数说明 |
|----------|--------|----------|
| request_id | string | 请求流水号 |
| status  | string | 状态码 |
| message | string | 响应状态描述 |
| data    | object | 响应内容 |
| operatorType | string | 运营商类型，如有返回值则按此运营商类型计费 |

## 1.7 响应报文示例

```
json
{
"request_id": "请求流水号",
"status": "200",
"message": "信息一致",
"data": {
"operatorType": "CMCC"
}
}
```
## 1.8 响应状态说明

| 状态码 | 状态描述 | 是否计费 |
|--------|----------|----------|
| 200    | 信息一致 | 是       |
| 201    | 信息不一致 | 是       |
| 202    | 无法验证 | 否       |
| 203    | 查无信息 | 否       |
| 204    | 不支持的手机号段验证 | 否       |
| 100    | 参数不正确或不规范 | 否       |
| 300    | 系统繁忙，请稍后重试 | 否       |
| 400    | AppID 无效或服务未开通 | 否       |
| 401    | IP 未报备白名单或签名失败 | 否       |
| 402    | 账户授信额度已用尽或服务已过期 | 否       |
| 403    | 交易限流或查询过于频繁，请稍后重试 | 否       |
| 404    | 当日交易总量已达上限，于次日 0 点恢复 | 否       |

## 1.9 运营商类型

| 运营商编码 | 运营商名称 |
|------------|------------|
| CMCC       | 移动       |
| CUCC       | 联通       |
| CTCC       | 电信       |

希望这份 `README.md` 文件能帮助您更好地理解和使用运营商三要素校验服务。如果有任何问题或建议，请随时联系我们。
```
