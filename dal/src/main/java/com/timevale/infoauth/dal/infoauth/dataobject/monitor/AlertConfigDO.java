package com.timevale.infoauth.dal.infoauth.dataobject.monitor;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * The table. 告警配置
 *
 * <AUTHOR> Kunpeng
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertConfigDO extends ToString {

  /** 自增主键. */
  private Long id;
  /** 告警配置ID. */
  private String alertId;
  /** 告警目标类型[1供应商,2空间] */
  private String targetType;
  /** 告警目标ID[供应商ID,空间ID] */
  private String targetId;
  /** 告警表达式. */
  private String expression;
  /** 扩展字段. */
  private String extend;
  private String desc;
  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;
}
