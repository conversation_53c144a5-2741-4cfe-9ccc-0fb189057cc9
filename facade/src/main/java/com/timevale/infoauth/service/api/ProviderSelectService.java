package com.timevale.infoauth.service.api;

import com.timevale.infoauth.service.dto.PSelectDTO;
import com.timevale.mandarin.common.annotation.RestClient;

import java.util.List;

/**
 * 应用层服务（选举子域 - 支撑域）
 *
 * <AUTHOR> href="mailto:<EMAIL>">jiuchen</a>
 */
@RestClient(serviceId = "infoauth-service")
public interface ProviderSelectService {

  /**
   * 保存选举子域
   *
   * @param pSelectDTO
   */
  void save(PSelectDTO pSelectDTO);

  /**
   * 查询一条选举子域
   *
   * @param managerId
   * @return
   */
  PSelectDTO getSelectById(Integer managerId);

  /**
   * 查询所有选举子域
   *
   * @return
   */
  List<PSelectDTO> querySelectAll();
}
