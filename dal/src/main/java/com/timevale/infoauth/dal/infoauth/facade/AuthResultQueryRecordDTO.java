package com.timevale.infoauth.dal.infoauth.facade;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class AuthResultQueryRecordDTO {
  /**
   * 信息比对id
   * */
  String infoAuthId;
  /**
   * 信息比对类型 InfoAuthServiceType infoAuthServiceType
   * */
  Integer infoAuthServiceType;
  /**
   * appId
   * */
  String appId;
  /**
   * 证件号
   * */
  String certNo;
  /**
   * 证件类型 CertTypeEnum
   * */
  int certType;
  /**
   * 查询类型: org(企业)/psn(个人)
   * */
  String queryType;
  /**
   * 名称
   * */
  String certName;
  /**
   * 核验状态: ServiceStatus
   * */
  Integer serviceStatus;
  /**
   * 开始日期
   * */
  Date startTime;
  /**
   * 结束日期
   * */
  Date endTime;

  /**
   * 开始id
   * */
  private Long startId;
  /**
   * 结束id
   * */
  private Long endId;

  /**
   * 分页信息
   * */
  int pageIndex, pageSize;
}
