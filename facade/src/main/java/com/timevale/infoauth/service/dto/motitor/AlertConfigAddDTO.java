package com.timevale.infoauth.service.dto.motitor;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/7/17 21:15
 */
@Data
public class AlertConfigAddDTO extends ToString {

    /** 告警配置ID. */
    private String alertId;
    /** 告警目标类型[1供应商,2空间] */
    private String targetType;
    /** 告警目标ID[供应商ID,空间ID] */
    private String targetId;
    /** 告警表达式. */
    private String expression;
    /** 扩展字段. */
    private String extend;
    private String desc;
}
