<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="biz_err_code_record" physicalName="biz_err_code_record" remark="错误原因流水表（固化错误原因）">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,MSG,CODE,P_MSG,P_CODE,EXTEND,TRACK_ID,PROVIDER,INFOAUTH_ID,BIZ_TYPE,CREATE_TIME,MODIFY_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.MSG,sf.CODE,sf.P_MSG,sf.P_CODE,sf.EXTEND,sf.TRACK_ID,sf.PROVIDER,sf.INFOAUTH_ID,sf.BIZ_TYPE,sf.CREATE_TIME,sf.MODIFY_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:biz_err_code_record">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO biz_err_code_record(
            ID
            ,MSG
            ,CODE
            ,P_MSG
            ,P_CODE
            ,EXTEND
            ,TRACK_ID
            ,PROVIDER
            ,INFOAUTH_ID
            ,BIZ_TYPE
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES(
             null
            , #{msg,jdbcType=VARCHAR}
            , #{code,jdbcType=VARCHAR}
            , #{pMsg,jdbcType=VARCHAR}
            , #{pCode,jdbcType=VARCHAR}
            , #{extend,jdbcType=VARCHAR}
            , #{trackId,jdbcType=VARCHAR}
            , #{provider,jdbcType=VARCHAR}
            , #{infoauthId,jdbcType=VARCHAR}
            , #{bizType,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{modifyTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:biz_err_code_record">
        INSERT INTO biz_err_code_record(
            ID
            ,MSG
            ,CODE
            ,P_MSG
            ,P_CODE
            ,EXTEND
            ,TRACK_ID
            ,PROVIDER
            ,INFOAUTH_ID
            ,BIZ_TYPE
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.msg,jdbcType=VARCHAR}
                , #{item.code,jdbcType=VARCHAR}
                , #{item.pMsg,jdbcType=VARCHAR}
                , #{item.pCode,jdbcType=VARCHAR}
                , #{item.extend,jdbcType=VARCHAR}
                , #{item.trackId,jdbcType=VARCHAR}
                , #{item.provider,jdbcType=VARCHAR}
                , #{item.infoauthId,jdbcType=VARCHAR}
                , #{item.bizType,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.modifyTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:biz_err_code_record">
        <![CDATA[
        UPDATE biz_err_code_record
        SET
            MSG             = #{msg,jdbcType=VARCHAR}
            ,CODE            = #{code,jdbcType=VARCHAR}
            ,P_MSG           = #{pMsg,jdbcType=VARCHAR}
            ,P_CODE          = #{pCode,jdbcType=VARCHAR}
            ,EXTEND          = #{extend,jdbcType=VARCHAR}
            ,TRACK_ID        = #{trackId,jdbcType=VARCHAR}
            ,PROVIDER        = #{provider,jdbcType=VARCHAR}
            ,INFOAUTH_ID     = #{infoauthId,jdbcType=VARCHAR}
            ,BIZ_TYPE        = #{bizType,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,MODIFY_TIME     = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:biz_err_code_record">
        <![CDATA[
        DELETE FROM biz_err_code_record
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:biz_err_code_record">
        SELECT *
        FROM biz_err_code_record
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxInfoauthId" multiplicity="many" remark="根据普通索引IdxInfoauthId获取数据:biz_err_code_record">
        SELECT *
        FROM biz_err_code_record
        WHERE
        <![CDATA[
            INFOAUTH_ID     = #{infoauthId,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxTrackId" multiplicity="many" remark="根据普通索引IdxTrackId获取数据:biz_err_code_record">
        SELECT *
        FROM biz_err_code_record
        WHERE
        <![CDATA[
            TRACK_ID        = #{trackId,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>
