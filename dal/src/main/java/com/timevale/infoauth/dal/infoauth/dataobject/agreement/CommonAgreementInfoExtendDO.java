package com.timevale.infoauth.dal.infoauth.dataobject.agreement;

import com.timevale.infoauth.service.enums.agreement.CommonAgreementLangEnum;
import lombok.Data;

import java.util.Date;


/**
 * 通用协议管理-协议信息主表
 * <AUTHOR>
 */
@Data
public class CommonAgreementInfoExtendDO extends CommonAgreementInfoDO{


  /** 协议发布记录ID. */
  private String releaseRecordId;

  /** 发布生效时间 */
  private Date releaseTime;

  /** 是否需要填充，0=不需要，1=需要 */
  private int contentFill;

  /**
   * 通用协议语言,默认显示中文
   * @see  CommonAgreementLangEnum
   * */
  private String lang;

  /** 协议简称.｜｜ 协议名称 */
  private String shortName;

  /** 协议全称. */
  private String fullName;

}
