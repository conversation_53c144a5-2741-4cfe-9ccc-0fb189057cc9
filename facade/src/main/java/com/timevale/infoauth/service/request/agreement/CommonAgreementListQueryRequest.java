package com.timevale.infoauth.service.request.agreement;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;


/**
 * 通用协议信息列表查询
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CommonAgreementListQueryRequest extends ToString {


    /**
     * 页面显示条数，默认10
   */
  @NotBlank
  private Integer pageSize;


    /**
     * 页面序号，默认1
   */
  private Integer pageIndex;

  public void format(){
      if(pageIndex == null){
           pageIndex = 1;
      }
      if(pageSize == null){
          pageSize = 20;
      }
  }
}
