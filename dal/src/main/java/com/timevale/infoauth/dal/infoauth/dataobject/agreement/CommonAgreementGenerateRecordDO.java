package com.timevale.infoauth.dal.infoauth.dataobject.agreement;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 通用协议管理-协议内部生成记录表
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
//@TableName("common_agreement_generate_record")
public class CommonAgreementGenerateRecordDO extends BaseDO{

  /** id 主键. */
//  @TableId(
//          type = IdType.AUTO
//  )
  private Long id;


  /** 生成协议记录ID. */
  private String generateRecordId;

  /** 协议发布记录ID. */
  private String releaseRecordId;

  /** 协议内容填充信息. */
  private String contentFillParam;

  /** 预留扩展. */
  private String extend;

  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;
}
