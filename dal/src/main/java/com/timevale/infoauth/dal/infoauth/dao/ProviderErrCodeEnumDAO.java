package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.ProviderErrCodeEnumDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;

/** 供应商错误码枚举 */
public interface ProviderErrCodeEnumDAO {

  @Insert({
    "<script>",
    "INSERT INTO `provider_err_code_enum` (`p_code_id`, `biz_type`, `provider`, `p_code`, `biz_md5`",
    "<if test=\"pMsg != null and pMsg != '' \"> <![CDATA[, p_msg]]> </if> ",
    ") VALUES (#{pCodeId}, #{bizType}, #{provider}, #{pCode}, #{bizMd5}",
    "<if test=\"pMsg != null and pMsg != '' \"> <![CDATA[, #{pMsg}]]> </if> ",
    ")",
    "</script>"
  })
  @Options(useGeneratedKeys = true, keyColumn = "id")
  int insert(ProviderErrCodeEnumDO entity);

  @Select({
    "<script>",
    "SELECT `p_code_id`,`biz_type`,`provider`,`p_code`,`p_msg`,`biz_md5`, `biz_code_id` FROM `provider_err_code_enum`",
    " WHERE `biz_md5`=#{md5} LIMIT 1",
    "</script>",
  })
  ProviderErrCodeEnumDO getByMd5(String md5);
}
