package com.timevale.infoauth.service.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务类型
 * <p>
 * 我就是想重命名一下枚举
 *
 * <AUTHOR>
 */
public enum InfoAuthServiceType {
  /**
   *
   */
  ENTERPRISE_3_ELEMENT(1, "企业三要素", "ORG_INFO_AUTH"),

  ENTERPRISE_2_ELEMENT(2, "企业二要素", "ORG_INFO_AUTH_TWO"),

  ENTERPRISE_4_ELEMENT(3, "企业四要素", "ORG_INFO_AUTH_FOUR"),

  LAWFIRM_3_ELEMENT(4, "律所三要素", "ORG_INFO_AUTH_LAW"),

  SOCIALORG_3_ELEMENT(5, "社会组织三要素", "ORG_INFO_AUTH_SOCIAL"),

  BANKCARD_4_ELEMENT(11, "个人银行卡四要素", "PSN_BANK4"),

  BANKCARD_3_ELEMENT(12, "个人银行卡三要素", "PSN_BANK3"),

  MOBILE_3_ELEMENT(13, "个人运营商三要素", "PSN_TELECOM3"),

  IDCARD_2_ELEMENT(14, "个人身份证二要素", "PSN_ID2"),

  IDCARD_OCR(15, "身份证OCR", "PSN_OCR"),
  FOREIGN_PERMANENT_OCR(16, "外国人永久居住证OCR", "FOREIGN_PERMANENT_OCR"),

  BANKCARD_OCR(17, "银行卡OCR", "BANK_CARD_OCR"),

  BUSINESS_LICENSE_OCR(18, "营业执照OCR", "ORG_LICENSE_OCR"),

  DRIVER_LICENSE_OCR(19, "驾驶证OCR", "OCR_LICENCE_DRIVING"),

  DRIVING_LICENSE_OCR(20, "行驶证OCR", "OCR_PERMIT_DRIVING"),

  BANKCARD_4_ELEMENT_INTERNATIONAL(21, "个人银行卡四要素国际版", "PSN_BANK4_INTERNATIONAL"),

  BANKCARD_4_ELEMENT_DETAIL_INNER(22, "个人银行卡四要素详情版（内部使用，未来废弃）",
      "PSN_BANK4_DETAIL_INNER"),

  MOBILE_3_ELEMENT_DETAIL_INNER(23, "个人运营商三要素详情版（内部使用，未来废弃）",
      "PSN_TELECOM3_DETAIL_INNER"),

  BANKCARD_4_ELEMENT_DETAIL(24, "个人银行卡四要素详情版", "BANK4_DETAIL"),

  BANKCARD_3_ELEMENT_DETAIL(25, "个人银行卡三要素详情版", "BANK3_DETAIL"),

  MOBILE_3_ELEMENT_DETAIL(26, "个人运营商三要素详情版", "TELECOM3_DETAIL"),

  GENERAL_CHARACTERS_OCR(27, "通用文字识别OCR", "GENERAL_CHARACTERS_OCR"),

  VEHICLE_REG_CERT_OCR(28, "机动车登记证书OCR", "VEHICLE_REG_CERT_OCR"),

  PSN_NAME_MOBILE_2_ELEMENT(29, "个人姓名手机号二要素", "PSN_NAME_MOBILE_2_ELEMENT"),

  FACE_PHOTO_COMPARE_WITHOUT_SOURCE(30, "人脸无源库照比对", "FACE_PHOTO_COMPARE_WITHOUT_SOURCE"),

  FACE_PHOTO_COMPARE_WITH_SOURCE(31, "人脸有源两照比对", "FACE_PHOTO_COMPARE_WITH_SOURCE"),

  LAWFIRM_2_ELEMENT(32, "律所二要素", "ORG_INFO_AUTH_LAW2"),

  SOCIALORG_2_ELEMENT(33, "社会组织二要素", "ORG_INFO_AUTH_SOCIAL2"),

  // 2025/2/26  用于登记号来进行组织的三要素核验。
  REGISTRATION_NO_3_ELEMENT(34, "组织登记号三要素", "REGISTRATION_NO_3_ELEMENT"),

  // 企业证件号模糊查询没有意义
  ENTERPRISE_FUZZY_QUERY(60, "企业名称模糊查询", "ENTERPRISE_QUERY"),

  // 包含企业名称精确查询
  ENTERPRISE_ACCURATE_QUERY(61, "企业名称精确查询", "ENTERPRISE_ACCURATE_QUERY"),
  // 包含企业证件号精确查询
  ENTERPRISE_ACCURATE_CERT_NO_QUERY(62, "企业证件号精确查询", "ENTERPRISE_ACCURATE_CERT_NO_QUERY"),

  //企业详情插件
  ORG_SUMMARY_QUERY(63, "企业详情插件", "ORG_SUMMARY_QUERY"),


  ORG_SAVE_ES(97, "企业es数据保存", "ORG_SAVE_ES"),

  PSN_SAVE_ES(98, "个人es数据保存", "PSN_SAVE_ES"),

  INFO_SERVICE(99, "信息比对主表记录", "INFO_SERVICE"),

  /**
   * 随机金额打款校验
   */
  BANK_TRANSFER(102, "随机金额打款校验", "BANK_TRANSFER"),

  /**
   * 反向打款
   */
  REVERSE_PAYMENT(107, "反向打款", "REVERSE_PAYMENT"),

  /**
   * 支付宝刷脸
   * PS: 暂时无需对接供用商调用，只做供用商选举
   */
  ALIPAY_FACE(210, "支付宝刷脸", "ALIPAY_FACE"),

  /**
   * 支付宝视频双录制语音识别
   * PS: 暂时无需对接供用商调用，只做供用商选举
   */
  ALIPAY_AUDIO_VIDEO_DUAL(211, "支付宝视频双录制语音识别", "ALIPAY_AUDIO_VIDEO_DUAL"),

  /**
   * 微信视频双录制语音识别
   * PS: 暂时无需对接供用商调用，只做供用商选举
   */
  WE_CHAT_VIDEO_DUAL(212, "微信视频双录制语音识别", "WE_CHAT_VIDEO_DUAL"),

  UNKNOW(0, "未知", "UNKNOW"),
  ;

  private int dbValue;

  private String description;

  private String oldName;

  public static Map<Integer, InfoAuthServiceType> nameIndex;

  public static Map<Integer, InfoAuthServiceType> organIndex;

  public static Map<Integer, InfoAuthServiceType> indivIndex;

  public static List<InfoAuthServiceType> payIndex;

  public static Map<Integer, InfoAuthServiceType> infoAuthCompareIndex;

  static {
    nameIndex = new HashMap<>();
    organIndex = new HashMap<>();
    indivIndex = new HashMap<>();
    payIndex = new ArrayList<>();
    infoAuthCompareIndex = new HashMap<>();
    for (InfoAuthServiceType serviceType : InfoAuthServiceType.values()) {
      nameIndex.put(serviceType.dbValue(), serviceType);
    }
    organIndex.put(ENTERPRISE_3_ELEMENT.dbValue, ENTERPRISE_3_ELEMENT);
    organIndex.put(ENTERPRISE_2_ELEMENT.dbValue, ENTERPRISE_2_ELEMENT);
    organIndex.put(ENTERPRISE_4_ELEMENT.dbValue, ENTERPRISE_4_ELEMENT);
    organIndex.put(LAWFIRM_3_ELEMENT.dbValue, LAWFIRM_3_ELEMENT);
    organIndex.put(SOCIALORG_3_ELEMENT.dbValue, SOCIALORG_3_ELEMENT);
    organIndex.put(LAWFIRM_2_ELEMENT.dbValue, LAWFIRM_2_ELEMENT);
    organIndex.put(SOCIALORG_2_ELEMENT.dbValue, SOCIALORG_2_ELEMENT);
    // 2025/3/18  组织登记新增
    organIndex.put(REGISTRATION_NO_3_ELEMENT.dbValue, REGISTRATION_NO_3_ELEMENT);

    indivIndex.put(BANKCARD_4_ELEMENT.dbValue, BANKCARD_4_ELEMENT);
    indivIndex.put(BANKCARD_3_ELEMENT.dbValue, BANKCARD_3_ELEMENT);
    indivIndex.put(MOBILE_3_ELEMENT.dbValue, MOBILE_3_ELEMENT);
    indivIndex.put(IDCARD_2_ELEMENT.dbValue, IDCARD_2_ELEMENT);
    indivIndex.put(BANKCARD_4_ELEMENT_INTERNATIONAL.dbValue, BANKCARD_4_ELEMENT_INTERNATIONAL);
    indivIndex.put(BANKCARD_4_ELEMENT_DETAIL.dbValue, BANKCARD_4_ELEMENT_DETAIL);
    indivIndex.put(BANKCARD_3_ELEMENT_DETAIL.dbValue, BANKCARD_3_ELEMENT_DETAIL);
    indivIndex.put(MOBILE_3_ELEMENT_DETAIL.dbValue, MOBILE_3_ELEMENT_DETAIL);

    payIndex.add(BANK_TRANSFER);
    payIndex.add(REVERSE_PAYMENT);

    infoAuthCompareIndex.put(BANKCARD_4_ELEMENT.dbValue, BANKCARD_4_ELEMENT);
    infoAuthCompareIndex.put(BANKCARD_3_ELEMENT.dbValue, BANKCARD_3_ELEMENT);
    infoAuthCompareIndex.put(MOBILE_3_ELEMENT.dbValue, MOBILE_3_ELEMENT);
    infoAuthCompareIndex.put(IDCARD_2_ELEMENT.dbValue, IDCARD_2_ELEMENT);
    infoAuthCompareIndex.put(ENTERPRISE_4_ELEMENT.dbValue, ENTERPRISE_4_ELEMENT);
    infoAuthCompareIndex.put(
        BANKCARD_4_ELEMENT_INTERNATIONAL.dbValue, BANKCARD_4_ELEMENT_INTERNATIONAL);
    infoAuthCompareIndex.put(
        BANKCARD_4_ELEMENT_DETAIL_INNER.dbValue, BANKCARD_4_ELEMENT_DETAIL_INNER);
  }

  InfoAuthServiceType(int dbValue, String description, String oldName) {
    this.dbValue = dbValue;
    this.description = description;
    this.oldName = oldName;
  }

  public int dbValue() {
    return dbValue;
  }

  public String getDescription() {
    return description;
  }

  public static InfoAuthServiceType from(Integer dbValue) {
    InfoAuthServiceType serviceType = nameIndex.get(dbValue);
    return serviceType == null ? UNKNOW : serviceType;
  }

  public String getOldName() {
    return oldName;
  }

  public static boolean isOrgan(Integer dbValue) {
    if (dbValue == null) {
      return false;
    }
    return organIndex.containsKey(dbValue);
  }

  public static boolean isIndiv(Integer dbValue) {
    if (dbValue == null) {
      return false;
    }
    return indivIndex.containsKey(dbValue);
  }

  public static boolean isPsnBank4(Integer dbValue) {
    if (dbValue == BANKCARD_4_ELEMENT.dbValue
        || dbValue == BANKCARD_4_ELEMENT_DETAIL_INNER.dbValue
        || dbValue == BANKCARD_4_ELEMENT_INTERNATIONAL.dbValue
        || dbValue == BANKCARD_4_ELEMENT_DETAIL.dbValue) {
      return true;
    }
    return false;
  }

  public static boolean isPsnTel3(Integer dbValue) {
    if (dbValue == MOBILE_3_ELEMENT_DETAIL_INNER.dbValue
        || dbValue == MOBILE_3_ELEMENT.dbValue
        || dbValue == MOBILE_3_ELEMENT_DETAIL.dbValue) {
      return true;
    }
    return false;
  }

  public static boolean isAdvanced(InfoAuthServiceType serviceType) {
    if (MOBILE_3_ELEMENT_DETAIL == serviceType || BANKCARD_3_ELEMENT_DETAIL == serviceType
        || BANKCARD_4_ELEMENT_DETAIL == serviceType) {
      return true;
    }
    return false;
  }

  public static InfoAuthServiceType parse(InfoAuthServiceType serviceType) {
    if (serviceType == ENTERPRISE_3_ELEMENT
        || serviceType == ENTERPRISE_4_ELEMENT
        || serviceType == SOCIALORG_3_ELEMENT
        || serviceType == ENTERPRISE_2_ELEMENT
        || serviceType == LAWFIRM_3_ELEMENT) {
      return ENTERPRISE_3_ELEMENT;
    }
    return serviceType;
  }

  public static InfoAuthServiceType getByName(String name) {
    for (InfoAuthServiceType infoAuthServiceType : InfoAuthServiceType.values()) {
      if (infoAuthServiceType.name().equalsIgnoreCase(name)) {
        return infoAuthServiceType;
      }
    }
    return UNKNOW;
  }

  public static InfoAuthServiceType advanced2Standard(InfoAuthServiceType serviceType) {
    switch (serviceType) {
      case MOBILE_3_ELEMENT_DETAIL:
        return MOBILE_3_ELEMENT;
      case BANKCARD_3_ELEMENT_DETAIL:
        return BANKCARD_3_ELEMENT;
      case BANKCARD_4_ELEMENT_DETAIL:
        return BANKCARD_4_ELEMENT;
      default:
        return UNKNOW;
    }
  }
}
