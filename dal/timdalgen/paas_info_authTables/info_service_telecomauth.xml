<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="info_service_telecomauth" physicalName="info_service_telecomauth" remark="info_service_telecomauth">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,IDNO,NAME,MOBILE,INFOAUTH_ID,PROVIDER_ID,BILLORDER_ID,PROVIDER_RESULT,RESULT,SERVICE_STATUS,CREATE_TIME,MODIFY_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.IDNO,sf.NAME,sf.MOBILE,sf.INFOAUTH_ID,sf.PROVIDER_ID,sf.BILLORDER_ID,sf.PROVIDER_RESULT,sf.RESULT,sf.SERVICE_STATUS,sf.CREATE_TIME,sf.MODIFY_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:info_service_telecomauth">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO info_service_telecomauth(
            ID
            ,IDNO
            ,NAME
            ,MOBILE
            ,INFOAUTH_ID
            ,PROVIDER_ID
            ,BILLORDER_ID
            ,PROVIDER_RESULT
            ,RESULT
            ,SERVICE_STATUS
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES(
             null
            , #{idno,jdbcType=VARCHAR}
            , #{name,jdbcType=VARCHAR}
            , #{mobile,jdbcType=VARCHAR}
            , #{infoauthId,jdbcType=VARCHAR}
            , #{providerId,jdbcType=VARCHAR}
            , #{billorderId,jdbcType=VARCHAR}
            , #{providerResult,jdbcType=VARCHAR}
            , #{result,jdbcType=INTEGER}
            , #{serviceStatus,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{modifyTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:info_service_telecomauth">
        INSERT INTO info_service_telecomauth(
            ID
            ,IDNO
            ,NAME
            ,MOBILE
            ,INFOAUTH_ID
            ,PROVIDER_ID
            ,BILLORDER_ID
            ,PROVIDER_RESULT
            ,RESULT
            ,SERVICE_STATUS
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.idno,jdbcType=VARCHAR}
                , #{item.name,jdbcType=VARCHAR}
                , #{item.mobile,jdbcType=VARCHAR}
                , #{item.infoauthId,jdbcType=VARCHAR}
                , #{item.providerId,jdbcType=VARCHAR}
                , #{item.billorderId,jdbcType=VARCHAR}
                , #{item.providerResult,jdbcType=VARCHAR}
                , #{item.result,jdbcType=INTEGER}
                , #{item.serviceStatus,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.modifyTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:info_service_telecomauth">
        <![CDATA[
        UPDATE info_service_telecomauth
        SET
            IDNO            = #{idno,jdbcType=VARCHAR}
            ,NAME            = #{name,jdbcType=VARCHAR}
            ,MOBILE          = #{mobile,jdbcType=VARCHAR}
            ,INFOAUTH_ID     = #{infoauthId,jdbcType=VARCHAR}
            ,PROVIDER_ID     = #{providerId,jdbcType=VARCHAR}
            ,BILLORDER_ID    = #{billorderId,jdbcType=VARCHAR}
            ,PROVIDER_RESULT = #{providerResult,jdbcType=VARCHAR}
            ,RESULT          = #{result,jdbcType=INTEGER}
            ,SERVICE_STATUS  = #{serviceStatus,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,MODIFY_TIME     = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:info_service_telecomauth">
        <![CDATA[
        DELETE FROM info_service_telecomauth
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:info_service_telecomauth">
        SELECT *
        FROM info_service_telecomauth
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
</table>
