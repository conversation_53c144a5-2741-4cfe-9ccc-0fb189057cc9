package com.timevale.infoauth.service.model;

/**
 * 通用枚举类，只放公共部分
 */
public enum CommonResultEnum implements IResultEnum {
  SUCCESS(0, "成功"),
  PARTIAL_FAILURE(1, "部分失败"),
  UNAUTH(401, "登录超时"),
  PARAM_WRONG(100000, "参数错误"),
  NULL_ARGUMENT(100001, "参数为空"),
  SERVER_TEMPLATE_ERR(100010, "内部服务错误"),
  RPC_INVOKE_ERR(100011, "内部服务错误");

  private int code;

  private String msg;

  @Override
  public int getCode() {
    return code;
  }

  public void setCode(int code) {
    this.code = code;
  }

  @Override
  public String getMsg() {
    return msg;
  }

  public String getMessage(Object... data) {
    return String.format(msg, data);
  }
  CommonResultEnum(int code, String msg) {
    this.code = code;
    this.msg = msg;
  }
}