package com.timevale.infoauth.dal.infoauth.dataobject.monitor;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * The table. 降级配置
 *
 * <AUTHOR> Kunpeng
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DegradationConfigDO extends ToString {

  /** id 自增主键. */
  private Long id;
  /** 降级配置ID. */
  private String configId;
  /** 告警配置ID */
  private String alertId;

  /** 配置生效开关[0关闭,1开启] */
  private Integer switchEnabled;

  /** 处理策略 */
  private String changeAction;
  //降级供应商
  private String changeDegradeProvider;
  /** 执行时间[秒] */
  private Integer changeTime;

  /**
   * 使用范围
   */
  private String scope;
  private String scopeAppId;
  private String desc;
  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;
}
