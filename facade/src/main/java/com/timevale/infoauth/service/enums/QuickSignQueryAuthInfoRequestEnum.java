package com.timevale.infoauth.service.enums;

import java.util.HashMap;
import java.util.Map;

/** <AUTHOR> */
public enum QuickSignQueryAuthInfoRequestEnum {

  /** 请忽略该状态 */
  IGNORE(-1),

  /** 进行中 */
  ING(0, "刷脸进行中"),

  /** 已扫描 */
  SCAN(1, "已扫描"),

  /** 成功 */
  SUCCESS(2, "认证成功"),

  /** 失败 */
  FAIL(3, "认证失败"),
  ;

  private int value;

  private String msg;

//  private static final Map<Integer, FaceAuthStatusEnum> cachedValues = new HashMap<>();

//  static {
//    for (FaceAuthStatusEnum status : FaceAuthStatusEnum.values()) {
//      cachedValues.put(status.getValue(), status);
//    }
//  }

  QuickSignQueryAuthInfoRequestEnum(Integer value) {
    this.value = value;
  }
  //
  QuickSignQueryAuthInfoRequestEnum(Integer value, String msg) {
    this.value = value;
    this.msg = msg;
  }
//
//  public static FaceAuthStatusEnum from(String name) {
//    for (FaceAuthStatusEnum status : FaceAuthStatusEnum.values()) {
//      if (status.name().equalsIgnoreCase(name)) {
//        return status;
//      }
//    }
//
//    return null;
//  }
//
//  public static FaceAuthStatusEnum from(int value) {
//    return cachedValues.get(value);
//  }

  public int getValue() {
    return value;
  }

  public String getMsg() {
    return msg;
  }
}
