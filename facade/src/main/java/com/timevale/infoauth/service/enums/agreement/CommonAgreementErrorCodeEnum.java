package com.timevale.infoauth.service.enums.agreement;

import com.timevale.infoauth.service.model.IResultEnum;
import com.timevale.mandarin.base.enums.EnumBase;

/**
 * 通用协议管理错误码
 * 范围：30610000~30619999
 * @see  com.timevale.infoauth.service.model.CommonResultEnum 基础错误码参考
 * @author: zhexiu
 * @since: 2024-12-04 12:48
 */
public enum CommonAgreementErrorCodeEnum  implements EnumBase, IResultEnum {
  AGR_INFO_NOT_FOUND("AGR_INFO_NOT_FOUND", "协议记录不存在", 30610000),
  AGR_INFO_NOT_ENABLE("AGR_INFO_NOT_ENABLE", "协议未生效", 30610001),
  AGR_INFO_ENABLE_CANNOT_CHANGE("AGR_INFO_ENABLE_CANNOT_CHANGE", "协议已经生效不能修改状态", 30610002),
  AGR_INFO_ENABLE_CANNOT_DEL("AGR_INFO_ENABLE_CANNOT_DEL", "协议已经生效不能删除", 30610002),

  AGR_RELEASE_VERSION_NOT_FOUND("AGR_RELEASE_VERSION_NOT_FOUND", "协议内容版本不存在", 30610100),
  AGR_RELEASE_VERSION_NOT_ENABLE("AGR_RELEASE_VERSION_NOT_ENABLE", "协议内容版本未发布", 30610101),
  AGR_RELEASE_ENABLE_CANNOT_CHANGE("AGR_RELEASE_ENABLE_CANNOT_CHANGE", "协议内容已经发布不能修改状态", 30610102),

  ;

  private String SCode;
  private String message;
  private Integer NCode;

  CommonAgreementErrorCodeEnum(String SCode, String message, int NCode) {
    this.SCode = SCode;
    this.message = message;
    this.NCode = NCode;
  }

  @Override
  public int getCode() {
    return NCode;
  }

  @Override
  public String getMsg() {
    return message;
  }

  public String getMessage() {
    return message;
  }

  public String getSCode() {
    return SCode;
  }

  @Override
  public String message() {
    return message;
  }
}
