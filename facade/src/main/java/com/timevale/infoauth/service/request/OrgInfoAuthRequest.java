package com.timevale.infoauth.service.request;

import com.timevale.infoauth.service.model.BaseModel;
import com.timevale.infoauth.service.enums.Nation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrgInfoAuthRequest extends BaseModel {
  /** 企业名称 */
  private String name;

  /** 组织机构代码 */
  private String codeORG;

  /** 社会统一信用代码 */
  private String codeUSC;

  /** 工商注册号 */
  private String codeREG;
  /** 法人姓名 */
  @NotNull private String legalName;

  /** 法人证件号 */
  private String legalCertNo;

  /** 法人证件类型
   * @see com.timevale.infoauth.service.enums.UserCenterCertTypeEnum
   * */
  private Integer legalCertType;

  /** 法人国籍 */
  private Nation legalArea = Nation.CH_MAINLAND;

  /**
   * realname_sub_service表主键
   */
  private Long subServicePrimaryKey;
}
