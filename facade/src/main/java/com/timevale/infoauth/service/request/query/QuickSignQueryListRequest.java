package com.timevale.infoauth.service.request.query;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class QuickSignQueryListRequest extends ToString {
    /**
     * 身份证号
     */
    private String idNo;
    private String infoAuthId;

    /**
     * 应用Id
     */
    private String appId;

    /**
     * 企业组织机构代码
     */
    private String codeOrg;

    /**
     * 企业社会统一信用代码
     */
    private String codeUsc;

    /**
     * 工商注册号
     */
    private String codeReg;

    /**
     * 签署时间
     */
    private Timestamp modifyTime;

    /**
     * 认证状态
     */
    private Integer serviceStatus;

    /**
     * 分页参数
     */
    private Integer pageSize, pageIndex;

    /**
     * 认证类型
     */
    private Integer type;
}
