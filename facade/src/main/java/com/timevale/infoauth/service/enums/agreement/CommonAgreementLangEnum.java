package com.timevale.infoauth.service.enums.agreement;

/**
 * 语言环境：en-us=英文;zh-cn=中文，默认中文
 * <AUTHOR>
 * @since 2024/12/4 21:04
 */
public enum CommonAgreementLangEnum {


    EN_US("en-us","英文"),
    ZH_CN("zh-cn","中文");

    private String lang;
    private String desc;

    CommonAgreementLangEnum(String lang, String desc) {
        this.lang = lang;
        this.desc = desc;
    }

    public String getLang() {
        return lang;
    }

    public String getDesc() {
        return desc;
    }

    public static CommonAgreementLangEnum getByLang(String lang){
        for (CommonAgreementLangEnum value : CommonAgreementLangEnum.values()) {
            if(value.getLang().equals(lang)){
                return value;
            }
        }
        return ZH_CN;
    }
}
