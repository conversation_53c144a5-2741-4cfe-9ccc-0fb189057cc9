package com.timevale.infoauth.service.request;

import static com.timevale.bizcommon.result.identityauth.IdentityAuthResultCodeEnum.PARAM_ERROR;

import com.timevale.infoauth.service.exception.InfoAuthBizRuntimeException;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/12/7 16:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProviderSelectServiceRequest extends ToString {

  /**
   * 业务类型名称: 对应运营支撑平台-供应商管理的业务类型
   * ps: 企业三要素、支付宝刷脸版本等
   * @see com.timevale.infoauth.service.enums.InfoAuthServiceType
   */
  @NotBlank(message = "业务类型名称不能为空")
  private String serviceTypeName;

  /**
   * 业务APPID，用于获取主通道配置
   * 当需要优先获取APPID下的主通道配置时需要传入
   * 需同时传入2个参数：bizAppId + mainChannelKey
   */
  private String bizAppId;

  /**
   * 主通道配置KEY
   * 取的是开放平台APPID配置KEY
   * 当需要优先获取APPID下的主通道配置时需要传入
   * 需同时传入2个参数：bizAppId + mainChannelKey
   */
  private String mainChannelKey;

  public void validate() {
    boolean appIdNotNull = StringUtils.isNotBlank(bizAppId);
    boolean channelKeyNotNull = StringUtils.isNotBlank(mainChannelKey);
    if (appIdNotNull ^ channelKeyNotNull) {
      throw new InfoAuthBizRuntimeException(
          PARAM_ERROR.getCode(), String.format(PARAM_ERROR.getMessage(), "bizAppId和mainChannelKey需要同时传入或都不传"));
    }
  }

}
