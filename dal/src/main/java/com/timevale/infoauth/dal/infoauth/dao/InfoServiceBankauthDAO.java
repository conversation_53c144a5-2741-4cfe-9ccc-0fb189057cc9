package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceBankauthDO;
import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceBankauthExtendDO;
import com.timevale.infoauth.dal.infoauth.facade.AuthInfoQueryRequestDTO;
import com.timevale.infoauth.dal.infoauth.facade.AuthResultQueryRecordDTO;
import org.apache.ibatis.annotations.*;

import java.sql.Timestamp;
import java.util.List;

/**
 * The Table info_service_bankauth. info_service_bankauth
 *
 * <AUTHOR> Kunpeng
 */
@Mapper
public interface InfoServiceBankauthDAO {

  /**
   * desc:插入表:info_service_bankauth.<br>
   *
   * @param entity entity
   * @return int
   */
  int insert(InfoServiceBankauthDO entity);

  @Insert(
          "INSERT INTO `info_service_bankauth` "
                  + "(`infoauth_id`, `name`, `idno`, `user_center_cert_type`, `mobile`, `cardno`,`provider_id`,`provider_result`,`result`, `service_status`,`timestamp_id`,`billorder_id`,`create_time`,`modify_time`)"
                  + " VALUES (#{infoauthId}, #{name},#{idno} ,#{userCenterCertType}, #{mobile}, #{cardno},#{providerId},#{providerResult},#{result}, #{serviceStatus},#{timestampId},#{billorderId}, now(), now())")
  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  int saveApply(InfoServiceBankauthDO entity);

  /**
   * desc:批量插入表:info_service_bankauth.<br>
   *
   * @param list list
   * @return int
   */
  int insertBatch(List<InfoServiceBankauthDO> list);

  /**
   * desc:根据主键删除数据:info_service_bankauth.<br>
   *
   * @param id id
   * @return int
   */
  int deleteById(Long id);

  /**
   * desc:根据主键获取数据:info_service_bankauth.<br>
   *
   * @param id id
   * @return InfoServiceBankauthDO
   */
  @Select("select * from info_service_bankauth where id = #{id}")
  InfoServiceBankauthDO getById(Long id);

  @Update("update info_service_bankauth set service_status=#{status} where id = #{id}")
  void updateStatus(@Param("id") Long id, @Param("status") Integer status);

  @Update(
      "UPDATE `info_service_bankauth`"
          + " set `infoauth_id`=#{infoauthId},`name`=#{name},`idno`=#{idno},`mobile`=#{mobile},`bank`=#{bank},`cardno`=#{cardno},`provider_id`=#{providerId},`provider_result`=#{providerResult},`result`=#{result},`service_status`=#{serviceStatus},`timestamp_id`=#{timestampId},`billorder_id`=#{billorderId}"
          + " WHERE `id`=#{id}")
  void update(InfoServiceBankauthDO infoServiceBankauthDO);

  @Select("select * from info_service_bankauth where infoauth_id = #{infoAuthId}")
  InfoServiceBankauthDO getByInfoAuthId(@Param("infoAuthId") String infoAuthId);

  @Select("select * from info_service_bankauth where idno = #{idno} and modify_time<=#{modifyTime} order by id desc limit #{currIndex},#{pageSize}")
  List<InfoServiceBankauthDO> getByUpdateTimeAndIdNo(@Param("idno") String idno, @Param("modifyTime") Timestamp modifyTime, @Param("currIndex") Integer currIndex, @Param("pageSize") Integer pageSize);

  /**
   * desc:专为快捷签出证查询使用
   *
   * @param authInfoQueryRequestDTO
   * @return List<InfoServiceBankauthDO>
   */
  @Select({
          "<script>",
          "select * from info_service_bankauth t1 inner join info_service t2 on t1.infoauth_id = t2.infoauth_id",
          "where  1=1 ",
          "<if test='infoAuthId != null'> and t1.infoauth_id = #{infoAuthId} </if>",
          "<if test='appId != null'> and t2.app_id= #{appId} </if>",
          "<if test='idno != null'> and t1.idno = #{idno} </if>",
          "<if test='cardno != null'> and t1.cardno = #{cardno} </if>",
          "<if test='mobile != null'> and t1.mobile = #{mobile} </if>",
          "<if test='serviceStatus != null'> and t1.service_status = #{serviceStatus} </if>",
          "<if test='updateTime != null'> and t1.modify_time between 0 and #{updateTime} </if>",
//          "<if test='modifyTime != null'> and modify_time &lt;= #{modifyTime} </if>",
          "order by t1.modify_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<InfoServiceBankauthExtendDO> queryByUpdateTimeAndIdNo(AuthInfoQueryRequestDTO authInfoQueryRequestDTO);

  @Select({
          "<script>",
          "select * from info_service_bankauth t1 inner join info_service t2 on t1.infoauth_id = t2.infoauth_id",
          "where  1=1 ",
          "<if test='infoAuthId != null'> and t1.infoauth_id = #{infoAuthId} </if>",
          "<if test='appId != null'> and t2.app_id= #{appId} </if>",
          "<if test='idno != null'> and t1.idno = #{idno} </if>",
          "<if test='cardno != null'> and t1.cardno = #{cardno} </if>",
          "<if test='mobile != null'> and t1.mobile = #{mobile} </if>",
          "<if test='serviceStatus != null'> and t1.service_status = #{serviceStatus} </if>",
          "<if test='modifyTime != null'> and t1.modify_time between 0 and #{modifyTime} </if>",
          "order by t1.modify_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<InfoServiceBankauthExtendDO> quickSignQueryListByPsn4Info(AuthInfoQueryRequestDTO authInfoQueryRequestDTO);

  /**
   * desc:专为信息比对记录查询使用
   *
   * @param authInfoQueryRequestDTO
   * @return List<InfoServiceBankauthDO>
   */
  @Select({
          "<script>",
          "select * from info_service_bankauth t1 inner join info_service t2 on t1.infoauth_id = t2.infoauth_id",
          "where  1=1 ",
          "<if test='infoAuthId != null'> and t1.infoauth_id = #{infoAuthId} </if>",
          "<if test='appId != null'> and t2.app_id= #{appId} </if>",
          "<if test='idno != null'> and t1.idno = #{idno} </if>",
          "<if test='cardno != null'> and t1.cardno = #{cardno} </if>",
          "<if test='mobile != null'> and t1.mobile = #{mobile} </if>",
          "<if test='serviceStatus != null'> and t1.service_status = #{serviceStatus} </if>",
          "<if test='startTime != null'> and t1.modify_time between #{startTime} and #{endTime} </if>",
          "order by t1.modify_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<InfoServiceBankauthExtendDO> queryForInfoAuthRecord(AuthInfoQueryRequestDTO authInfoQueryRequestDTO);

  @Update(
          "UPDATE `info_service_bankauth` SET err_id = #{errId} WHERE `infoauth_id` = #{infoauthId}")
  void bindErrId(@Param("infoauthId") String infoauthId, @Param("errId") Long errId);


  /**
   * desc:根据条件查询企业信息比对记录
   *
   * @return InfoServiceDO
   */
  @Select({
          "<script>",
          "select * from info_service t1 inner join info_service_bankauth t2 on t1.infoauth_id = t2.infoauth_id",
          "where  1=1 ",
          "<if test='certName != null'> and t2.name = #{certName} </if>",
          "<if test='serviceStatus != null'> and t2.service_status = #{serviceStatus} </if>",
          "<if test='certNo != null'> and t2.idno = #{certNo} </if>",
          "<if test='infoAuthServiceType != null'> and t1.type = #{infoAuthServiceType} </if>",
          "<if test='appId != null'> AND t1.app_id = #{appId} </if>",
          "<if test='startTime != null'> AND t1.modify_time between #{startTime} and #{endTime} </if>",
          "<if test='infoAuthId != null'> AND t1.infoauth_id = #{infoAuthId} </if>",
          "order by t1.modify_time desc limit #{pageIndex},#{pageSize}",
          "</script>"})
  List<InfoServiceBankauthDO> getPsnBank4ByAuthResultQuery(AuthResultQueryRecordDTO authResultQueryRecordDTO);

  /**
   * desc:根据条件查询企业信息比对记录
   *
   * @return InfoServiceDO
   */
  @Select({
          "<script>",
          "select count(*) from info_service t1 inner join info_service_bankauth t2 on t1.infoauth_id = t2.infoauth_id",
          "where  1=1 ",
          "<if test='certName != null'> and t2.name = #{certName} </if>",
          "<if test='serviceStatus != null'> and t2.service_status = #{serviceStatus} </if>",
          "<if test='certNo != null'> and t2.idno = #{certNo} </if>",
          "<if test='infoAuthServiceType != null'> and t1.type = #{infoAuthServiceType} </if>",
          "<if test='appId != null'> AND t1.app_id = #{appId} </if>",
          "<if test='startTime != null'> AND t1.modify_time between #{startTime} and #{endTime} </if>",
          "<if test='infoAuthId != null'> AND t1.infoauth_id = #{infoAuthId} </if>",
          "</script>"})
  int getPsnBank4ByAuthResultQueryCount(AuthResultQueryRecordDTO authResultQueryRecordDTO);
}
