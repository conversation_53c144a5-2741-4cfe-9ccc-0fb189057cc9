package com.timevale.infoauth.dal.infoauth.dataobject;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * The table. info_service
 *
 * <AUTHOR> Kunpeng
 */
@Data
public abstract class InfoServiceAuthDO extends ToString {

    /** infoauthId 业务主键. */
    private String infoauthId;

    public String getInfoauthId() {
        return infoauthId;
    }

    public void setInfoauthId(String infoauthId) {
        this.infoauthId = infoauthId;
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
