package com.timevale.infoauth.service.request.agreement;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Date;

/**
 * 通用协议修改信息
 *
 * <AUTHOR>
 */
@Data
public class CommonAgreementContentModifyRequest extends CommonAgreementContentBaseRequest {


  /** 协议简称. */
  private String shortName;

  /** 协议全称. */
  private String fullName;

  /** 协议发布备注. */
  private String releaseRemark;
  /** 协议内容. */
  private String content;
  /** 协议内容存储类型 htmlContent=html文本 htmlUrl=http地址 pdfUrl=pdf地址 zeyuanUrl=泽元系统Url. */
  private String contentType;
  /** 操作人. */
  private String operator;


}
