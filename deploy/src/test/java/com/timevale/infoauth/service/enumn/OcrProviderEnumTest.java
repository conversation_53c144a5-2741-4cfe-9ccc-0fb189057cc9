package com.timevale.infoauth.service.enumn;

import com.timevale.infoauth.service.impl.provider.constants.OcrProviderEnum;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @since 2023/10/11 15:01
 */
public class OcrProviderEnumTest {

    @Test
    public void fromDefault(){
        Assert.assertEquals(OcrProviderEnum.from("xxx"), OcrProviderEnum.TENCENT);
        Assert.assertEquals(OcrProviderEnum.from("HUAWEI"), OcrProviderEnum.HUAWEI);
    }
}
