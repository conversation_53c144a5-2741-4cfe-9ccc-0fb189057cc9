package com.timevale.infoauth.service.api;

import com.timevale.infoauth.service.dto.PClientDTO;
import com.timevale.mandarin.common.annotation.RestClient;

import java.util.List;

/**
 * 应用层服务（客户端子域 - 通用域）
 *
 * <AUTHOR> href="mailto:<EMAIL>">jiuchen</a>
 */
@RestClient(serviceId = "infoauth-service")
public interface ProviderClientService {

  /**
   * 保存客户端子域
   *
   * @param pClientDTO
   */
  void save(PClientDTO pClientDTO);

  /**
   * 查询一条客户端子域
   *
   * @param managerId
   * @return
   */
  PClientDTO getClientById(Integer managerId);

  /**
   * 查询所有客户端子域
   *
   * @return
   */
  List<PClientDTO> queryClientAll();
}
