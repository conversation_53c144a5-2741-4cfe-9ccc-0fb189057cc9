package com.timevale.infoauth.dal.infoauth.dataobject;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InfoServiceCompareDO  extends InfoServiceAuthDO {
    private String infoauthId;
    private Integer type;
    private Integer objectType;
    private String appId;
    private String name;
    private String idno;
    private String mobile;
    private String bank;
    private String cardno;
    private String codeORG;
    private String codeUSC;
    private String codeREG;
    private String legalName;
    private String legalIdno;
    private Integer legalCertType;
    private Short legalArea;
    private Date businessStartTime;
    private Date businessEndTime;
    private String businessEndTimeDesc;
    private Short businessStatus;
    private String providerId;
    private String providerResult;
    private String baseProviderId;
    private String baseProviderResult;
    private Integer result;
    private Integer serviceStatus;
    private Integer status;
    private String timestampId;
    private String billorderId;
    private Long errId;
    private Integer userCenterCertType;
    private Date createTime;
    private Date modifyTime;
    private Boolean isDelete;

}
