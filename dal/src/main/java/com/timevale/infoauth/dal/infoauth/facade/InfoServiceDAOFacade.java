package com.timevale.infoauth.dal.infoauth.facade;

import com.timevale.infoauth.dal.infoauth.dao.InfoServiceDAO;
import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceDO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class InfoServiceDAOFacade {

  @Resource private InfoServiceDAO infoServiceDAO;

  public void updateStatusByInfoauthId(InfoServiceDO service) {
    infoServiceDAO.updateStatus(service.getInfoauthId(), service.getStatus());
  }
}
