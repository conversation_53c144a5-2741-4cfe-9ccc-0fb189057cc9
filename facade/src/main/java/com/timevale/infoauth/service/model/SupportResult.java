package com.timevale.infoauth.service.model;

import com.timevale.mandarin.common.result.ToString;

public class SupportResult<T> extends ToString {

  /**
   * 错误码，0表示成功，其他均表示失败
   */
  private int code;

  /**
   * 错误信息
   */
  private String message;

  /**
   * 业务信息
   */
  private T data;


  public SupportResult() {
  }

  public SupportResult(int code, String message, T data) {
    this.code = code;
    this.message = message;
    this.data = data;
  }

  public SupportResult(IResultEnum resultEnum, T data) {
    this.code = resultEnum.getCode();
    this.message = resultEnum.getMsg();
    this.data = data;
  }


  public int getCode() {
    return code;
  }

  public void setCode(int code) {
    this.code = code;
  }

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public T getData() {
    return data;
  }

  public void setData(T data) {
    this.data = data;
  }


  public boolean ifSuccess() {
    return CommonResultEnum.SUCCESS.getCode() == this.code;
  }

  public boolean ifFail() {
    return !this.ifSuccess();
  }


  public static <T> SupportResult<T> success(T d) {
    SupportResult<T> result = new SupportResult<>(CommonResultEnum.SUCCESS, d);
    return result;
  }

  public static <T> SupportResult<T> success() {
    SupportResult<T> result = new SupportResult<>(CommonResultEnum.SUCCESS, null);
    return result;
  }

  public static <T> SupportResult<T> fail(IResultEnum resultEnum) {
    return new SupportResult<>(resultEnum.getCode(), resultEnum.getMsg(), null);
  }

  public static <T> SupportResult<T> fail(IResultEnum resultEnum, T data) {
    return new SupportResult<>(resultEnum.getCode(), resultEnum.getMsg(), data);
  }

  public static <T> SupportResult<T> fail(IResultEnum resultEnum, String msg) {
    return new SupportResult<>(resultEnum.getCode(), msg, null);
  }

  public static <T> SupportResult<T> fail(int code, String msg) {
    return new SupportResult<>(code, msg, null);
  }
}