package com.timevale.infoauth.service.enums;

import com.timevale.esign.compontent.common.base.evidence.ISegmentType;

public enum EvidenceServiceType implements ISegmentType {
  PSN_BANK4(1, "PSN_BANK4", "银行四要素比对");
  private int segmentType;

  private String segmentName;

  private String description;

  EvidenceServiceType(int segmentType, String segmentName, String description) {
    this.segmentType = segmentType;
    this.segmentName = segmentName;
    this.description = description;
  }

  public int getSegmentType() {
    return segmentType;
  }

  public void setSegmentType(int segmentType) {
    this.segmentType = segmentType;
  }

  public String getSegmentName() {
    return segmentName;
  }

  public void setSegmentName(String segmentName) {
    this.segmentName = segmentName;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }
}
