package com.timevale.infoauth.dal.infoauth.cache;

import lombok.Builder;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 本地缓存
 *
 * null值不存储
 *
 */
public class LocalStorageMap {

    private Map<String,Entity> storage ;

    private int maxSize;

    private static final int LIMIT_MAX = 1000;

    private static final int KEY_LENGTH = 512;

    private static LocalStorageMap instance = new LocalStorageMap(LIMIT_MAX);

    private final Logger logger = LoggerFactory.getLogger(LocalStorageMap.class);

    private LocalStorageMap(int maxSize) {
        storage = new ConcurrentHashMap<>(maxSize);
        this.maxSize = maxSize > LIMIT_MAX ? LIMIT_MAX : maxSize;
    }

    public static LocalStorageMap getInstance(){
        return instance;
    }

    /**
     * 放入缓存
     * @param key 查询对象的键
     * @param value 缓存对象
     * @param seconds 存储时长
     * @return 是否存储成功
     */
    public boolean put(String key,Object value,long seconds){

        // 日志输出缓存长度
        logger.info("====> local cache map size is :{}",storage.size());

        // key 为 null 或者 key 大于限制 或者 缓存容量大于限制
        if(key == null || key.length() > KEY_LENGTH || storage.size() > maxSize){
            return false;
        }
        if(value == null){
            return false;
        }
        storage.put(key,Entity.builder()
                .data(value)
                .timestamp(System.currentTimeMillis() + seconds*1000)
                .build());
        return true;
    }

    /**
     * 获取key值有关的缓存数据
     * @param key 获取凭证
     * @return 缓存数据
     */
    public Object get(String key){
        if(key == null || key.length() > KEY_LENGTH){
            return false;
        }
        Entity entity = storage.get(key);

        if(entity != null && entity.getTimestamp() > System.currentTimeMillis()){
            return entity.getData();
        }else {
            storage.remove(key);
            return null;
        }
    }

    /**
     * 清除某一个缓存数据
     * @param key 缓存键值
     */
    public boolean delete(String key){
        return storage.remove(key) != null;
    }

    /**
     * 扫除过期存储对象
     */
    public void evict(){
        for(String key : storage.keySet()){
            Entity entity ;
            if((entity = storage.get(key)) != null && entity.getTimestamp() < System.currentTimeMillis()){
                storage.remove(key);
            }
        }
    }

    @Builder
    @Data
    private static class Entity{
        private Object data;
        private long timestamp;
    }
}
