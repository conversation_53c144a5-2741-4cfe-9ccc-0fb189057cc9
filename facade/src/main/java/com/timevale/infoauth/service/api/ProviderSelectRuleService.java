package com.timevale.infoauth.service.api;

import com.timevale.infoauth.service.request.ProviderSelectServiceRequest;
import com.timevale.infoauth.service.response.ProviderSelectServiceResponse;
import com.timevale.mandarin.common.annotation.RestClient;

/**
 * 通用供应商选举策略服务
 * 提供对外RPC接口对接
 *
 * <AUTHOR>
 * @since 2023/12/7 18:05
 */
@RestClient(serviceId = "infoauth-service")
public interface ProviderSelectRuleService {

  /**
   * 供应商选举
   * 仅做选举，不执行调用供应商
   * @param request
   * @return
   */
  ProviderSelectServiceResponse providerSelectOnly(ProviderSelectServiceRequest request);

}
