package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceOrgcheckDO;
import com.timevale.infoauth.dal.infoauth.facade.AuthInfoQueryRequestDTO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * The Table info_service_orgcheck. info_service_orgcheck
 *
 * <AUTHOR> Kunpeng
 */
public interface InfoServiceOrgcheckDAO {

  /**
   * desc:插入表:info_service_orgcheck.<br>
   *
   * @param entity entity
   * @return int
   */
  @Insert(
      "INSERT INTO info_service_orgcheck "
          + "(`id`, `infoauth_id`, `name`, `codeORG`, `codeUSC`, `codeREG`, `legal_name`, `legal_area`, `business_startTime`, `business_endTime`, `business_endTimeDesc`, `business_status`, `provider_id`, `provider_result`, `base_providerId`, `base_providerResult`, `result`, `service_status`, `billorder_id`, `timestamp_id`, `create_time`, `modify_time`) "
          + "VALUES "
          + "(#{id}, #{infoauthId}, #{name}, #{codeorg}, #{codeusc}, #{codereg}, #{legalName}, #{legalArea}, #{businessStarttime}, #{businessEndtime}, #{businessEndtimedesc}, #{businessStatus}, #{providerId}, #{providerResult},#{baseProviderid},#{baseProviderresult}, #{result}, #{serviceStatus}, #{billorderId}, #{timestampId}, now(), now())")
  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  public int insert(InfoServiceOrgcheckDO entity);

  /**
   * desc:批量插入表:info_service_orgcheck.<br>
   *
   * @param list list
   * @return int
   */
  public int insertBatch(List<InfoServiceOrgcheckDO> list);

  /**
   * desc:根据主键删除数据:info_service_orgcheck.<br>
   *
   * @param id id
   * @return int
   */
  public int deleteById(Long id);

  /**
   * desc:根据主键获取数据:info_service_orgcheck.<br>
   *
   * @param id id
   * @return InfoServiceOrgcheckDO
   */
  public InfoServiceOrgcheckDO getById(Long id);

  /**
   * desc:根据普通索引InfoauthId获取数据:info_service_orgcheck.<br>
   *
   * @param infoauthId infoauthId
   * @return List<InfoServiceOrgcheckDO>
   */
  public List<InfoServiceOrgcheckDO> queryByInfoauthId(String infoauthId);

  /**
   * desc:根据普通索引Name获取数据:info_service_orgcheck.<br>
   *
   * @param name name
   * @return List<InfoServiceOrgcheckDO>
   */
  public List<InfoServiceOrgcheckDO> queryByName(String name);

  @Select("select * from info_service_orgcheck where infoauth_id = #{infoAuthId}")
  InfoServiceOrgcheckDO getByInfoAuthId(@Param("infoAuthId") String infoAuthId);

  /**
   * 快捷签查询企业的三要素比对记录
   * @param authInfoQueryRequestDTO 请求参数
   * @return 三要素比对记录
   */
  @Select({
          "<script>",
          "select * from info_service_orgcheck t1 inner join info_service t2 on t1.infoauth_id = t2.infoauth_id",
          "where  1=1 ",
          "<if test='infoAuthId != null'> AND t1.infoauth_id = #{infoAuthId} </if>",
          "<if test='appId != null'> AND t2.app_id= #{appId} </if>",
          "<if test='serviceStatus != null'> AND t1.service_status = #{serviceStatus} </if>",
          "<if test='modifyTime != null'> AND t1.modify_time between 0 AND #{modifyTime} </if>",
          "<if test='codeORG != null||codeUSC != null||codeREG != null'> AND </if>",
          "<trim prefix='(' prefixOverrides='OR'>",
          "<if test='codeORG != null'> OR t1.codeORG = #{codeORG} </if>",
          "<if test='codeUSC != null'> OR t1.codeUSC = #{codeUSC} </if>",
          "<if test='codeREG != null'> OR t1.codeREG = #{codeREG} </if>",
          "<if test='codeORG != null||codeUSC != null||codeREG != null'> ) </if>",
          "</trim>",
          "order by t1.modify_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<InfoServiceOrgcheckDO> quickSignQueryListByOrgCheck(AuthInfoQueryRequestDTO authInfoQueryRequestDTO);
}
