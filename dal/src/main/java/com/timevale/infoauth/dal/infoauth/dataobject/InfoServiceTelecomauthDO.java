package com.timevale.infoauth.dal.infoauth.dataobject;

import java.util.Date;

/**
 * The table. info_service_telecomauth
 *
 * <AUTHOR> Kunpeng
 */
public class InfoServiceTelecomauthDO extends InfoServiceAuthDO {

  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;
  /** id 主键. */
  private Long id;
  /** idno 用户输入身份证号. */
  private String idno;
  /** name 用户输入姓名. */
  private String name;
  /** mobile 用户输入手机号. */
  private String mobile;
  /** infoauthId 服务对象ID. */
  private String infoauthId;
  /** providerId 三要素供应商. */
  private String providerId;
  /** billorderId 计费订单ID. */
  private String billorderId;
  /** providerResult 供应商返回结果. */
  private String providerResult;
  /** result 认证结果. */
  private Integer result;
  /** serviceStatus 服务记录状态. */
  private Integer serviceStatus;
  private Long errId;

  /** Set createTime 记录创建时间. */
  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  /**
   * Get createTime 记录创建时间.
   *
   * @return the string
   */
  public Date getCreateTime() {
    return createTime;
  }

  /** Set modifyTime 记录修改时间. */
  public void setModifyTime(Date modifyTime) {
    this.modifyTime = modifyTime;
  }

  /**
   * Get modifyTime 记录修改时间.
   *
   * @return the string
   */
  public Date getModifyTime() {
    return modifyTime;
  }

  /** Set id 主键. */
  public void setId(Long id) {
    this.id = id;
  }

  /**
   * Get id 主键.
   *
   * @return the string
   */
  public Long getId() {
    return id;
  }

  /** Set idno 用户输入身份证号. */
  public void setIdno(String idno) {
    this.idno = idno;
  }

  /**
   * Get idno 用户输入身份证号.
   *
   * @return the string
   */
  public String getIdno() {
    return idno;
  }

  /** Set name 用户输入姓名. */
  public void setName(String name) {
    this.name = name;
  }

  /**
   * Get name 用户输入姓名.
   *
   * @return the string
   */
  public String getName() {
    return name;
  }

  /** Set mobile 用户输入手机号. */
  public void setMobile(String mobile) {
    this.mobile = mobile;
  }

  /**
   * Get mobile 用户输入手机号.
   *
   * @return the string
   */
  public String getMobile() {
    return mobile;
  }

  /** Set infoauthId 服务对象ID. */
  public void setInfoauthId(String infoauthId) {
    this.infoauthId = infoauthId;
  }

  /**
   * Get infoauthId 服务对象ID.
   *
   * @return the string
   */
  public String getInfoauthId() {
    return infoauthId;
  }

  /** Set providerId 三要素供应商. */
  public void setProviderId(String providerId) {
    this.providerId = providerId;
  }

  /**
   * Get providerId 三要素供应商.
   *
   * @return the string
   */
  public String getProviderId() {
    return providerId;
  }

  /** Set providerResult 供应商返回结果. */
  public void setProviderResult(String providerResult) {
    this.providerResult = providerResult;
  }

  /**
   * Get providerResult 供应商返回结果.
   *
   * @return the string
   */
  public String getProviderResult() {
    return providerResult;
  }

  /** Set result 认证结果. */
  public void setResult(Integer result) {
    this.result = result;
  }

  /**
   * Get result 认证结果.
   *
   * @return the string
   */
  public Integer getResult() {
    return result;
  }

  /** Set serviceStatus 服务记录状态. */
  public void setServiceStatus(Integer serviceStatus) {
    this.serviceStatus = serviceStatus;
  }

  /**
   * Get serviceStatus 服务记录状态.
   *
   * @return the string
   */
  public Integer getServiceStatus() {
    return serviceStatus;
  }

  public Long getErrId() {
    return errId;
  }

  public void setErrId(Long errId) {
    this.errId = errId;
  }
}
