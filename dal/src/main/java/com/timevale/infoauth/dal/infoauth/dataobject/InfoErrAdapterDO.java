package com.timevale.infoauth.dal.infoauth.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/** 供应商错误码映射表 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InfoErrAdapterDO {

  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录更新时间. */
  private Date modifyTime;
  /** id 自增主键. */
  private Long id;
  /** md5 type+provider+provider_result. */
  private String bizMd5;
  /** extend 扩展字段. */
  private String extend;
  /** provider 供应商名称. */
  private String provider;
  /** adapterErrMsg 对外展示错误文案，初始值与provider_err_msg一致. */
  private String adapterErrMsg;
  /** adapterErrCode 对外展示错误码，初始值与provider_err_code一致. */
  private String adapterErrCode;
  /** providerErrMsg 供应商错误信息. */
  private String providerErrMsg;
  /** providerResult 供应商返回结果. */
//  private String providerResult; // java zip 会导致堆外内存溢出，不再进行压缩
  /** providerErrCode 供应商错误码. */
  private String providerErrCode;
  /** type 信息比对类型. */
  private Integer bizType;
}
