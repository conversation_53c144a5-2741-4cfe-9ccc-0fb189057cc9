package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.OutlierDataRecordDO;
import com.timevale.infoauth.dal.infoauth.facade.OutlierDataRecordDTO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * table outlier_data_record
 * */
public interface OutlierDataRecordDAO {
  @Insert(
      "INSERT INTO `outlier_data_record` "
          + "(`infoauth_id`, `type`, `event_type`, `request_date`, `status`,`create_time`,`modify_time`)"
          + " VALUES (#{infoauthId}, #{type}, #{eventType}, #{requestDate}, #{status}, #{createTime}, #{modifyTime})")
  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  Integer saveOutlierDataRecord(OutlierDataRecordDO entity);

  @Update(
          "<script>"
                  + "UPDATE outlier_data_record "
                  + "<trim prefix='set' suffixOverrides=','>"
                  + "<if test='infoauthId != null'>infoauth_id = #{infoauthId}, </if>"
                  + "<if test='type != null'>type = #{type} ,</if>"
                  + "<if test='eventType != null'>event_type = #{eventType} ,</if>"
                  + "<if test='requestDate != null'>request_date = #{requestDate} ,</if>"
                  + "<if test='status != null'>status = #{status} ,</if>"
                  + "</trim>"
                  + "WHERE id = #{id,jdbcType=BIGINT}"
                  + "</script>")
  Integer updateById(OutlierDataRecordDO entity);

  @Update("update outlier_data_record set status=#{status} where id = #{id}")
  void updateStatus(@Param("id") Long id, @Param("status") Integer status);

  @Select("select * from outlier_data_record where infoauth_id = #{infoAuthId}")
  OutlierDataRecordDO getByInfoAuthId(@Param("infoAuthId") String infoAuthId);

  /**
   * desc:查询list
   *
   * @return List<OutlierDataRecordDO>
   */
  @Select({
          "<script>",
          "select * from outlier_data_record",
          "where  1=1 ",
          "<if test='infoauthId != null'> AND infoauth_id = #{infoauthId} </if>",
          "<if test='type != null'> AND type = #{type} </if>",
          "<if test='eventType != null'> AND event_type = #{eventType} </if>",
          "<if test='requestDate != null'> AND request_date = #{requestDate} </if>",
          "<if test='status != null'> AND status = #{status} </if>",
          "<if test='startTime != null'> AND create_time between #{startTime} and #{endTime} </if>",
          "order by modify_time desc limit #{pageIndex},#{pageSize}",
          "</script>"})
  List<OutlierDataRecordDO> getOutlierDataRecordDOList(OutlierDataRecordDTO entity);

  /**
   * desc:查询符合条件的数量
   *
   * @return int
   */
  @Select({
          "<script>",
          "select count(id) from outlier_data_record",
          "where  1=1 ",
          "<if test='infoauthId != null'> AND infoauth_id = #{infoauthId} </if>",
          "<if test='type != null'> AND type = #{type} </if>",
          "<if test='eventType != null'> AND event_type = #{eventType} </if>",
          "<if test='requestDate != null'> AND request_date = #{requestDate} </if>",
          "<if test='status != null'> AND status = #{status} </if>",
          "<if test='startTime != null'> AND create_time between #{startTime} and #{endTime} </if>",
          "</script>"})
  Integer countOutlierDataRecordDOList(OutlierDataRecordDTO entity);

  /**
   * desc:查询最大id
   *
   * @return long
   */
  @Select({
          "<script>",
          "select max(id) from outlier_data_record",
          "where  1=1 ",
          "<if test='infoauthId != null'> AND infoauth_id = #{infoauthId} </if>",
          "<if test='type != null'> AND type = #{type} </if>",
          "<if test='eventType != null'> AND event_type = #{eventType} </if>",
          "<if test='requestDate != null'> AND request_date = #{requestDate} </if>",
          "<if test='status != null'> AND status = #{status} </if>",
          "<if test='startTime != null'> AND create_time between #{startTime} and #{endTime} </if>",
          "</script>"})
  Long getMaxIdOutlierDataRecordList(OutlierDataRecordDTO entity);

  /**
   * desc:查询最小id
   *
   * @return long
   */
  @Select({
          "<script>",
          "select min(id) from outlier_data_record",
          "where  1=1 ",
          "<if test='infoauthId != null'> AND infoauth_id = #{infoauthId} </if>",
          "<if test='type != null'> AND type = #{type} </if>",
          "<if test='eventType != null'> AND event_type = #{eventType} </if>",
          "<if test='requestDate != null'> AND request_date = #{requestDate} </if>",
          "<if test='status != null'> AND status = #{status} </if>",
          "<if test='startTime != null'> AND create_time between #{startTime} and #{endTime} </if>",
          "</script>"})
  Long getMinIdOutlierDataRecordList(OutlierDataRecordDTO entity);

  /**
   * desc:查询list进行数据补偿
   *
   * @return InfoServiceDO
   */
  @Select({
          "<script>",
          "select * from outlier_data_record",
          "where  1=1 ",
          "<if test='infoauthId != null'> AND infoauth_id = #{infoauthId} </if>",
          "<if test='type != null'> AND type = #{type} </if>",
          "<if test='eventType != null'> AND event_type = #{eventType} </if>",
          "<if test='requestDate != null'> AND request_date = #{requestDate} </if>",
          "<if test='status != null'> AND status = #{status} </if>",
          "<if test='startId != null'> AND id between #{startId} and #{endId} </if>",
          "order by modify_time desc",
          "</script>"})
  List<OutlierDataRecordDO> getOutlierDataRecordListForUpdate(OutlierDataRecordDTO entity);
}
