package com.timevale.infoauth.service.model;

import com.timevale.infoauth.service.enums.DigestAlgorithmModel;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

public enum DigestProivder {
  MD5("MD5", DigestAlgorithmModel.MD5),
  SHA1("SHA-1", DigestAlgorithmModel.SHA1),
  SHA256("SHA-256", DigestAlgorithmModel.SHA256);

  private String disc;
  private DigestAlgorithmModel digestType;
  private static final Map<DigestAlgorithmModel, DigestProivder> models = new HashMap();

  private DigestProivder(String disc, DigestAlgorithmModel digestType) {
    this.disc = disc;
    this.digestType = digestType;
  }

  public static DigestProivder from(DigestAlgorithmModel model) {
    return (DigestProivder) models.get(model);
  }

  public MessageDigest provider() {
    try {
      return MessageDigest.getInstance(this.disc);
    } catch (NoSuchAlgorithmException var2) {
      var2.printStackTrace();
      return null;
    }
  }

  static {
    DigestProivder[] var0 = values();
    int var1 = var0.length;

    for (int var2 = 0; var2 < var1; ++var2) {
      DigestProivder provider = var0[var2];
      models.put(provider.digestType, provider);
    }
  }
}
