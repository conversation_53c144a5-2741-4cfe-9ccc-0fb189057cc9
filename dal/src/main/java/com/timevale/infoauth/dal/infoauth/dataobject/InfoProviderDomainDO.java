package com.timevale.infoauth.dal.infoauth.dataobject;

import lombok.Data;

import java.util.Date;

@Data
public class InfoProviderDomainDO {

  /** id 自增主键. */
  private int id;
  /** metaName 服务商名称. */
  private String pName;
  /** bizType 业务类型[1企业三要素,2企业二要素]. */
  private Integer bizType;
  /** pStatus 供应商状态[1上架,0下架]. */
  private Integer pStatus;
  /** domainManager 管理子域. */
  private String domainManager;
  /** domainClient 客户端子域. */
  private String domainClient;
  /** domainSelect 选举子域. */
  private String domainSelect;
  /** domainRetry 重试子域. */
  private String domainRetry;
  /** domainResource 资源子域. */
  private String domainResource;
  /** domainCollect 采集统计子域. */
  private String domainCollect;
  /** domainMonitor 监控子域. */
  private String domainMonitor;
  /** domainRecommend 推荐计算子域. */
  private String domainRecommend;
  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;

  /**
   * 供应商质量标签
   */
  private String qualityTag;
}
