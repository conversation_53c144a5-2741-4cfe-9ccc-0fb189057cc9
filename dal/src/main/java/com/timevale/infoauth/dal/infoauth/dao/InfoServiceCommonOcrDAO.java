package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceCommonOcrDO;
import org.apache.ibatis.annotations.*;

public interface InfoServiceCommonOcrDAO {
  /**
   * desc:插入表:info_service_org.<br>
   *
   * @param entity entity
   * @return int
   */
  @Insert(
      "INSERT INTO `info_service_common_ocr` "
          + "(`id`, `app_id`,`type`, `infoauth_id`, `photo_idcard_face`, `photo_idcard_emblem`, `create_time`, `modify_time`) "
          + "VALUES "
          + "(#{id}, #{appId}, #{type},#{infoauthId}, #{photoIdcardFace}, #{photoIdcardEmblem}, now(), now())")
  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  int insert(InfoServiceCommonOcrDO entity);

  @Update("update info_service_common_ocr set service_status=#{status} where id = #{id}")
  void updateStatus(@Param("id") Integer id, @Param("status") Integer status);

  @Update(
      "update info_service_common_ocr set "
          + "ocr_name=#{ocrName},ocr_idno=#{ocrIdno},ocr_birth=#{ocrBirth},ocr_address=#{ocrAddress},ocr_nation=#{ocrNation},ocr_sex=#{ocrSex},"
          + "ocr_start_time=#{ocrStartTime},ocr_end_time=#{ocrEndTime},ocr_organ=#{ocrOrgan},provider_result=#{providerResult},provider_request_id=#{providerRequestId},provider_id=#{providerId},`result`=#{result},`service_status`= #{serviceStatus} "
          + "where id = #{id}")
  void updateOcrResult(InfoServiceCommonOcrDO entity);

  @Update(
      "update info_service_common_ocr set "
          + "provider_result=#{providerResult},provider_request_id=#{providerRequestId},provider_id=#{providerId},`ocr_extend`=#{ocrExtend},`service_status`= #{serviceStatus} "
          + "where id = #{id}")
  void updateOcrServiceInfo(InfoServiceCommonOcrDO entity);

  @Select("select * from info_service_common_ocr where infoauth_id = #{infoauthId}")
  InfoServiceCommonOcrDO queryByInfoAuthId(@Param("infoauthId") String infoauthId);
}
