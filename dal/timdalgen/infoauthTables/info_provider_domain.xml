<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="info_provider_domain" physicalName="info_provider_domain" remark="info_provider_domain">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,META_URL,META_DESC,META_NAME,DOMAIN_RETRY,META_ACCOUNT,META_SIGN_KEY,DOMAIN_CLIENT,DOMAIN_SELECT,META_PASSWORD,DOMAIN_COLLECT,DOMAIN_MANAGER,DOMAIN_MONITOR,META_VERIFY_KEY,DOMAIN_RESOURCE,DOMAIN_RECOMMEND,BIZ_TYPE,P_STATUS,CREATE_TIME,MODIFY_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.META_URL,sf.META_DESC,sf.META_NAME,sf.DOMAIN_RETRY,sf.META_ACCOUNT,sf.META_SIGN_KEY,sf.DOMAIN_CLIENT,sf.DOMAIN_SELECT,sf.META_PASSWORD,sf.DOMAIN_COLLECT,sf.DOMAIN_MANAGER,sf.DOMAIN_MONITOR,sf.META_VERIFY_KEY,sf.DOMAIN_RESOURCE,sf.DOMAIN_RECOMMEND,sf.BIZ_TYPE,sf.P_STATUS,sf.CREATE_TIME,sf.MODIFY_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:info_provider_domain">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO info_provider_domain(
            ID
            ,META_URL
            ,META_DESC
            ,META_NAME
            ,DOMAIN_RETRY
            ,META_ACCOUNT
            ,META_SIGN_KEY
            ,DOMAIN_CLIENT
            ,DOMAIN_SELECT
            ,META_PASSWORD
            ,DOMAIN_COLLECT
            ,DOMAIN_MANAGER
            ,DOMAIN_MONITOR
            ,META_VERIFY_KEY
            ,DOMAIN_RESOURCE
            ,DOMAIN_RECOMMEND
            ,BIZ_TYPE
            ,P_STATUS
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES(
             null
            , #{metaUrl,jdbcType=VARCHAR}
            , #{metaDesc,jdbcType=VARCHAR}
            , #{metaName,jdbcType=VARCHAR}
            , #{domainRetry,jdbcType=LONGVARCHAR}
            , #{metaAccount,jdbcType=VARCHAR}
            , #{metaSignKey,jdbcType=LONGVARCHAR}
            , #{domainClient,jdbcType=LONGVARCHAR}
            , #{domainSelect,jdbcType=LONGVARCHAR}
            , #{metaPassword,jdbcType=VARCHAR}
            , #{domainCollect,jdbcType=LONGVARCHAR}
            , #{domainManager,jdbcType=LONGVARCHAR}
            , #{domainMonitor,jdbcType=LONGVARCHAR}
            , #{metaVerifyKey,jdbcType=LONGVARCHAR}
            , #{domainResource,jdbcType=LONGVARCHAR}
            , #{domainRecommend,jdbcType=LONGVARCHAR}
            , #{bizType,jdbcType=TINYINT}
            , #{pStatus,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{modifyTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:info_provider_domain">
        INSERT INTO info_provider_domain(
            ID
            ,META_URL
            ,META_DESC
            ,META_NAME
            ,DOMAIN_RETRY
            ,META_ACCOUNT
            ,META_SIGN_KEY
            ,DOMAIN_CLIENT
            ,DOMAIN_SELECT
            ,META_PASSWORD
            ,DOMAIN_COLLECT
            ,DOMAIN_MANAGER
            ,DOMAIN_MONITOR
            ,META_VERIFY_KEY
            ,DOMAIN_RESOURCE
            ,DOMAIN_RECOMMEND
            ,BIZ_TYPE
            ,P_STATUS
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.metaUrl,jdbcType=VARCHAR}
                , #{item.metaDesc,jdbcType=VARCHAR}
                , #{item.metaName,jdbcType=VARCHAR}
                , #{item.domainRetry,jdbcType=LONGVARCHAR}
                , #{item.metaAccount,jdbcType=VARCHAR}
                , #{item.metaSignKey,jdbcType=LONGVARCHAR}
                , #{item.domainClient,jdbcType=LONGVARCHAR}
                , #{item.domainSelect,jdbcType=LONGVARCHAR}
                , #{item.metaPassword,jdbcType=VARCHAR}
                , #{item.domainCollect,jdbcType=LONGVARCHAR}
                , #{item.domainManager,jdbcType=LONGVARCHAR}
                , #{item.domainMonitor,jdbcType=LONGVARCHAR}
                , #{item.metaVerifyKey,jdbcType=LONGVARCHAR}
                , #{item.domainResource,jdbcType=LONGVARCHAR}
                , #{item.domainRecommend,jdbcType=LONGVARCHAR}
                , #{item.bizType,jdbcType=TINYINT}
                , #{item.pStatus,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.modifyTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:info_provider_domain">
        <![CDATA[
        UPDATE info_provider_domain
        SET
            META_URL        = #{metaUrl,jdbcType=VARCHAR}
            ,META_DESC       = #{metaDesc,jdbcType=VARCHAR}
            ,META_NAME       = #{metaName,jdbcType=VARCHAR}
            ,DOMAIN_RETRY    = #{domainRetry,jdbcType=LONGVARCHAR}
            ,META_ACCOUNT    = #{metaAccount,jdbcType=VARCHAR}
            ,META_SIGN_KEY   = #{metaSignKey,jdbcType=LONGVARCHAR}
            ,DOMAIN_CLIENT   = #{domainClient,jdbcType=LONGVARCHAR}
            ,DOMAIN_SELECT   = #{domainSelect,jdbcType=LONGVARCHAR}
            ,META_PASSWORD   = #{metaPassword,jdbcType=VARCHAR}
            ,DOMAIN_COLLECT  = #{domainCollect,jdbcType=LONGVARCHAR}
            ,DOMAIN_MANAGER  = #{domainManager,jdbcType=LONGVARCHAR}
            ,DOMAIN_MONITOR  = #{domainMonitor,jdbcType=LONGVARCHAR}
            ,META_VERIFY_KEY = #{metaVerifyKey,jdbcType=LONGVARCHAR}
            ,DOMAIN_RESOURCE = #{domainResource,jdbcType=LONGVARCHAR}
            ,DOMAIN_RECOMMEND = #{domainRecommend,jdbcType=LONGVARCHAR}
            ,BIZ_TYPE        = #{bizType,jdbcType=TINYINT}
            ,P_STATUS        = #{pStatus,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,MODIFY_TIME     = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=INTEGER}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:info_provider_domain">
        <![CDATA[
        DELETE FROM info_provider_domain
        WHERE
            ID = #{id,jdbcType=INTEGER}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:info_provider_domain">
        SELECT *
        FROM info_provider_domain
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=INTEGER}
        ]]>
    </operation>

    <!-- 根据唯一约束操作数据 -->
    <operation name="updateByUnqNameType" paramtype="object" remark="根据唯一约束UnqNameType更新表:info_provider_domain">
        <![CDATA[
        UPDATE info_provider_domain
        SET
            META_URL        = #{metaUrl,jdbcType=VARCHAR}
            ,META_DESC       = #{metaDesc,jdbcType=VARCHAR}
            ,DOMAIN_RETRY    = #{domainRetry,jdbcType=LONGVARCHAR}
            ,META_ACCOUNT    = #{metaAccount,jdbcType=VARCHAR}
            ,META_SIGN_KEY   = #{metaSignKey,jdbcType=LONGVARCHAR}
            ,DOMAIN_CLIENT   = #{domainClient,jdbcType=LONGVARCHAR}
            ,DOMAIN_SELECT   = #{domainSelect,jdbcType=LONGVARCHAR}
            ,META_PASSWORD   = #{metaPassword,jdbcType=VARCHAR}
            ,DOMAIN_COLLECT  = #{domainCollect,jdbcType=LONGVARCHAR}
            ,DOMAIN_MANAGER  = #{domainManager,jdbcType=LONGVARCHAR}
            ,DOMAIN_MONITOR  = #{domainMonitor,jdbcType=LONGVARCHAR}
            ,META_VERIFY_KEY = #{metaVerifyKey,jdbcType=LONGVARCHAR}
            ,DOMAIN_RESOURCE = #{domainResource,jdbcType=LONGVARCHAR}
            ,DOMAIN_RECOMMEND = #{domainRecommend,jdbcType=LONGVARCHAR}
            ,P_STATUS        = #{pStatus,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,MODIFY_TIME     = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE
            META_NAME       = #{metaName,jdbcType=VARCHAR}
            AND BIZ_TYPE        = #{bizType,jdbcType=TINYINT}
        ]]>
    </operation>

    <operation name="deleteByUnqNameType" remark="根据唯一约束UnqNameType删除数据:info_provider_domain">
        <![CDATA[
        DELETE FROM info_provider_domain
        WHERE
            META_NAME       = #{metaName,jdbcType=VARCHAR}
            AND BIZ_TYPE        = #{bizType,jdbcType=TINYINT}
        ]]>
    </operation>

    <operation name="getByUnqNameType" multiplicity="one" remark="根据唯一约束UnqNameType获取数据:info_provider_domain">
        SELECT *
        FROM info_provider_domain
        WHERE
        <![CDATA[
            META_NAME       = #{metaName,jdbcType=VARCHAR}
            AND BIZ_TYPE        = #{bizType,jdbcType=TINYINT}

        ]]>
    </operation>
</table>
