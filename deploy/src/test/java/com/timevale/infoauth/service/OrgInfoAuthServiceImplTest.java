package com.timevale.infoauth.service;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.model.output.OrgInfoAuthOutput;
import com.timevale.infoauth.service.impl.biz.impl.details.OrgInfoAuthServiceImpl;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @author: jiuchen
 * @since: 2020-05-25 13:46
 */
@JsonDataIgnore
public class OrgInfoAuthServiceImplTest extends ApplicationTest {

  @Resource OrgInfoAuthServiceImpl orgInfoAuthService;

  @Test
  public void bindErrId() {
    OrgInfoAuthOutput output = new OrgInfoAuthOutput("b4b7c8c7-61ac-4f25-b41d-c043b3f4925d");
    output.setErrId(16L);
    orgInfoAuthService.bindErrId(output);
    Assert.assertTrue(true);
    Assert.assertEquals(Optional.ofNullable(output.getErrId()), Optional.of(16L));
  }
}
