package com.timevale.infoauth.service.enums;

/**
 * 功能说明：在营状态
 *
 * @return <br>
 *     修改历史：<br>
 *     1.[2016年09月01日上午16:00] 创建方法 by hw
 */
public enum EntStatus {
  ING(OrgbsStatus.CAPMING,true ,"|状态正常|正常执业|迁出|迁入|存续|在营|在业|开业|正常|登记|确立|经营|在营在册|有效|在业在册|在营（开业）|-|"),


  REVOKE(OrgbsStatus.WITHDRAW, false,"|吊销|"),
  CLOSE(OrgbsStatus.WITHDRAW, false,"|责令关闭|"),

  CANCEL(OrgbsStatus.CANCEL,false ,"|注销|"),
  NOT_ING(OrgbsStatus.WITHDRAW, false,"|非正常经营状态|"),

  OTHER(OrgbsStatus.OTHER, true,"|其他|"),

  UNKNOWN(OrgbsStatus.UNKNOWN, true,""),
  ;

  EntStatus(OrgbsStatus status, Boolean available, String name) {
    this.status = status;
    this.available = available;
    this.name = name;
  }

  private OrgbsStatus status;
  private Boolean available;
  private String name;

  public OrgbsStatus status() {
    return status;
  }

  public String getName() {
    return name;
  }

  public Boolean getAvailable() {
    return available;
  }

  public static EntStatus from(String name) {
    for (EntStatus ent : EntStatus.values()) {
      if (ent.getName().indexOf("|" + name + "|") > -1) {
        return ent;
      }
    }
    return UNKNOWN;
  }

  public static EntStatus from(OrgbsStatus status) {

    for (EntStatus ent : EntStatus.values()) {
      if (ent.status.equals(status)) {
        return ent;
      }
    }
    return UNKNOWN;
  }
}
