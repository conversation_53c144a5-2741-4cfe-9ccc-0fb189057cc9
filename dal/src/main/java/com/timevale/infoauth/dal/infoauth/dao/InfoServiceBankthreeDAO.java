package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceBankthreeDO;
import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceBankthreeExtendDO;
import com.timevale.infoauth.dal.infoauth.facade.AuthInfoQueryRequestDTO;
import com.timevale.infoauth.dal.infoauth.facade.AuthResultQueryRecordDTO;
import org.apache.ibatis.annotations.*;

import java.sql.Timestamp;
import java.util.List;

public interface InfoServiceBankthreeDAO {
  @Insert(
          "INSERT INTO `info_service_bankthree` "
                  + "(`infoauth_id`, `name`, `idno`, `cardno`,`provider_id`,`provider_result`,`result`, `service_status`,`create_time`,`modify_time`)"
                  + " VALUES (#{infoauthId}, #{name},#{idno}, #{cardno},#{providerId},#{providerResult},#{result},#{serviceStatus}, now(), now())")
  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  int saveApply(InfoServiceBankthreeDO entity);

  @Update(
      "UPDATE `info_service_bankthree`"
          + " set `infoauth_id`=#{infoauthId},`name`=#{name},`idno`=#{idno},`bank`=#{bank},`cardno`=#{cardno},`provider_id`=#{providerId},`provider_result`=#{providerResult},`result`=#{result},`service_status`=#{serviceStatus}"
          + " WHERE `id`=#{id}")
  void update(InfoServiceBankthreeDO infoServiceBankauthDO);

  @Update("update info_service_bankthree set service_status=#{status} where id = #{id}")
  void updateStatus(@Param("id") Long id, @Param("status") Integer status);

  @Select("select * from info_service_bankthree where infoauth_id = #{infoAuthId}")
  InfoServiceBankthreeDO getByInfoAuthId(@Param("infoAuthId") String infoAuthId);

  @Select("select * from info_service_bankthree where idno = #{idno} and modify_time<=#{modifyTime} order by id desc limit #{currIndex},#{pageSize}")
  List<InfoServiceBankthreeDO> getByUpdateTimeAndIdNo(@Param("idno") String idno, @Param("modifyTime") Timestamp modifyTime, @Param("currIndex") Integer currIndex, @Param("pageSize") Integer pageSize);

//  @Select({"<script> "+
//          "select * from info_service_bankthree where 1=1 " +
//          "<when test=\"infoAuthId !=null \"> and infoauth_id = #{infoAuthId} </when>" +
//          "<when test=\"idno !=null \"> and idno = #{idno} </when>" +
//          "<when test=\"serviceStatus !=null \"> and service_status = #{serviceStatus} </when>" +
//          "<when test=\"modifyTime !=null \"> and modify_time = #{modifyTime} </when>" +
//          "order by id desc limit #{currIndex},#{pageSize}"+
//          "</script>"})
  /**
   * desc:专为快捷签出证查询使用
   *
   * @param authInfoQueryRequestDTO
   * @return List<InfoServiceBankthreeDO>
   */
  @Select({
          "<script>",
          "select * from info_service_bankthree t1 inner join info_service t2 on t1.infoauth_id = t2.infoauth_id",
          "where  1=1 ",
          "<if test='infoAuthId != null'> and t1.infoauth_id = #{infoAuthId} </if>",
          "<if test='appId != null'> and t2.app_id= #{appId} </if>",
          "<if test='idno != null'> and t1.idno = #{idno} </if>",
          "<if test='serviceStatus != null'> and t1.service_status = #{serviceStatus} </if>",
          "<if test='updateTime != null'> and t1.modify_time between 0 and #{updateTime} </if>",
//          "<if test='modifyTime != null'> and modify_time &lt;= #{modifyTime} </if>",
          "order by t1.modify_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<InfoServiceBankthreeExtendDO> queryByUpdateTimeAndIdNo(AuthInfoQueryRequestDTO authInfoQueryRequestDTO);

  @Select({
          "<script>",
          "select * from info_service_bankthree t1 inner join info_service t2 on t1.infoauth_id = t2.infoauth_id",
          "where  1=1 ",
          "<if test='infoAuthId != null'> and t1.infoauth_id = #{infoAuthId} </if>",
          "<if test='appId != null'> and t2.app_id= #{appId} </if>",
          "<if test='idno != null'> and t1.idno = #{idno} </if>",
          "<if test='serviceStatus != null'> and t1.service_status = #{serviceStatus} </if>",
          "<if test='modifyTime != null'> and t1.modify_time between 0 and #{modifyTime} </if>",
          "order by t1.modify_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<InfoServiceBankthreeExtendDO> quickSignQueryListByBankThree(AuthInfoQueryRequestDTO authInfoQueryRequestDTO);

  /**
   * desc:专为信息比对记录查询使用
   *
   * @param authInfoQueryRequestDTO
   * @return List<InfoServiceBankthreeDO>
   */
  @Select({
          "<script>",
          "select * from info_service_bankthree t1 inner join info_service t2 on t1.infoauth_id = t2.infoauth_id",
          "where  1=1 ",
          "<if test='infoAuthId != null'> and t1.infoauth_id = #{infoAuthId} </if>",
          "<if test='appId != null'> and t2.app_id= #{appId} </if>",
          "<if test='idno != null'> and t1.idno = #{idno} </if>",
          "<if test='serviceStatus != null'> and t1.service_status = #{serviceStatus} </if>",
          "<if test='startTime != null'> and t1.modify_time between #{startTime} and #{endTime} </if>",
          "order by t1.modify_time desc limit #{currIndex},#{pageSize}",
          "</script>"})
  List<InfoServiceBankthreeExtendDO> queryForInfoAuthRecord(AuthInfoQueryRequestDTO authInfoQueryRequestDTO);

  @Update(
          "UPDATE `info_service_bankthree` SET err_id = #{errId} WHERE `infoauth_id` = #{infoauthId}")
  void bindErrId(@Param("infoauthId") String infoauthId, @Param("errId") Long errId);


  /**
   * desc:根据条件查询企业信息比对记录
   *
   * @return InfoServiceDO
   */
  @Select({
          "<script>",
          "select * from info_service t1 inner join info_service_bankthree t2 on t1.infoauth_id = t2.infoauth_id",
          "where  1=1 ",
          "<if test='certName != null'> and t2.name = #{certName} </if>",
          "<if test='serviceStatus != null'> and t2.service_status = #{serviceStatus} </if>",
          "<if test='certNo != null'> and t2.idno = #{certNo} </if>",
          "<if test='infoAuthServiceType != null'> and t1.type = #{infoAuthServiceType} </if>",
          "<if test='appId != null'> AND t1.app_id = #{appId} </if>",
          "<if test='startTime != null'> AND t1.modify_time between #{startTime} and #{endTime} </if>",
          "<if test='infoAuthId != null'> AND t1.infoauth_id = #{infoAuthId} </if>",
          "order by t1.modify_time desc limit #{pageIndex},#{pageSize}",
          "</script>"})
  List<InfoServiceBankthreeDO> getPsnBank3ByAuthResultQuery(AuthResultQueryRecordDTO authResultQueryRecordDTO);

  /**
   * desc:根据条件查询企业信息比对记录
   *
   * @return InfoServiceDO
   */
  @Select({
          "<script>",
          "select count(*) from info_service t1 inner join info_service_bankthree t2 on t1.infoauth_id = t2.infoauth_id",
          "where  1=1 ",
          "<if test='certName != null'> and t2.name = #{certName} </if>",
          "<if test='serviceStatus != null'> and t2.service_status = #{serviceStatus} </if>",
          "<if test='certNo != null'> and t2.idno = #{certNo} </if>",
          "<if test='infoAuthServiceType != null'> and t1.type = #{infoAuthServiceType} </if>",
          "<if test='appId != null'> AND t1.app_id = #{appId} </if>",
          "<if test='startTime != null'> AND t1.modify_time between #{startTime} and #{endTime} </if>",
          "<if test='infoAuthId != null'> AND t1.infoauth_id = #{infoAuthId} </if>",
          "</script>"})
  int getPsnBank3ByAuthResultQueryCount(AuthResultQueryRecordDTO authResultQueryRecordDTO);
}
