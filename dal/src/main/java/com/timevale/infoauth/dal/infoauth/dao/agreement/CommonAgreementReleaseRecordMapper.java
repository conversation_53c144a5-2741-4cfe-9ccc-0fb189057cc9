package com.timevale.infoauth.dal.infoauth.dao.agreement;

import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementInfoDO;
import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementReleaseRecordDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 通用协议发布记录的Mapper接口
 * 负责对common_agreement_release_record表进行数据库操作
 */
@Mapper
public interface CommonAgreementReleaseRecordMapper {

    /**
     * 插入一条协议发布记录
     *
     * @param recordDO 协议发布记录对象，包含要插入的记录的各种信息
     * @return 插入记录的ID，表示插入操作的结果
     */
    @Insert(
            "<script>INSERT INTO common_agreement_release_record(enable_status,release_version, release_record_id, agreement_id,short_name,full_name,lang,release_remark,content,content_type,operator,creator"
                    + ") VALUES(\n"
                    + "            #{enableStatus,jdbcType=TINYINT}\n"
                    + "            , #{releaseVersion,jdbcType=VARCHAR}\n"
                    + "            , #{releaseRecordId,jdbcType=VARCHAR}\n"
                    + "            , #{agreementId,jdbcType=VARCHAR}\n"
                    + "            , #{shortName,jdbcType=VARCHAR}\n"
                    + "            , #{fullName,jdbcType=VARCHAR}\n"
                    + "            , #{lang,jdbcType=VARCHAR}\n"
                    + "            , #{releaseRemark,jdbcType=VARCHAR}\n"
                    + "            , #{content,jdbcType=VARCHAR}\n"
                    + "            , #{contentType,jdbcType=VARCHAR}\n"
                    + "            , #{operator,jdbcType=VARCHAR}\n"
                    + "            , #{creator,jdbcType=VARCHAR}\n"
                    + ")"
                    + "</script>")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(CommonAgreementReleaseRecordDO recordDO);

    /**
     * 根据ID更新协议发布记录
     *
     * @param entity 包含更新信息的协议发布记录对象
     */
    @Update("<script> UPDATE common_agreement_release_record <set> " +
            "<if test='enableStatus != null'>  enable_status = #{enableStatus} ,</if>" +
            "<if test='agreementId != null'>  agreement_id = #{agreementId} ,</if>" +
            "<if test='shortName != null'>  short_name = #{shortName} ,</if>" +
            "<if test='fullName != null'>  full_name = #{fullName} ,</if>" +
            "<if test='lang != null'>  lang = #{lang} ,</if>" +
            "<if test='releaseRemark != null'>  release_remark = #{releaseRemark}, </if>" +
            "<if test='releaseTime != null'>  release_time = #{releaseTime,jdbcType=TIMESTAMP} ,</if>" +
            "<if test='releaseVersion != null'>  release_version = #{releaseVersion}, </if>" +
            "<if test='content != null'>  content = #{content} ,</if>" +
            "<if test='contentType != null'>  content_type = #{contentType}, </if>" +
            "<if test='operator != null'>  operator = #{operator}, </if>" +
            "<if test='creator != null'>  creator = #{creator}, </if>" +
            "  modify_time = now() " +
            "  </set> " +
            " where id = #{id} </script>")
    void updateById(CommonAgreementReleaseRecordDO entity);

    /**
     * 根据协议ID和版本获取协议发布记录
     *
     * @param agreementId 协议ID
     * @param releaseVersion 协议版本
     * @return 匹配条件的协议发布记录列表
     */
    @Select("select * from common_agreement_release_record where agreement_id = #{agreementId} " +
            " and release_version = #{releaseVersion} ORDER BY id DESC limit 100 ")
    List<CommonAgreementReleaseRecordDO> getByAgreementIdAndVersion(@Param("agreementId") String agreementId,
                                                                    @Param("releaseVersion") String releaseVersion);

    /**
     * 批量获取协议发布记录
     *
     * @param releaseRecordIdList 协议发布记录ID列表
     * @return 匹配ID列表的协议发布记录列表
     */
    @Select(
            "<script>"
                    + " select * from common_agreement_release_record "
                    + "WHERE  "
                    + "  release_record_id IN"
                    + "<foreach item='item' index='index' collection='releaseRecordIdList' open='(' separator=',' close=')'>"
                    + "#{item}"
                    + "</foreach> limit 100"
                    + "</script>")
    List<CommonAgreementInfoDO>  getBatchAgreementId(
            @Param("releaseRecordIdList") List<String> releaseRecordIdList);

    /**
     * 根据发布记录ID获取协议发布记录
     *
     * @param releaseRecordId 发布记录ID
     * @return 匹配的协议发布记录
     */
    @Select("select * from common_agreement_release_record where   release_record_id = #{releaseRecordId}  limit 1 ")
    CommonAgreementReleaseRecordDO getByReleaseRecordId(@Param("releaseRecordId") String releaseRecordId);

    /**
     * 分页获取协议发布记录列表
     *
     * @param agreementId 协议ID
     * @param pageIndex 页码
     * @param pageSize 页大小
     * @return 分页后的协议发布记录列表
     */
    @Select(
            "<script>"
                    + "SELECT * FROM `common_agreement_release_record` WHERE agreement_id = #{agreementId} "
                    + " ORDER BY id DESC LIMIT #{pageIndex},#{pageSize}"
                    + "</script>")
    List<CommonAgreementReleaseRecordDO> getListPages(
            @Param("agreementId") String agreementId,
            @Param("pageIndex") int pageIndex,
            @Param("pageSize") int pageSize);
}
