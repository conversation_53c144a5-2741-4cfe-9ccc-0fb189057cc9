package com.timevale.infoauth.mayi;

import cn.com.antcloud.api.product.AntCloudProdRequest;
import lombok.Data;

/**
 *  AntCloudProdRequest<T>
 * <AUTHOR>
 * @since 2023/9/11 17:12
 */
@Data
public class CheckRouteTwometaRequest  extends AntCloudProdRequest<CheckRouteTwometaResponse>  {

    private String outer_order_no;
    private String cert_name;
    private String cert_no;
    private String scene;
    private String extern_param;

    public CheckRouteTwometaRequest() {
        super("di.realperson.route.twometa.check", "1.0");
        this.setProductInstanceId("PRODUCT_INSTANCE_ID");
    }



}
