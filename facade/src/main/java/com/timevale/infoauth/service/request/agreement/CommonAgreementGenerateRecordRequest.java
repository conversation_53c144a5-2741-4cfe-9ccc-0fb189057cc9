package com.timevale.infoauth.service.request.agreement;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;


/**
 * 生成协议记录
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CommonAgreementGenerateRecordRequest extends ToString {

  /** 协议发布记录ID. */
  private String releaseRecordId;

  /**
   * 模版类型的协议，填充信息
   */
  private Map<String,String> contentFillParam;
}
