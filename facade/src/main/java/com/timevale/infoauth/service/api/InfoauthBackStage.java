package com.timevale.infoauth.service.api;

import com.timevale.infoauth.service.model.SupportResult;
import com.timevale.infoauth.service.request.backstage.*;
import com.timevale.infoauth.service.response.backstage.CreateOrgMockResponse;
import com.timevale.infoauth.service.response.backstage.GetErrMsgPageListResponse;
import com.timevale.infoauth.service.response.backstage.ProviderDataList;
import com.timevale.infoauth.service.response.backstage.ProviderList;
import com.timevale.infoauth.service.response.backstage.QueryOrgMockResponse;
import com.timevale.infoauth.service.response.backstage.RouteConfigResponse;
import com.timevale.infoauth.service.response.backstage.RouteProviderConfigResponse;
import com.timevale.infoauth.service.response.backstage.ServiceTypeMapper;
import com.timevale.mandarin.common.annotation.RestClient;
import io.swagger.annotations.ApiOperation;

@RestClient(serviceId = "infoauth-service")
public interface InfoauthBackStage {
  void addRouteConfig(AddConfigRequest request);

  RouteInfoAuthResponse routeSelect(RouteInfoAuthRequest request);

  RouteInfoAuthResponse routeSelectAny(RouteInfoAuthManyRequest request);

  RouteConfigResponse routeListSelect(RouteSelectRequest request);

  void deleteRouteConfig(AppConfigDeleteRequest request);

  RouteProviderConfigResponse providerConfig();

  // 供应商表查询
  ProviderDataList queryProviderData();

  // 供应商表查询
  @Deprecated
  ProviderList queryProviderDataNoDesensitization(int type, int status);

  // 打款供应商列表查询
  ProviderList queryPayProviderList();

  // 所有业务类型
  ServiceTypeMapper serviceTypeMappers();


  ServiceTypeMapper getServiceTypesByScene(GetServiceTypeRequest request);

  /**
   * 分页获取错误原因
   *
   * @param request
   * @return
   */
  GetErrMsgPageListResponse getErrMsgPageList(GetErrMsgPageListRequest request);

  /**
   * 修改错误原因
   *
   * @param request
   */
  void modifyAdapterErrMsgById(ModifyErrMsgRequest request);

  /**
   * 删除错误原因适配记录
   *
   * @param id
   */
  void removeErrMsgById(Long id);

  /**
   * 查询单通道供应商
   *
   * @param certNo 主体（企业/个人）证件号
   * @return 供应商英文名
   */
  String querySingleRouteProvider(String certNo);

  SupportResult<CreateOrgMockResponse> createOrgMock(CreateOrgMockRequest request);

  SupportResult<QueryOrgMockResponse> queryOrgMock(QueryOrgMockRequest request);

  SupportResult<Void> deleteOrgMock(long id);


  /**
   * 根据id和账号修改供应商客户端元数据密码
   *
   * @param request
   * @return
   */
  @ApiOperation(value = "禁止业务调用  此类ops接口订正数据使用")
  SupportResult<Boolean> updateProviderDomainClientMetaPasswordByIdAndAccount(ProviderDomainClientMetaPasswordRequest request);

}
