package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.InfoProviderDomainDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface InfoProviderDomainDAO {

  @Insert(
      "<script>INSERT INTO info_provider_domain(p_name, biz_type, p_status"
          + ") VALUES(\n"
          + "            #{pName,jdbcType=VARCHAR}\n"
          + "            , #{bizType,jdbcType=VARCHAR}\n"
          + "            , #{pStatus,jdbcType=TINYINT}\n"
          + ")"
          + "</script>")
  @Options(useGeneratedKeys = true, keyProperty = "id")
  int insert(InfoProviderDomainDO infoProviderDomainDO);

  @Update("UPDATE info_provider_domain SET domain_manager=#{domainManager} where id = #{id}")
  void updateManagerDomainWithoutCore(
      @Param("id") Integer id, @Param("domainManager") String domainManager);

  @Update({
    "<script>",
    "UPDATE `info_provider_domain` ",
    "<set>",
    "<if test=\"pName != null\">",
    "p_name = #{pName},",
    "</if>",
    "<if test=\"bizType != null\">",
    "biz_type = #{bizType},",
    "</if>",
    "<if test=\"pStatus != null\">",
    "p_status = #{pStatus},",
    "</if>",
    "<if test=\"qualityTag != null\">",
          "quality_tag = #{qualityTag},",
    "</if>",
    "`domain_manager` = #{domainManager}",
    "</set>",
    " WHERE `id` = #{id}",
    "</script>",
  })
  void updateManagerDomain(InfoProviderDomainDO entity);

  @Update("UPDATE info_provider_domain SET domain_client=#{domainClient} where id = #{id}")
  void updateClientDomain(InfoProviderDomainDO entity);

  @Update("UPDATE info_provider_domain SET domain_select=#{domainSelect} where id = #{id}")
  void updateSelectDomain(InfoProviderDomainDO entity);

  @Update("UPDATE info_provider_domain SET domain_retry=#{domainRetry} where id = #{id}")
  void updateRetryDomain(InfoProviderDomainDO entity);

  @Update("UPDATE info_provider_domain SET p_status=#{status} where id = #{id}")
  void onOffline(@Param("id") Integer id, @Param("status") Integer status);

  @Select("SELECT * FROM info_provider_domain WHERE id = #{id}")
  InfoProviderDomainDO getById(@Param("id") long id);

  @Select("SELECT * FROM info_provider_domain LIMIT 200")
  List<InfoProviderDomainDO> getAllList();

  @Delete("DELETE FROM info_provider_domain WHERE id = #{id}")
  int deleteById(@Param("id") long id);

  @Select("SELECT * FROM info_provider_domain WHERE p_name = #{name} and biz_type = #{type}")
  InfoProviderDomainDO getByNameAndType(@Param("name") String name, @Param("type") int type);

  @Select("SELECT * FROM info_provider_domain WHERE biz_type = #{type} and p_status = #{status}")
  List<InfoProviderDomainDO> getListByTypeAndStatus(
      @Param("type") int type, @Param("status") int status);

  @Select("SELECT * FROM info_provider_domain WHERE biz_type = #{type}")
  List<InfoProviderDomainDO> getListByType(@Param("type") int type);

  @Select("SELECT * from info_provider_domain WHERE p_status = 1")
  List<InfoProviderDomainDO> getAllOnlineProviders();


  @Select("SELECT * from info_provider_domain WHERE p_status = #{status} and biz_type = #{type} limit 500")
  List<InfoProviderDomainDO> queryProvidersWithBizTypeAndStatus(@Param("type") int type  , @Param("status") int status);

  @Insert({
    "<script>",
    "INSERT INTO info_provider_domain (p_name, biz_type, p_status, domain_manager, domain_client, domain_select, domain_retry) VALUES",
    "<foreach collection='list' item='item' separator=','>",
    "(#{item.pName}, #{item.bizType}, #{item.pStatus}, ",
    "#{item.domainManager}, #{item.domainClient}, ",
    "#{item.domainSelect}, #{item.domainRetry})",
    "</foreach>",
    "</script>"
  })
  int insertBatch(List<InfoProviderDomainDO> list);
}
