package com.timevale.infoauth.service.enums.agreement;

import java.util.Objects;

/**
 * 协议业务类型
 *
 * <AUTHOR>
 * @since 2024/12/7 16:31
 */
public enum CommonAgreementCategoryEnum {

    identity("identity", "认证服务"),
    caCert("caCert", "证书服务"),
    ;

    private String category;
    private String desc;


    CommonAgreementCategoryEnum(String category, String desc) {
        this.category = category;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public static CommonAgreementCategoryEnum categoryOf(String category){
        for(CommonAgreementCategoryEnum showType : CommonAgreementCategoryEnum.values()){
            if(Objects.equals(showType.category, category)){
                return showType;
            }
        }
        return identity;
    }
}
