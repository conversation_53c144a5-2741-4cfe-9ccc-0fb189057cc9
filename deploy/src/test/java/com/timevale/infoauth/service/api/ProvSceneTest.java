package com.timevale.infoauth.service.api;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.impl.common.dto.OrganProvin;
import com.timevale.infoauth.service.impl.provider.client.core.EsignRequest;
import com.timevale.infoauth.service.impl.provider.client.shucifang.ShuCiFangClient;
import com.timevale.infoauth.service.impl.provider.client.shucifang.domain.ShuCiFangFourElementResponse;
import com.timevale.infoauth.service.impl.provider.client.youshu.YouShuClient;
import com.timevale.infoauth.service.impl.provider.client.youshu.domain.YouShuOrgInfoFourOutput;
import com.timevale.infoauth.service.impl.provider.client.youshu.domain.YouShuOrgInfoThreeOutput;
import com.timevale.infoauth.service.impl.provider.client.youshu.domain.YouShuOutput;
import com.timevale.infoauth.service.impl.provider.dto.ZSZHEnterprise3ElementResponse;
import com.timevale.infoauth.service.impl.provider.dto.ZSZHEnterprise4ElementRequest;
import com.timevale.infoauth.service.impl.provider.dto.ZSZHEnterprise4ElementResponse;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.List;

import static com.dyuproject.protostuff.CollectionSchema.MessageFactories.ArrayList;

@JsonDataIgnore
public class ProvSceneTest  extends ApplicationTest {

    @Test
    public void shuCiFangHandleProvSceneFour_1() {
        String str = "{\"code\":\"01\",\"data\":{\"reasonCode\":\"1\",\"reasonDesc\":\"统代或注册号查⽆\"},\"msg\":\"⽆法核\n" +
                "验\",\"orderno\":\"2505221500517371315\"}";
        ShuCiFangFourElementResponse response = JSON.parseObject(str, ShuCiFangFourElementResponse.class);
        ShuCiFangClient.handleProvSceneFour( response);
    }

    @Test
    public void shuCiFangHandleProvSceneFour_2() {
        String str = "{\"code\":\"00\",\"data\":\n" +
                "{\"entState\":\"\",\"isAllMatch\":\"0\",\"isCernoMatch\":\"0\",\"isCodeMatch\":\"1\",\"isEntNameMatch\":\"0\",\"isFrNameMatch\n" +
                "\":\"1\"},\"msg\":\"查询成功\",\"orderno\":\"2505221505447161855\"}";
        ShuCiFangFourElementResponse response = JSON.parseObject(str, ShuCiFangFourElementResponse.class);
        ShuCiFangClient.handleProvSceneFour( response);
    }

    @Test
    public void shuCiFangHandleProvSceneFour_3() {
        String str = "{\"code\":\"00\",\"data\":{\"entState\":\"\",\"isAllMatch\":\"0\",\"isCernoMatch\":\"0\",\"isCodeMatch\":\"1\",\"isEntNameMatch\":\"1\",\"isFrNameMatch\":\"0\"},\"msg\":\"查询成功\",\"orderno\":\"2505221504047411887\"}";
        ShuCiFangFourElementResponse response = JSON.parseObject(str, ShuCiFangFourElementResponse.class);
        ShuCiFangClient.handleProvSceneFour( response);
    }

    @Test
    public void testZSZHEnterprise4Element_1(){
        String str = "{\"result\":{\"entinfo\":{\"matched\":0,\"detail\":{\"entmark1\":1,\"entmark2\":0}},\"relation\":{\"matched\":-1,\"detail\":{\"name\":-1,\"id\":-1}}},\"code\":404}";
        EsignRequest<ZSZHEnterprise4ElementResponse> request =  new ZSZHEnterprise4ElementRequest()
                .withEname("测试")
                .withCreditCode("测试").withName("测试");
        JSONObject jsonObject = JSON.parseObject(str);
        ZSZHEnterprise4ElementResponse response = new ZSZHEnterprise4ElementResponse(request,null);
        response.getSuccessProvScene(jsonObject);
    }

    @Test
    public void testZSZHEnterprise4Element_2(){
        String str = "{\"result\":{\"entinfo\":{\"matched\":1,\"detail\":{\"entstatus\":\"1\",\"entmark1\":1,\"entmark2\":1,\"opfrom\":\"2002-12-18\"}},\"relation\":{\"matched\":0,\"detail\":{\"name\":0,\"id\":0}}},\"code\":200}";
        EsignRequest<ZSZHEnterprise4ElementResponse> request =  new ZSZHEnterprise4ElementRequest()
                .withEname("测试")
                .withCreditCode("测试").withName("测试");
        JSONObject jsonObject = JSON.parseObject(str);
        ZSZHEnterprise4ElementResponse response = new ZSZHEnterprise4ElementResponse(request,null);
        response.getSuccessProvScene(jsonObject);
    }

    @Test
    public void testZSZHEnterprise4Element_3(){
        String str = "{\"result\":{\"entinfo\":{\"matched\":1,\"detail\":{\"entstatus\":\"1\",\"entmark1\":1,\"entmark2\":1,\"opfrom\":\"2002-12-18\"}},\"relation\":{\"matched\":0,\"detail\":{\"name\":1,\"id\":-1}}},\"code\":200}";
        EsignRequest<ZSZHEnterprise4ElementResponse> request =  new ZSZHEnterprise4ElementRequest()
                .withEname("测试")
                .withCreditCode("测试").withName("测试");
        JSONObject jsonObject = JSON.parseObject(str);
        ZSZHEnterprise4ElementResponse response = new ZSZHEnterprise4ElementResponse(request,null);
        response.getSuccessProvScene(jsonObject);
    }

    @Test
    public void testZSZHEnterprise4Element_4(){
        String str = "{\"result\":{\"entinfo\":{\"matched\":1,\"detail\":{\"entstatus\":\"1\",\"entmark1\":1,\"entmark2\":1,\"opfrom\":\"2002-12-18\"}},\"relation\":{\"matched\":0,\"detail\":{\"name\":1,\"id\":-1}}},\"code\":200}";
        EsignRequest<ZSZHEnterprise4ElementResponse> request =  new ZSZHEnterprise4ElementRequest()
                .withEname("测试")
                .withCreditCode("测试").withName("测试");
        JSONObject jsonObject = JSON.parseObject(str);
        ZSZHEnterprise4ElementResponse response = new ZSZHEnterprise4ElementResponse(request,null);
        response.getSuccessProvScene(jsonObject);
    }

    @Test
    public void testZSZHEnterprise4Element_5(){
        String str = "{\"result\":{\"entinfo\":{\"matched\":0,\"detail\":{\"entmark1\":1,\"entmark2\":0}},\"relation\":{\"matched\":-1,\"detail\":{\"name\":-1,\"id\":-1}}},\"code\":404}";
        EsignRequest<ZSZHEnterprise4ElementResponse> request =  new ZSZHEnterprise4ElementRequest()
                .withEname("测试")
                .withCreditCode("测试").withName("测试");
        JSONObject jsonObject = JSON.parseObject(str);
        request.putQueryParameter("entmark1","111111");
        ZSZHEnterprise4ElementResponse response = new ZSZHEnterprise4ElementResponse(request,null);
        response.getSuccessProvScene(jsonObject);
    }

    @Test
    public void testZSZHEnterprise3Element_1(){
        String str = "{\"result\":{\"entinfo\":{\"matched\":1,\"detail\":{\"entstatus\":\"1\",\"entmark1\":-1,\"entmark2\":1,\"opfrom\":\"2002-12-18\"}},\"relation\":{\"matched\":1,\"detail\":{\"inv\":1,\"manager\":1,\"name\":1,\"fr\":1}}},\"code\":200}";
        EsignRequest<ZSZHEnterprise4ElementResponse> request =  new ZSZHEnterprise4ElementRequest()
                .withEname("测试")
                .withCreditCode("测试").withName("测试");
        JSONObject jsonObject = JSON.parseObject(str);
        ZSZHEnterprise3ElementResponse response = new ZSZHEnterprise3ElementResponse(request,null);
        response.getSuccessProvScene(jsonObject);
    }

    @Test
    public void testZSZHEnterprise3Element_2(){
        String str = "{\"result\":{\"entinfo\":{\"matched\":0,\"detail\":{\"entmark1\":0,\"entmark2\":1}},\"relation\":{\"matched\":-1,\"detail\":{\"name\":-1,\"id\":-1}}},\"code\":404}";
        EsignRequest<ZSZHEnterprise4ElementResponse> request =  new ZSZHEnterprise4ElementRequest()
                .withEname("测试")
                .withCreditCode("测试").withName("测试");
        JSONObject jsonObject = JSON.parseObject(str);
        ZSZHEnterprise3ElementResponse response = new ZSZHEnterprise3ElementResponse(request,null);
        response.getSuccessProvScene(jsonObject);
    }

    @Test
    public void testZSZHEnterprise3Element_3(){
        String str = "{\"result\":{\"entinfo\":{\"matched\":0,\"detail\":{\"entmark1\":1,\"entmark2\":0}},\"relation\":{\"matched\":-1,\"detail\":{\"name\":-1,\"id\":-1}}},\"code\":404}";
        EsignRequest<ZSZHEnterprise4ElementResponse> request =  new ZSZHEnterprise4ElementRequest()
                .withEname("测试")
                .withCreditCode("测试").withName("测试");
        JSONObject jsonObject = JSON.parseObject(str);
        ZSZHEnterprise3ElementResponse response = new ZSZHEnterprise3ElementResponse(request,null);
        response.getSuccessProvScene(jsonObject);
    }

    @Test
    public void testYouShu3_1(){
        YouShuClient youShuClient = new YouShuClient();
        OrganProvin input = new OrganProvin();
        input.setOrgName("杭州天⾕信息科技有限公司");
        input.setLegalRepName("测试");
        YouShuOutput<YouShuOrgInfoThreeOutput> output = new YouShuOutput<>();
        output.setCode("0001");
        output.setMsg("查询无记录");
        youShuClient.handleThreeProvScene(new OrganProvin(),output);
    }

    @Test
    public void testYouShu3_2(){
        YouShuClient youShuClient = new YouShuClient();
        OrganProvin input = new OrganProvin();
        input.setOrgName("杭州天⾕信息科技有限公司");
        input.setLegalRepName("测试");
        YouShuOutput<YouShuOrgInfoThreeOutput> output = new YouShuOutput<>();
        output.setCode("0000");
        YouShuOrgInfoThreeOutput youShuOrgInfoThreeOutput = new YouShuOrgInfoThreeOutput();
        List<YouShuOrgInfoThreeOutput.Basic> basicList = new ArrayList<>();
        YouShuOrgInfoThreeOutput.Basic basic = new YouShuOrgInfoThreeOutput.Basic();
        basic.setEntName("杭州天⾕信息科技有限公司");
        basic.setFrName("测试");
        basic.setEntStatus("正常");
        basicList.add(basic);
        youShuOrgInfoThreeOutput.setBasicList(basicList);
        output.setData(youShuOrgInfoThreeOutput);
        youShuClient.handleThreeProvScene(input,output);
    }


    @Test
    public void testYouShu4_1(){
        YouShuClient youShuClient = new YouShuClient();
        YouShuOutput<YouShuOrgInfoFourOutput> response = new YouShuOutput<>();
        response.setCode("0001");
        youShuClient.handleFourProvScene(response);
    }

    @Test
    public void testYouShu4_2(){
        YouShuClient youShuClient = new YouShuClient();
        YouShuOutput<YouShuOrgInfoFourOutput> response = new YouShuOutput<>();
        response.setCode("0000");
        YouShuOrgInfoFourOutput youShuOrgInfoFourOutput = new YouShuOrgInfoFourOutput();
        youShuOrgInfoFourOutput.setCheck_corp_name("1");
        youShuOrgInfoFourOutput.setCheck_identification("1");
        youShuOrgInfoFourOutput.setCheck_legalperson_name("2");
        youShuOrgInfoFourOutput.setCheck_legalperson_idcard("2");
        youShuOrgInfoFourOutput.setCheck_enterprise_status("1");
        response.setData(youShuOrgInfoFourOutput);
        youShuClient.handleFourProvScene(response);
    }

    @Test
    public void testYouShu4_3(){
        YouShuClient youShuClient = new YouShuClient();
        YouShuOutput<YouShuOrgInfoFourOutput> response = new YouShuOutput<>();
        response.setCode("0000");
        YouShuOrgInfoFourOutput youShuOrgInfoFourOutput = new YouShuOrgInfoFourOutput();
        youShuOrgInfoFourOutput.setCheck_corp_name("1");
        youShuOrgInfoFourOutput.setCheck_identification("2");
        youShuOrgInfoFourOutput.setCheck_legalperson_name("2");
        youShuOrgInfoFourOutput.setCheck_legalperson_idcard("2");
        youShuOrgInfoFourOutput.setCheck_enterprise_status("1");
        response.setData(youShuOrgInfoFourOutput);
        youShuClient.handleFourProvScene(response);
    }


}
