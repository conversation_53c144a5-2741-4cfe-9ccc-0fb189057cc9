package com.timevale.infoauth.service.api;

import com.timevale.infoauth.service.dto.PRetryDTO;
import com.timevale.mandarin.common.annotation.RestClient;

import java.util.List;

/**
 * 应用层服务（重试子域 - 支撑域）
 *
 * <AUTHOR> href="mailto:<EMAIL>">jiuchen</a>
 */
@RestClient(serviceId = "infoauth-service")
public interface ProviderRetryService {

  /**
   * 保存重试子域
   *
   * @param pRetryDTO
   */
  void save(PRetryDTO pRetryDTO);

  /**
   * 查询一条重试子域
   *
   * @param managerId
   * @return
   */
  PRetryDTO getRetryById(Integer managerId);

  /**
   * 查询所有重试子域
   *
   * @return
   */
  List<PRetryDTO> queryRetryAll();
}
