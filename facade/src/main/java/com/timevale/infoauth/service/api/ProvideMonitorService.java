package com.timevale.infoauth.service.api;

import com.timevale.infoauth.service.dto.motitor.*;
import com.timevale.mandarin.common.annotation.RestClient;

/**
 * 告警监控管理
 *
 * <AUTHOR>
 */
@RestClient(serviceId = "infoauth-service")
public interface ProvideMonitorService {

  /**
   * 供应商故障恢复
   *
   * @param recoveryDTO
   */
  void providerRecovery(ProviderRecoveryDTO recoveryDTO);

  /**
   * 供应商故障降级
   *
   * @param degradeDTO
   */
  void providerDegrade(ProviderDegradeDTO degradeDTO);


  /**
   * 告警监控项目新增
   *
   * @param addDTO
   */
  void monitorAlertConfigAdd(AlertConfigAddDTO addDTO);


  /**
   * 告警监控项目修改
   *
   * @param modifyDTO
   */
  void monitorAlertConfigModify(AlertConfigModifyDTO modifyDTO);

  /**
   * 降级配置新增
   *
   * @param addDTO
   */
  void monitorDegradationConfigAdd(DegradationConfigAddDTO addDTO);

  /**
   * 降级配置修改
   *
   * @param modifyDTO
   */
  void monitorDegradationConfigModify(DegradationConfigModifyDTO modifyDTO);

  /**
   * 告警通知新增
   *
   * @param addDTO
   */
  void monitorNotifyConfigAdd(NotifyConfigAddDTO addDTO);

  /**
   * 告警通知修改
   *
   * @param modifyDTO
   */
  void monitorNotifyConfigAddModify(NotifyConfigModifyDTO modifyDTO);


}
