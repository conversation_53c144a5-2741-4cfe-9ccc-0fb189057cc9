hystrix.sleep-time = 10000
failover.rety-match-casue = false
failover.max-retry=2
#��ֹ�쳣��
#failover.abort-errors = com.timevale.esign.realname.impl.provider.exception.ProviderException
#�����쳣��
failover.retry-errors = java.util.concurrent.TimeoutException,com.netflix.hystrix.exception.HystrixRuntimeException,com.timevale.infoauth.service.exception.AgainException,com.timevale.infoauth.service.exception.SuperException,com.timevale.infoauth.service.exception.ProviderException
failover.abort-match-cause = true
hystrix.core-pool-size = 12
#��������ʱʱ��
hystrix.execution-timeout= 10000
#ʱ�䴰��������ʧ����
hystrix.error-percentage = 80
#�������ʧ�ܴ���
hystrix.com.timevale.infoauth.service.request-volume = 10
#���뼶��
hystrix.isolation = THREAD