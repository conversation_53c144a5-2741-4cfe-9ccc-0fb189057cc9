package com.timevale.infoauth.service.request.agreement;

import com.timevale.infoauth.service.enums.agreement.CommonAgreementLangEnum;
import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * 通用协议信息查询
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CommonAgreementBatchQueryRequest extends ToString {

  public static final int MAX_BATCH_SIZE = 50;

  private List<String> commonAgreementIds;

  private String lang;

}
