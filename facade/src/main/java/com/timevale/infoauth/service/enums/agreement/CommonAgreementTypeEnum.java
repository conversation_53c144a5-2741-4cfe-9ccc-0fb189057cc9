package com.timevale.infoauth.service.enums.agreement;

import java.util.Objects;

/**
 * 协议分类枚举
 *
 * <AUTHOR>
 * caCertShunt  caStandardCertShunt common caStandardCert
 * @since 2024/12/7 16:31
 */
public enum CommonAgreementTypeEnum {

    common("common", "普通协议"),
    caCert("caCert", "CA证书协议"),
    caStandardCert("caStandardCert", "标准CA证书协议"),
    ;

    private String type;
    private String desc;



    CommonAgreementTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }


    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }



    public static CommonAgreementTypeEnum typeOf(String code){
        for(CommonAgreementTypeEnum showType : CommonAgreementTypeEnum.values()){
            if(Objects.equals(showType.type, code)){
                return showType;
            }
        }
        return common;
    }
    }

