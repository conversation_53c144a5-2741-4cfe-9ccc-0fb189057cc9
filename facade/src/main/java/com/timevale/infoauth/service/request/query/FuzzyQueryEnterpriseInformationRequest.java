package com.timevale.infoauth.service.request.query;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
@Data
public class FuzzyQueryEnterpriseInformationRequest extends ToString {

  private String appId;

  /** 模糊查询的关键字（必填，企业名称） */
  private String keyword;

  /** 支持分页查询，查询起始页（非必填，不填默认查第一页，固定每页最多10条记录） */
  @Deprecated
  private int pageIndex;

  /**
   * false 不使用缓存
   * true 预期希望使用使用缓存
   */
  private boolean expectCache = false;
}
