package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.InfoServiceDO;
import com.timevale.infoauth.dal.infoauth.facade.AuthResultQueryRecordDTO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * The Table info_service. info_service
 *
 * <AUTHOR> Kunpeng
 */
public interface InfoServiceDAO {

  /**
   * desc:插入表:info_service.<br>
   *
   * @param entity entity
   * @return int
   */
  @Insert(
      "INSERT INTO `info_service` (`infoauth_id`, `type`, `object_type`, `status`, "
          + "`app_id`, `create_time`, `modify_time`)"
          + " VALUES (#{infoauthId}, #{type},#{objectType}, #{status}, #{appId}, now(), now())")
  int insert(InfoServiceDO entity);

  /**
   * desc:插入表:info_service.<br>
   *
   * @param entity entity
   * @return int
   */
  @Insert(
          "INSERT INTO `info_service` (`infoauth_id`, `type`, `object_type`, `status`, "
                  + "`app_id`, `create_time`, `modify_time`)"
                  + " VALUES (#{infoauthId}, #{type},#{objectType}, #{status}, #{appId}, #{createTime}, now())")
  int insertNew(InfoServiceDO entity);

  /**
   * desc:批量插入表:info_service.<br>
   *
   * @param list list
   * @return int
   */
  int insertBatch(List<InfoServiceDO> list);

  /**
   * desc:根据主键删除数据:info_service.<br>
   *
   * @param id id
   * @return int
   */
  int deleteById(Long id);

  /**
   * desc:根据主键获取数据:info_service.<br>
   *
   * @param id id
   * @return InfoServiceDO
   */
  InfoServiceDO getById(Long id);

  @Update(
      "<script>"
          + "UPDATE info_service SET "
          + "`status` = #{status,jdbcType=TINYINT}, modify_time = now() "
          + "WHERE infoauth_id = #{infoauthId,jdbcType=VARCHAR}"
          + "</script>")
  void updateStatus(@Param("infoauthId") String infoauthId, @Param("status") int status);

  @Select("select * from info_service where infoauth_id = #{infoAuthId}")
  InfoServiceDO getByInfoAuthId(@Param("infoAuthId") String infoAuthId);

  /**
   * desc:根据条件查询信息比对记录
   *
   * @return InfoServiceDO
   */
  @Select({
          "select * from info_service",
          "<script>",
          "where  1=1 ",
          "<if test='infoAuthServiceType != null'> and type = #{infoAuthServiceType} </if>",
          "<if test='serviceStatus != null'> and status = #{serviceStatus} </if>",
          "<if test='appId != null'>and app_id = #{appId} </if>",
          "<if test='startTime != null'>and modify_time between #{startTime} and #{endTime} </if>",
          "order by modify_time desc limit #{pageIndex},#{pageSize}",
          "</script>"})
  List<InfoServiceDO> getByAuthResultQuery(AuthResultQueryRecordDTO authResultQueryRecordDTO);

  /**
   * desc:根据条件查询信息比对记录条数
   *
   * @return InfoServiceDO
   */
  @Select({
          "select count(1) from info_service",
          "<script>",
          "where  1=1 ",
          "<if test='infoAuthServiceType != null'> and type = #{infoAuthServiceType} </if>",
          "<if test='serviceStatus != null'> and status = #{serviceStatus} </if>",
          "<if test='appId != null'> and  app_id = #{appId} </if>",
          "<if test='startTime != null'> and modify_time between #{startTime} and #{endTime} </if>",
          "order by modify_time desc limit #{pageIndex},#{pageSize}",
          "</script>"})
  int getByAuthResultQueryCount(AuthResultQueryRecordDTO authResultQueryRecordDTO);


  /**
   * desc:查询最小id
   *
   * @return long
   */
  @Select({
          "<script>",
          "select min(id) from info_service",
          "where  1=1 ",
          "<if test='infoAuthServiceType != null'> and type = #{infoAuthServiceType} </if>",
          "<if test='serviceStatus != null'> and status = #{serviceStatus} </if>",
          "<if test='appId != null'> and  app_id = #{appId} </if>",
          "<if test='startTime != null'> and  modify_time between #{startTime} and #{endTime} </if>",
          "</script>"})
  long getMinIdInfoService(AuthResultQueryRecordDTO authResultQueryRecordDTO);

  /**
   * desc:查询最大id
   *
   * @return long
   */
  @Select({
          "<script>",
          "select max(id) from info_service",
          "where  1=1 ",
          "<if test='infoAuthServiceType != null'> and type = #{infoAuthServiceType} </if>",
          "<if test='serviceStatus != null'> and status = #{serviceStatus} </if>",
          "<if test='appId != null'> and  app_id = #{appId} </if>",
          "<if test='startTime != null'> and  modify_time between #{startTime} and #{endTime} </if>",
          "</script>"})
  long getMaxIdInfoService(AuthResultQueryRecordDTO authResultQueryRecordDTO);

  /**
   * desc:查询list进行数据补偿
   *
   * @return InfoServiceDO
   */
  @Select({
          "<script>",
          "select * from info_service",
          "where  1=1 ",
          "<if test='infoAuthId != null'> AND infoauth_id = #{infoAuthId} </if>",
          "<if test='infoAuthServiceType != null'> and type = #{infoAuthServiceType} </if>",
          "<if test='serviceStatus != null'> and status = #{serviceStatus} </if>",
          "<if test='appId != null'> and  app_id = #{appId} </if>",
          "<if test='startTime != null'> and  modify_time between #{startTime} and #{endTime} </if>",
          "<if test='startId != null'> AND id between #{startId} and #{endId} </if>",
          "</script>"})
  List<InfoServiceDO> getInfoServiceListForUpdate(AuthResultQueryRecordDTO entity);

  @Update(
          "<script>"
                  + "UPDATE info_service SET "
                  + "`object_type` = #{objectType,jdbcType=TINYINT}, modify_time = now() "
                  + "WHERE infoauth_id = #{infoauthId,jdbcType=VARCHAR} and object_type = 0"
                  + "</script>")
  int updateObjectType(@Param("infoauthId") String infoauthId, @Param("objectType") int objectType);
}
