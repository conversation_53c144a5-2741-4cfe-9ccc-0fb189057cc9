package com.timevale.infoauth.service.agreement.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.timevale.framework.puppeteer.model.ConfigChange;
import com.timevale.framework.puppeteer.model.ConfigChangeEvent;
import com.timevale.framework.puppeteer.spring.annotation.PuppeteerConfigChangeListener;
import com.timevale.infoauth.service.agreement.component.tool.SpringTool;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 定义可配置化的属性
 *
 * <AUTHOR>
 * @copyright 2024
 * @date 2024/6/27
 */
@Slf4j
@Component
public class CommonAgreementConfigurableProperties {


  //#协议编号前缀
  @Getter
  @Value("${common-agreement.id.prefix}")
  private String agreementIdPrefix;

  //#协议发布记录ID前缀
  @Getter
  @Value("${common-agreement.release_id.prefix}")
  private String agreementReleaseIdPrefix;

  private Map<String,String> fillParamsKeyMap = new HashMap<>();

  @Value("${common-agreement.fill.paramsKey.convert:}")
  private void setAgreementFillParamsKeyConvert(String text){
     if(StringUtils.isBlank(text)){
       fillParamsKeyMap.clear();
       return;
     }
    Map<String,String> fillParamsKeyMapTemp = JSON.parseObject(text, new TypeReference<Map<String,String>>(){});
    fillParamsKeyMap.clear();
    fillParamsKeyMap.putAll(fillParamsKeyMapTemp);
  }

  public String fillParamKeyConvertToContentKey(String fillKey){
    if(StringUtils.isBlank(fillKey)){
       return fillKey;
    }
    String contentKey  = fillParamsKeyMap.get(fillKey);
    if(StringUtils.isBlank(contentKey)){
       return fillKey;
    }
    return contentKey;
  }

  private Map<String, Field> properties;

  @PostConstruct
  private void onInitialize() {
    Field[] fields = getClass().getDeclaredFields();
    properties = (new HashMap<>(2 * fields.length));
    String propertyKeyPatternKeyName = "key";
    Pattern propertyKeyPattern =
        Pattern.compile(
            "^(\\$\\{)?(?<" + propertyKeyPatternKeyName + ">[0-9a-zA-Z-_.]+)(:\\S*)?}?$");
    for (Field field : fields) {
      String key = deducePropertyKey(propertyKeyPatternKeyName, propertyKeyPattern, field);
      if (null == key) {
        continue;
      }
      ensureAccessible(field);
      properties.put(key, field);
    }
  }

  private String deducePropertyKey(String patternKeyName, Pattern keyPattern, Field field) {
    Value valueAnn = field.getAnnotation(Value.class);
    if (null == valueAnn) {
      return field.getName();
    }

    try {
      Matcher matcher = keyPattern.matcher(valueAnn.value());
      if (matcher.find()) {
        return matcher.group(patternKeyName);
      }
      return field.getName();
    } catch (Exception cause) {
      log.warn("Field deduce property key on '" + valueAnn.value() + "'", cause);
      return null;
    }
  }

  private void ensureAccessible(Field field) {
    if (!field.isAccessible()) {
      field.setAccessible(true);
    }
  }

  @PuppeteerConfigChangeListener("common-agreement")
  void onPropertyChanged(ConfigChangeEvent changeEvent) {

    ConfigChange change = changeEvent.getChange("common-agreement.fill.paramsKey.convert");
    if (change != null) {
      setAgreementFillParamsKeyConvert(change.getNewValue());
    }

    for (String key : changeEvent.changedKeys()) {
      Field property = properties.get(key);
      if (null == property) {
        continue;
      }
      PropertyChanged propertyChanged = PropertyChanged.valueOf(changeEvent.getChange(key));
      propertyChanged.changeProperty(this, property);
    }
  }

  private static volatile CommonAgreementConfigurableProperties DEFAULT_PRO;

  public static CommonAgreementConfigurableProperties getInstance() {
    if(DEFAULT_PRO == null){
      synchronized (CommonAgreementConfigurableProperties.class){
        if (DEFAULT_PRO == null) {
          DEFAULT_PRO = SpringTool.getBean(CommonAgreementConfigurableProperties.class);
        }
      }
    }

    return DEFAULT_PRO;
  }

  private static final class PropertyChanged {

    private static final Function<String, Byte> CONVERTER_BYTE_FUNCTION = Byte::valueOf;
    private static final Function<String, Short> CONVERTER_SHORT_FUNCTION = Short::valueOf;
    private static final Function<String, Integer> CONVERTER_INTEGER_FUNCTION = Integer::valueOf;
    private static final Function<String, Long> CONVERTER_LONG_FUNCTION = Long::valueOf;
    private static final Function<String, Float> CONVERTER_FLOAT_FUNCTION = Float::valueOf;
    private static final Function<String, Double> CONVERTER_DOUBLE_FUNCTION = Double::valueOf;
    private static final Function<String, String> CONVERTER_STRING_FUNCTION = (s) -> s;
    private static final Function<String, Boolean> CONVERTER_BOOLEAN_FUNCTION = Boolean::valueOf;

    private static final Map<Class<?>, Function<String, ?>> CONVERTER_FUNCTIONS;

    static {
      CONVERTER_FUNCTIONS = (new HashMap<>(16));
      CONVERTER_FUNCTIONS.put(byte.class, CONVERTER_BYTE_FUNCTION);
      CONVERTER_FUNCTIONS.put(short.class, CONVERTER_SHORT_FUNCTION);
      CONVERTER_FUNCTIONS.put(int.class, CONVERTER_INTEGER_FUNCTION);
      CONVERTER_FUNCTIONS.put(long.class, CONVERTER_LONG_FUNCTION);
      CONVERTER_FUNCTIONS.put(float.class, CONVERTER_FLOAT_FUNCTION);
      CONVERTER_FUNCTIONS.put(double.class, CONVERTER_DOUBLE_FUNCTION);
      CONVERTER_FUNCTIONS.put(boolean.class, CONVERTER_BOOLEAN_FUNCTION);
      CONVERTER_FUNCTIONS.put(Byte.class, CONVERTER_BYTE_FUNCTION);
      CONVERTER_FUNCTIONS.put(Short.class, CONVERTER_SHORT_FUNCTION);
      CONVERTER_FUNCTIONS.put(Integer.class, CONVERTER_INTEGER_FUNCTION);
      CONVERTER_FUNCTIONS.put(Long.class, CONVERTER_LONG_FUNCTION);
      CONVERTER_FUNCTIONS.put(Float.class, CONVERTER_FLOAT_FUNCTION);
      CONVERTER_FUNCTIONS.put(Double.class, CONVERTER_DOUBLE_FUNCTION);
      CONVERTER_FUNCTIONS.put(String.class, CONVERTER_STRING_FUNCTION);
      CONVERTER_FUNCTIONS.put(Boolean.class, CONVERTER_BOOLEAN_FUNCTION);
    }

    private final ConfigChange change;

    private PropertyChanged(ConfigChange change) {
      this.change = change;
    }

    private void changeProperty(Object instance, Field field) {
      Function<String, ?> converter = CONVERTER_FUNCTIONS.get(field.getType());
      if (null == converter) {
        log.warn("Not found converter function on type '" + field.getType() + "' .");
        return;
      }
      Object value;
      try {
        value = converter.apply(change.getNewValue());
      } catch (Exception cause) {
        log.warn(
            "'" + change.getNewValue() + "' could not convert to type '" + field.getType() + "' .",
            cause);
        return;
      }

      try {
        field.set(instance, value);
      } catch (Exception cause) {
        log.warn(
            "Failed to set value on field '" + field.getName() + "' with value '" + value + "' .",
            cause);
      }
    }

    private static PropertyChanged valueOf(ConfigChange change) {
      return (new PropertyChanged(change));
    }
  }
}
