package com.timevale.infoauth.dal.infoauth.dao;

import com.timevale.infoauth.dal.infoauth.dataobject.ProviderErrCodeEnumDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;

/** 供应商错误码枚举影子表 */
public interface ProviderErrCodeEnumShadowDAO {

  @Insert({
    "<script>",
    "INSERT INTO `provider_err_code_enum_shadow` (`p_code_id`, `biz_type`, `provider`, `biz_md5`",
    "<if test=\"pCode != null and pCode != '' \"> <![CDATA[, p_code]]> </if> ",
    "<if test=\"pMsg != null and pMsg != '' \"> <![CDATA[, p_msg]]> </if> ",
    ") VALUES (#{pCodeId}, #{bizType}, #{provider}, #{bizMd5}",
    "<if test=\"pCode != null and pCode != '' \"> <![CDATA[, #{pCode}]]> </if> ",
    "<if test=\"pMsg != null and pMsg != '' \"> <![CDATA[, #{pMsg}]]> </if> ",
    ")",
    "</script>"
  })
  @Options(useGeneratedKeys = true, keyColumn = "id")
  int insert(ProviderErrCodeEnumDO entity);

  @Select({
    "<script>",
    "SELECT `p_code_id`,`biz_type`,`provider`,`p_code`,`p_msg`,`biz_md5`, `biz_code_id` FROM `provider_err_code_enum_shadow`",
    " WHERE `biz_md5`=#{md5} LIMIT 1",
    "</script>",
  })
  ProviderErrCodeEnumDO getByMd5(String md5);

  @Delete({
          "<script>",
          "delete    FROM  provider_err_code_enum_shadow ",
          " WHERE  biz_md5 =#{md5}  ",
          "</script>",
  })
  int deleteByMd5(String md5);
}
