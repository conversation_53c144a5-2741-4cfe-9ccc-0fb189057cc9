package com.timevale.infoauth.dal.infoauth.dataobject;

import com.timevale.infoauth.dal.infoauth.utils.StringUtil;
import lombok.Getter;

@Getter
public class InfoServiceBankCardOcrDO {
  private Integer id;
  private String infoauthId;
  private String providerId;
  private String frontImgKey;
  private Integer result;
  private Integer serviceStatus;
  private String providerResult;
  private String ocrBankCardNo;
  private String ocrBankName;
  private Integer ocrBankCardType;
  private String createTime;
  private String modifyTime;

  public void setId(Integer id) {
    this.id = id;
  }

  public void setInfoauthId(String infoauthId) {
    this.infoauthId = infoauthId;
  }

  public void setProviderId(String providerId) {
    this.providerId = providerId;
  }

  public void setFrontImgKey(String frontImgKey) {
    this.frontImgKey = StringUtil.truncation(frontImgKey, 300);
  }

  public void setResult(Integer result) {
    this.result = result;
  }

  public void setServiceStatus(Integer serviceStatus) {
    this.serviceStatus = serviceStatus;
  }

  public void setProviderResult(String providerResult) {
    this.providerResult = StringUtil.truncation(providerResult, 50);
  }

  public void setOcrBankCardNo(String ocrBankCardNo) {
    this.ocrBankCardNo = StringUtil.truncation(ocrBankCardNo, 20);
  }

  public void setOcrBankName(String ocrBankName) {
    this.ocrBankName = StringUtil.truncation(ocrBankName, 50);
  }

  public void setOcrBankCardType(Integer ocrBankCardType) {
    this.ocrBankCardType = ocrBankCardType;
  }

  public void setCreateTime(String createTime) {
    this.createTime = createTime;
  }

  public void setModifyTime(String modifyTime) {
    this.modifyTime = modifyTime;
  }
}
