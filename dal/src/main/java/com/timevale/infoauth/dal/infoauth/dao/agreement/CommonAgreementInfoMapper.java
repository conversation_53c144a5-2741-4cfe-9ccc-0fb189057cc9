package com.timevale.infoauth.dal.infoauth.dao.agreement;

import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementInfoDO;
import com.timevale.infoauth.dal.infoauth.dataobject.agreement.CommonAgreementInfoExtendDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * CommonAgreementInfoMapper定义了通用协议信息表的操作接口。
 * 该接口主要用于执行协议信息的插入、更新、查询等操作。
 */
@Mapper
public interface CommonAgreementInfoMapper {

    /**
     * 插入一条新的协议信息记录。
     *
     * @param infoDO 包含要插入的协议信息的数据对象。
     * @return 插入操作影响的行数。
     */
    @Insert(
            "<script>INSERT INTO common_agreement_info(status, deleted, agreement_id,agreement_title,agreement_type,agreement_mark,release_version,category_ref,operator,creator"
                    + ") VALUES(\n"
                    + "            #{status,jdbcType=TINYINT}\n"
                    + "            , #{deleted,jdbcType=TINYINT}\n"
                    + "            , #{agreementId,jdbcType=VARCHAR}\n"
                    + "            , #{agreementTitle,jdbcType=VARCHAR}\n"
                    + "            , #{agreementType,jdbcType=VARCHAR}\n"
                    + "            , #{agreementMark,jdbcType=VARCHAR}\n"
                    + "            , #{releaseVersion,jdbcType=VARCHAR}\n"
                    + "            , #{categoryRef,jdbcType=VARCHAR}\n"
                    + "            , #{operator,jdbcType=VARCHAR}\n"
                    + "            , #{creator,jdbcType=VARCHAR}\n"
                    + ")"
                    + "</script>")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(CommonAgreementInfoDO infoDO);

    /**
     * 根据ID更新协议信息记录。
     *
     * @param entity 包含要更新的协议信息的数据对象。
     */
    @Update("<script> UPDATE common_agreement_info <set> " +
            "<if test='status != null'>  status = #{status}, </if>" +
            "<if test='deleted != null'>  deleted = #{deleted},  </if>" +
            "<if test='agreementTitle != null'>  agreement_title = #{agreementTitle},  </if>" +
            "<if test='agreementType != null'>  agreement_type = #{agreementType},  </if>" +
            "<if test='agreementMark != null'>  agreement_mark = #{agreementMark},  </if>" +
            "<if test='releaseVersion != null'>  release_version = #{releaseVersion},  </if>" +
            "<if test='categoryRef != null'>  category_ref = #{categoryRef},  </if>" +
            "<if test='operator != null'>  operator = #{operator},  </if>" +
            "<if test='creator != null'>  creator = #{creator},  </if>" +
            "  modify_time = now() " +
            "  </set> " +
            " where id = #{id} </script>")
    void updateById(CommonAgreementInfoDO entity);

    /**
     * 根据协议ID查询协议信息记录。
     *
     * @param agreementId 协议的唯一标识符。
     * @return 查询到的协议信息数据对象，如果没有找到则返回null。
     */
    @Select("select * from common_agreement_info where deleted = 0 and agreement_id = #{agreementId} limit 1 ")
    CommonAgreementInfoDO getByAgreementId(@Param("agreementId") String agreementId);

    /**
     * 分页查询协议信息记录。
     *
     * @param pageIndex 页码索引。
     * @param pageSize 每页的记录数。
     * @return 查询到的协议信息列表。
     */
    @Select(
            "<script>"
                    + "SELECT * FROM `common_agreement_info` WHERE deleted = 0 "
                    + " ORDER BY id DESC LIMIT #{pageIndex},#{pageSize}"
                    + "</script>")
    List<CommonAgreementInfoDO> getListPages(
            @Param("pageIndex") int pageIndex,
            @Param("pageSize") int pageSize);

    /**
     * 计算未删除的协议信息记录总数。
     *
     * @return 协议信息记录的总数。
     */
    @Select(
            "<script>"
       + " SELECT count(1) FROM `common_agreement_info` WHERE  deleted = 0 "
                    + "</script>")
    long count();

    /**
     * 批量查询发布状态的协议信息。
     *
     * @param agreementIdList 协议ID列表。
     * @return 查询到的扩展协议信息列表，包含关联的发布记录信息。
     */
    @Select(
            "<script>"
                    + " select A.*,B.content_fill,B.release_record_id,B.short_name,B.full_name,B.lang,B.release_time" +
                    " from common_agreement_info A inner join common_agreement_release_record B " +
                    " on  A.deleted = 0   and A.agreement_id = B.agreement_id and A.release_version = B.release_version and B.enable_status = 1 "
                    + "WHERE  "
                    + "  A.agreement_id IN"
                    + "<foreach item='item' index='index' collection='agreementIdList' open='(' separator=',' close=')'>"
                    + "#{item}"
                    + "</foreach> limit 100"
                    + "</script>")
    List<CommonAgreementInfoExtendDO>  getBatchAgreementIdOnRelease(
            @Param("agreementIdList") List<String> agreementIdList);

}
