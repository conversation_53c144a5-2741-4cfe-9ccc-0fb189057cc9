package com.timevale.infoauth.mayi;

import cn.com.antcloud.api.product.AntCloudProdClient;
import cn.com.antcloud.api.product.AntCloudProdResponse;
import com.alibaba.fastjson.JSON;
import com.timevale.infoauth.service.api.OcrParseTest;
import com.timevale.infoauth.utils.ResourceUtils;
import esign.utils.IOUtil;
import lombok.Data;
import lombok.SneakyThrows;
import org.apache.catalina.User;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * 文档：https://antdigital.com/docs/11/375425
 * <AUTHOR>
 * @since 2023/9/11 17:07
 */
public class AntdigitalAuthTest {

  // 构造时提供的 AccessKey ID 和 secret 为调用方租户下服务账号的 AccessKey ID 和 secret
// 调试前需要确保该租户的管理员对此服务账号赋予了对应接口的权限
// 同时确保该服务账户在网关上开通了服务调用权限
// ENDPOINT 和 PRODUCT_INSTANCE_ID 值不用修改，直接使用

    private static final String ACCESS_KEY = "LTAI9kGTlVr1a7Wx";
    private static final String ACCESS_SECRET = "UNUR6LopAnkyZarBKUQBPN5LCLoJWW";
    private static final String PRODUCT_INSTANCE_ID = "REALPERSON-PROD";
    private static final String ENDPOINT = "https://openapi.antchain.antgroup.com/gateway.do";

    public static void main(String[] args) throws InterruptedException {
        AntCloudProdClient client = AntCloudProdClient.newBuilder()
                .setEndpoint(ENDPOINT)
                .setAccess(ACCESS_KEY, ACCESS_SECRET)
                .build();



        CheckRouteTwometaRequest  request = new CheckRouteTwometaRequest();
                request.setScene(PRODUCT_INSTANCE_ID);
        request.setCert_name("王年英");
        request.setCert_no("362526198412242411");
        request.setScene("COMP");
        request.setOuter_order_no("IDNO"+System.currentTimeMillis());
        request.setExtern_param("{}");

        long startTime = System.currentTimeMillis();
        CheckRouteTwometaResponse response =   client.execute(request);
        long endTime = System.currentTimeMillis();
        System.out.println(JSON.toJSONString(response));
        System.out.println(":::" +(endTime-startTime));
    }


}
