/*
 *  __  .__                            .__
 *_/  |_|__| _____   _______  _______  |  |   ____
 *\   __\  |/     \_/ __ \  \/ /\__  \ |  | _/ __ \
 * |  | |  |  Y Y  \  ___/\   /  / __ \|  |_\  ___/
 * |__| |__|__|_|  /\___  >\_/  (____  /____/\___  >
 *               \/     \/           \/          \/
 *
 *                   Copyright 2017-2017 Timevale.
 */
package com.timevale.infoauth.deploy;

import com.timevale.framework.puppeteer.spring.annotation.EnablePuppeteerConfig;
import com.timevale.infoauth.service.impl.biz.common.async.MdcTaskDecorator;
import com.timevale.infoauth.service.impl.biz.component.mq.outlierdatarecord.OutlierDataRecordInitiator;
import com.timevale.mandarin.microservice.UniversalService;
import com.timevale.mandarin.microservice.util.ApplicationContextUtils;
import com.timevale.monitor.provider.collect.cosumer.ProviderCallRecordConsumer;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 服务启动入口
 *
 * <AUTHOR> Kunpeng
 * @version $Id: Application.java, v 0.1 2017年11月13日 下午2:23:03 LIU Kunpeng Exp $
 */
@UniversalService
//@NoDBService
@EnableFeignClients({
  "com.timevale.filesystem.common.service.api",
  "com.timevale.open.platform.service.service",
  "com.timevale.facedetectalgo.service","com.timevale.gray.config.manage.service.api"
})
@MapperScan("com.timevale.infoauth.dal")
@ComponentScan(
    basePackages = {
      "com.timevale.monitor.provider",
      "com.timevale.infoauth.service",
      "com.timevale.infoauth.dal",
      "com.timevale.infoauth.integration",
      "com.timevale.open.platform.service.service",
      "com.timevale.mc",
      "com.timevale.identity.event.recorder",
      "com.timevale.infoauth.deploy",
            "com.timevale.filesystem.common.service.api",
            "com.timevale.identity.component.account.config"
    })
@EnablePuppeteerConfig({
  "application",
  "JSBZ.SOA_PUBLIC",
  "datacollect-config",
  "EQB.monitor_provider",
  "template_data",
  "common-agreement",
  "JSBZ.messagecenter",
  "AQFK.RISK_PUBLIC",
  "EQB.identity.oss.common.config",
  "EQB.identity.common.config"
})
@EnableAsync(proxyTargetClass = true)
//@AutoConfiCat
//@EnableAspectJAutoProxy(exposeProxy = true)
public class Application {

  public static void main(String[] args) {
    SpringApplication.run(Application.class, args);
    mqConsumerStart();
  }

  @Bean("asyncTaskPool4BaiduOcr")
  public AsyncTaskExecutor asyncTaskPool4BaiduOcr() {
    int cpuCores = Runtime.getRuntime().availableProcessors();
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setThreadNamePrefix("Async-Task-Pool-Baidu-Ocr");
    executor.setCorePoolSize(Math.max(cpuCores, 4));
    executor.setMaxPoolSize(cpuCores * 10);
    executor.setQueueCapacity(100);
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    executor.setTaskDecorator(new MdcTaskDecorator());
    return executor;
  }

  @Bean("asyncTaskPool4HuaweiOcr")
  public AsyncTaskExecutor asyncTaskPool4HuaweiOcr() {
    int cpuCores = Runtime.getRuntime().availableProcessors();
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setThreadNamePrefix("Async-Task-Pool-Huawei-Ocr");
    executor.setCorePoolSize(Math.max(cpuCores, 4));
    executor.setMaxPoolSize(cpuCores * 10);
    executor.setQueueCapacity(100);
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    executor.setTaskDecorator(new MdcTaskDecorator());
    return executor;
  }

  @Bean("asyncTaskPool4HuaweiDrivingLicenseOcr")
  public AsyncTaskExecutor asyncTaskPool4HuaweiDrivingLicenceOcr() {
    int cpuCores = Runtime.getRuntime().availableProcessors();
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setThreadNamePrefix("Async-Task-Pool-Huawei-DrivingLicence-Ocr");
    executor.setCorePoolSize(Math.max(cpuCores, 4));
    executor.setMaxPoolSize(cpuCores * 10);
    executor.setQueueCapacity(100);
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    executor.setTaskDecorator(new MdcTaskDecorator());
    return executor;
  }

  @Bean("asyncTaskPool4HuaweiDrivingPermitOcr")
  public AsyncTaskExecutor asyncTaskPool4HuaweiDrivingPermitOcr() {
    int cpuCores = Runtime.getRuntime().availableProcessors();
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setThreadNamePrefix("Async-Task-Pool-Huawei-DrivingPermit-Ocr");
    executor.setCorePoolSize(Math.max(cpuCores, 4));
    executor.setMaxPoolSize(cpuCores * 10);
    executor.setQueueCapacity(100);
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    executor.setTaskDecorator(new MdcTaskDecorator());
    return executor;
  }

  @Bean("asyncTaskPool4TencentOcr")
  public AsyncTaskExecutor asyncTaskPool4TencentOcr() {
    int cpuCores = Runtime.getRuntime().availableProcessors();
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setThreadNamePrefix("Async-Task-Pool-Tencent-IDCard-Ocr");
    executor.setCorePoolSize(Math.max(cpuCores, 4));
    executor.setMaxPoolSize(cpuCores * 10);
    executor.setQueueCapacity(100);
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    executor.setTaskDecorator(new MdcTaskDecorator());
    return executor;
  }

  @Bean("asyncTaskPool4TencentDrivingPermitOcr")
  public AsyncTaskExecutor asyncTaskPool4TencentDrivingPermitOcr() {
    int cpuCores = Runtime.getRuntime().availableProcessors();
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setThreadNamePrefix("Async-Task-Pool-Tencent-DrivingPermit-Ocr");
    executor.setCorePoolSize(Math.max(cpuCores, 4));
    executor.setMaxPoolSize(cpuCores * 10);
    executor.setQueueCapacity(100);
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    executor.setTaskDecorator(new MdcTaskDecorator());
    return executor;
  }

    @Bean("asyncTaskPool4TencentDrivingLicenseOcr")
    public AsyncTaskExecutor asyncTaskPool4TencentDrivingLicenseOcr() {
        int cpuCores = Runtime.getRuntime().availableProcessors();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("Async-Task-Pool-Tencent-DrivingLicense-Ocr");
        executor.setCorePoolSize(Math.max(cpuCores, 4));
        executor.setMaxPoolSize(cpuCores * 10);
        executor.setQueueCapacity(100);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new MdcTaskDecorator());
        return executor;
    }

  // 2024/10/8 TODO mangcao  阿里云多线程配置
  @Bean("asyncTaskPool4AliyunOcr")
  public AsyncTaskExecutor asyncTaskPool4AliyunOcr() {
    int cpuCores = Runtime.getRuntime().availableProcessors();
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setThreadNamePrefix("Async-Task-Pool-aliyun-Ocr");
    executor.setCorePoolSize(Math.max(cpuCores, 4));
    executor.setMaxPoolSize(cpuCores * 10);
    executor.setQueueCapacity(100);
    executor.setKeepAliveSeconds(60);
    executor.setAllowCoreThreadTimeOut(true);
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    executor.setTaskDecorator(new MdcTaskDecorator());
    return executor;
  }


  @Bean("asyncDataReporting")
  public AsyncTaskExecutor asyncDataReporting() {
    int cpuCores = Runtime.getRuntime().availableProcessors();
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setThreadNamePrefix("Async-Task-Pool-data-reporting");
    executor.setCorePoolSize(1);
    executor.setMaxPoolSize(1);
    executor.setQueueCapacity(200);
    executor.setKeepAliveSeconds(60);
    executor.setAllowCoreThreadTimeOut(true);
    //丢弃任务策略
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
    executor.setTaskDecorator(new MdcTaskDecorator());
    return executor;
  }

  /**
   * 在应用启动后,启动mq的消费者
   * */
  private static void mqConsumerStart() {


   ApplicationContextUtils.getApplicationContext().getBean(OutlierDataRecordInitiator.class).init();


   ApplicationContextUtils.getApplicationContext().getBean(ProviderCallRecordConsumer.class).init();

  }

}
