<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC
		"-//mybatis.org//DTD Config 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>

	<!-- 全局配置 -->
	<settings>
		<!--允许 JDBC 支持自动生成主键 -->
		<setting name="useGeneratedKeys" value="false" />
		<!--是否开启自动驼峰命名规则（camel case）映射 -->
		<setting name="mapUnderscoreToCamelCase" value="true" />
		<!-- 延迟加载的全局开关。默认值：false。 -->
        <setting name="lazyLoadingEnabled" value="true"/>
        <!-- 设置超时时间，它决定驱动等待数据库响应的秒数。-->
        <setting name="defaultStatementTimeout" value="10"/>
		<!--<setting name="logImpl" value="STDOUT_LOGGING" />-->
	</settings>

	<plugins>
		<plugin interceptor="com.github.pagehelper.PageHelper">
			<!-- 4.0.0以后版本可以不设置该参数 -->
			<property name="dialect" value="mysql" />
			<!-- 该参数默认为false -->
			<!-- 设置为true时，会将RowBounds第一个参数offset当成pageNum页码使用 -->
			<!-- 和startPage中的pageNum效果一样 -->
			<property name="offsetAsPageNum" value="true" />
			<!-- 该参数默认为false -->
			<!-- 设置为true时，使用RowBounds分页会进行count查询 -->
			<property name="rowBoundsWithCount" value="true" />
			<!-- 设置为true时，如果pageSize=0或者RowBounds.limit = 0就会查询出全部的结果 -->
			<!-- （相当于没有执行分页查询，但是返回结果仍然是Page类型） -->
			<property name="pageSizeZero" value="true" />
			<!-- 3.3.0版本可用 - 分页参数合理化，默认false禁用 -->
			<!-- 启用合理化时，如果pageNum<1会查询第一页，如果pageNum>pages会查询最后一页 -->
			<!-- 禁用合理化时，如果pageNum<1或pageNum>pages会返回空数据 -->
			<property name="reasonable" value="true" />
			<!-- 3.5.0版本可用 - 为了支持startPage(Object params)方法 -->
			<!-- 增加了一个`params`参数来配置参数映射，用于从Map或ServletRequest中取值 -->
			<!-- 可以配置pageNum,pageSize,count,pageSizeZero,reasonable,orderBy,不配置映射的用默认值 -->
			<!-- 不理解该含义的前提下，不要随便复制该配置 -->
			<property name="params" value="pageNum=start;pageSize=limit;" />
			<!-- 支持通过Mapper接口参数来传递分页参数 -->
			<property name="supportMethodsArguments" value="true" />
			<!-- always总是返回PageInfo类型,check检查返回类型是否为PageInfo,none返回Page -->
			<property name="returnPageInfo" value="check" />
		</plugin>
	</plugins>
</configuration>