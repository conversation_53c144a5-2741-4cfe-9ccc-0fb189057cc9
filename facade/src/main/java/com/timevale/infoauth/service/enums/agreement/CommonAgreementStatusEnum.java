package com.timevale.infoauth.service.enums.agreement;

import java.util.Objects;

/**
 * 通用协议状态
 * 协议状态。0=待生效 1=生效中 2=作废
 * <AUTHOR>
 * @since 2024/12/4 21:04
 */
public enum CommonAgreementStatusEnum {

    AGREE_STATUS_NOT_ENABLE(0, "待生效"),

    AGREE_STATUS_ENABLE(1, "生效中"),

    AGREE_STATUS_DISABLE(2, "作废");

    private int code;
    private String message;

    CommonAgreementStatusEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }


    public static boolean notEnable(Integer status) {
        return !enable(status);
    }

    public static boolean enable(Integer status) {
        return Objects.equals(status, AGREE_STATUS_ENABLE.getCode());
    }


    public static CommonAgreementStatusEnum getByStatus(Integer status){
        for (CommonAgreementStatusEnum value : CommonAgreementStatusEnum.values()) {
            if(Objects.equals(status,value.code)){
                return value;
            }
        }
        return AGREE_STATUS_NOT_ENABLE;
    }


}
