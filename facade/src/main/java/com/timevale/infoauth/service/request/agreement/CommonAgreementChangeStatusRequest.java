package com.timevale.infoauth.service.request.agreement;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 通用协议状态修改
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommonAgreementChangeStatusRequest extends CommonAgreementBaseRequest {



  /**
   *  协议状态。0=待生效 1=生效中 2=作废.
   * @see com.timevale.infoauth.service.enums.agreement.CommonAgreementStatusEnum
   *
   * */
  private Integer status;

  /** 操作人. */
  private String operator;

}
