package com.timevale.infoauth.dal.infoauth.facade;

import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class AuthInfoQueryRequestDTO {
  String infoAuthId;
  String idno;
  String cardno;
  String mobile;
  String provider;
  /**
   * 企业组织机构代码
   * */
  String codeORG;
  /**
   * 企业社会统一信用代码
   * */
  String codeUSC;

  /**
   * 工商注册号
   * */
  String codeREG;
  String appId;
  Date updateTime;
  Date startTime;
  Date endTime;
  Timestamp modifyTime;
  Timestamp createTime;
  Integer serviceStatus, currIndex, pageSize;
}
