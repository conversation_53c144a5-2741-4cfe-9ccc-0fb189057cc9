package com.timevale.infoauth.service.api;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.enums.InfoAuthServiceType;
import com.timevale.infoauth.service.enums.ProvResult;
import com.timevale.infoauth.service.impl.common.dto.Provout;
import com.timevale.infoauth.service.impl.provider.biz.adapter.bankthree.BankThreeUnionPay;
import com.timevale.infoauth.service.impl.dto.BankthreeProvin;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Ignore;
import org.testng.annotations.Test;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2020/4/16 下午5:02
 */
@JsonDataIgnore
public class BankThreeUnionPayTest extends ApplicationTest {

  @Resource BankThreeUnionPay bankThreeUnionPay;

  @Test
  public void authFail() {
    BankthreeProvin input = new BankthreeProvin();
    input.setCardno("****************");
    input.setName("周树腾");
    input.setCertNumber("131126198702250051111");
    input.setProviderName("UNIONPAY");
    input.setServiceType(InfoAuthServiceType.BANKCARD_3_ELEMENT);
    input.setBizId(UUID.randomUUID().toString());
    Provout out = bankThreeUnionPay.auth(input);
    Assert.assertNotNull(out);
    Assert.assertEquals(ProvResult.FAILURE, out.getProvResult());
  }

  @Ignore
  @Test
  public void authSuccess() {
    BankthreeProvin input = new BankthreeProvin();
    input.setCardno("6222081203009064490");
    input.setName("林书芳");
    input.setCertNumber("330327199106085684");
    input.setProviderName("UNIONPAY");
    input.setServiceType(InfoAuthServiceType.BANKCARD_3_ELEMENT);
    input.setBizId(UUID.randomUUID().toString());
    Provout out = bankThreeUnionPay.auth(input);
    Assert.assertNotNull(out);
    Assert.assertEquals(ProvResult.SUCCESS, out.getProvResult());
  }
}
