package com.timevale.infoauth.dal.infoauth.dao.monitor;

import com.timevale.infoauth.dal.infoauth.dataobject.monitor.DegradationConfigDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/** 供应商降级配置 */
public interface DegradationConfigDAO {

  @Insert({
          "<script>",
          "INSERT INTO `monitor_provider_degradation_config` (`config_id`,`alert_id`, `switch_enabled`, `change_action`, `change_degrade_provider`, ",
          "`change_time`, `scope`, `scope_app_id`, `desc`) VALUES ",
          "(#{configId},#{alertId}, #{switchEnabled}, #{changeAction}, #{changeDegradeProvider},",
          "#{changeTime}, #{scope}, #{scopeAppId}, #{desc})",
          "</script>"
  })
  int insert(DegradationConfigDO entity);

  @Update({
          "<script>",
          "UPDATE `monitor_provider_degradation_config` ",
          "<set>",
          "<if test=\"alertId != null\">",
          "alert_id = #{alertId},",
          "</if>",
          "<if test=\"switchEnabled != null\">",
          "switch_enabled = #{switchEnabled},",
          "</if>",
          "<if test=\"changeAction != null\">",
          "change_action = #{changeAction},",
          "</if>",
          "<if test=\"changeDegradeProvider != null\">",
          "change_degrade_provider = #{changeDegradeProvider},",
          "</if>",
          "<if test=\"changeTime != null\">",
          "change_time = #{changeTime},",
          "</if>",
          "<if test=\"scope != null\">",
          "scope = #{scope},",
          "</if>",
          "<if test=\"scopeAppId != null\">",
          "scope_app_id = #{scopeAppId},",
          "</if>",
          "<if test=\"desc != null\">",
          "`desc`  = #{desc},",
          "</if>",
          "</set>",
          " WHERE `config_id` = #{configId}",
          "</script>",
  })
  void updateById(DegradationConfigDO entity);


  @Select({
    "<script>",
    "SELECT * FROM `monitor_provider_degradation_config`",
    " WHERE `alert_id`=#{alertId}  order by id desc  LIMIT 1",
    "</script>",
  })
  DegradationConfigDO getByAlertId(String alertId);


}
