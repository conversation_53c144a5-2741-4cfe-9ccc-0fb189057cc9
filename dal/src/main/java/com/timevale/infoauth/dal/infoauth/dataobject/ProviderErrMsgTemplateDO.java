package com.timevale.infoauth.dal.infoauth.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 供应商错误msg模板表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProviderErrMsgTemplateDO{

    /**
     * createTime 记录创建时间.
     */
    private Date createTime;
    /**
     * modifyTime 记录更新时间.
     */
    private Date modifyTime;
    /**
     * id 自增主键.
     */
    private Long id;
    /**
     * pCode 供应商错误码.
     */
    private String pCode;
    /**
     * extend 扩展字段.
     */
    private String extend;
    /**
     * provider 供应商名称.
     */
    private String provider;
    /**
     * pMsgRegex 供应商错误msg匹配白名单.
     */
    private String pMsgRegex;
    /**
     * pMsgReplace 供应商错误msg替换模板.
     */
    private String pMsgReplace;
    /**
     * bizType 信息比对类型.
     */
    private Integer bizType;
}
