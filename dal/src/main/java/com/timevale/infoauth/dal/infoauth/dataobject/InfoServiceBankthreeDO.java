package com.timevale.infoauth.dal.infoauth.dataobject;

import java.util.Date;

public class InfoServiceBankthreeDO extends InfoServiceAuthDO {

  /** createTime 记录创建时间. */
  private Date createTime;
  /** modifyTime 记录修改时间. */
  private Date modifyTime;
  /** id 自增主键. */
  private Long id;
  /** bank 银行卡号所属银行卡. */
  private String bank;
  /** idno 身份证号. */
  private String idno;
  /** name 姓名. */
  private String name;
  /** cardno 银行卡号. */
  private String cardno;
  /** infoauthId 业务主键. */
  private String infoauthId;
  /** providerId 4要素服务提供商. */
  private String providerId;
  /** providerResult 服务商处理结果. */
  private String providerResult;
  /** result 适配结果. */
  private Integer result;
  /** serviceStatus 记录状态. */
  private Integer serviceStatus;
  private Long errId;

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public Date getModifyTime() {
    return modifyTime;
  }

  public void setModifyTime(Date modifyTime) {
    this.modifyTime = modifyTime;
  }

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getBank() {
    return bank;
  }

  public void setBank(String bank) {
    this.bank = bank;
  }

  public String getIdno() {
    return idno;
  }

  public void setIdno(String idno) {
    this.idno = idno;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getCardno() {
    return cardno;
  }

  public void setCardno(String cardno) {
    this.cardno = cardno;
  }

  public String getInfoauthId() {
    return infoauthId;
  }

  public void setInfoauthId(String infoauthId) {
    this.infoauthId = infoauthId;
  }

  public String getProviderId() {
    return providerId;
  }

  public void setProviderId(String providerId) {
    this.providerId = providerId;
  }

  public String getProviderResult() {
    return providerResult;
  }

  public void setProviderResult(String providerResult) {
    this.providerResult = providerResult;
  }

  public Integer getResult() {
    return result;
  }

  public void setResult(Integer result) {
    this.result = result;
  }

  public Integer getServiceStatus() {
    return serviceStatus;
  }

  public void setServiceStatus(Integer serviceStatus) {
    this.serviceStatus = serviceStatus;
  }

  public Long getErrId() {
    return errId;
  }

  public void setErrId(Long errId) {
    this.errId = errId;
  }
}
