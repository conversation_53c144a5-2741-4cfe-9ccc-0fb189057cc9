package com.timevale.infoauth.service.api;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.impl.biz.checker.OrgInputChecker;
import com.timevale.infoauth.service.impl.common.dto.OrgProvout;
import com.timevale.infoauth.service.impl.common.dto.OrganProvin;
import com.timevale.infoauth.service.request.LawFirmTwoRequest;
import com.timevale.infoauth.service.request.OrgInfoAuthRequest;
import com.timevale.infoauth.service.request.OrganTwoRequest;
import com.timevale.infoauth.service.request.SocialOrganTwoRequest;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.testng.Assert;
import org.testng.annotations.Test;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
@JsonDataIgnore
public class OrgInputCheckerTest extends ApplicationTest {

  @Test
  public void orgInputCheckTest() {
    String name =
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa";
    OrgInfoAuthRequest input = new OrgInfoAuthRequest();
    input.setName(name);
    Assert.assertThrows(() -> OrgInputChecker.orgInputCheck(input));
    input.setName("杭州天谷信息科技有限公司");
    input.setLegalName(name);
    Assert.assertThrows(() -> OrgInputChecker.orgInputCheck(input));
    input.setLegalName("何一兵");
    Assert.assertThrows(() -> OrgInputChecker.orgInputCheck(input));
    input.setCodeORG("12345xxxxx");
    Assert.assertThrows(() -> OrgInputChecker.orgInputCheck(input));
    input.setCodeORG(null);
    input.setCodeREG("12345xxxxx");
    Assert.assertThrows(() -> OrgInputChecker.orgInputCheck(input));
    input.setCodeREG(null);
    input.setCodeUSC("12345xxxxx");
    Assert.assertThrows(() -> OrgInputChecker.orgInputCheck(input));
  }

  @Test
  public void socialOrganTwoCheckTest() {
    String name =
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa";
    SocialOrganTwoRequest input = new SocialOrganTwoRequest();
    input.setName(name);
    Assert.assertThrows(() -> OrgInputChecker.socialOrganTwoCheck(input));
    input.setName("潇湘晨报社");
    Assert.assertThrows(() -> OrgInputChecker.socialOrganTwoCheck(input));
    input.setCodeORG("12345xxxxx");
    Assert.assertThrows(() -> OrgInputChecker.socialOrganTwoCheck(input));
    input.setCodeORG(null);
    input.setCodeREG("12345xxxxx");
    Assert.assertThrows(() -> OrgInputChecker.socialOrganTwoCheck(input));
    input.setCodeREG(null);
    input.setCodeUSC("12345xxxxx");
    Assert.assertThrows(() -> OrgInputChecker.socialOrganTwoCheck(input));
  }

  @Test
  public void organTwoCheckTest() {
    String name =
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa";
    OrganTwoRequest input = new OrganTwoRequest();
    Assert.assertThrows(() -> OrgInputChecker.organTwoCheck(input));
    input.setName(name);
    Assert.assertThrows(() -> OrgInputChecker.organTwoCheck(input));
    input.setName("杭州天谷信息科技有限公司");
    Assert.assertThrows(() -> OrgInputChecker.organTwoCheck(input));
    input.setCodeREG("12345xxxxx");
    Assert.assertThrows(() -> OrgInputChecker.organTwoCheck(input));
    input.setCodeREG(null);
    input.setCodeUSC("12345xxxxx");
    Assert.assertThrows(() -> OrgInputChecker.organTwoCheck(input));
  }

  @Test
  public void lawFirmTwoCheckTest() {
    String name =
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa";
    LawFirmTwoRequest input = new LawFirmTwoRequest();
    Assert.assertThrows(() -> OrgInputChecker.lawFirmTwoCheck(input));
    input.setName(name);
    Assert.assertThrows(() -> OrgInputChecker.lawFirmTwoCheck(input));
    input.setName("山东海乐普律师事务所");
    Assert.assertThrows(() -> OrgInputChecker.lawFirmTwoCheck(input));
    input.setCodeORG("12345xxxxx");
    Assert.assertThrows(() -> OrgInputChecker.lawFirmTwoCheck(input));
    input.setCodeORG(null);
    input.setCodeREG("12345xxxxx");
    Assert.assertThrows(() -> OrgInputChecker.lawFirmTwoCheck(input));
    input.setCodeREG(null);
    input.setCodeUSC("12345xxxxx");
    Assert.assertThrows(() -> OrgInputChecker.lawFirmTwoCheck(input));
  }

  @Test
  public void checkAllCodeTest() {
    OrganProvin input = new OrganProvin();
    Assert.assertThrows(() -> OrgInputChecker.checkAllCode(input));
    Assert.assertTrue(true);
  }

  @Test
  public void checkAllCodeForStandrdTest() {
    OrgProvout input = new OrgProvout();
    Assert.assertThrows(() -> OrgInputChecker.checkAllCodeForStandrd(input));
    Assert.assertTrue(true);
  }
}
