package com.timevale.infoauth.service.enums;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
public enum OrgStatus {
  CUN_XU("1", "存续"),
  ZHU_XIAO("2", "注销"),
  DIAO_XIAO("3", "吊销"),
  CHE_XIAO("4", "撤销"),
  QIAN_CHU("5", "迁出"),
  SHE_LI_ZHONG("6", "设立中"),
  QING_SUAN_ZHONG("7", "清算中"),
  TING_YE("8", "停业"),
  QI_TA("9", "其他"),

  XIE_YE("11", "歇业"),
  ZE_LING_GUAN_BI("12", "责令关闭"),
  FEI_FA_ZU_ZHI("13", "已取缔非法社会组织"),
  FEI_FA_ZU_ZHI_QI_TA("14","涉嫌非法社会组织其他未知类型为空"),
  ;

  private String value;

  private String desc;

  OrgStatus(String value, String desc) {
    this.value = value;
    this.desc = desc;
  }

  public String getValue() {
    return value;
  }

  public String getDesc() {
    return desc;
  }

  public static OrgStatus getByValue(String value) {
    for (OrgStatus status : OrgStatus.values()) {
      if (status.getValue().equalsIgnoreCase(value)) return status;
    }
    return QI_TA;
  }

  public static OrgStatus getByDesc(String desc) {
    for (OrgStatus status : OrgStatus.values()) {
      if (status.getDesc().equalsIgnoreCase(desc)) {
        return status;
      }
    }
    return QI_TA;
  }
}
