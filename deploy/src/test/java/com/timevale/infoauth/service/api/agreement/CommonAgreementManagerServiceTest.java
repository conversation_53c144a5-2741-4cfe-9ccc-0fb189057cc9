package com.timevale.infoauth.service.api.agreement;

import com.timevale.infoauth.ApplicationTest;
import com.timevale.infoauth.service.enums.agreement.*;
import com.timevale.infoauth.service.request.agreement.*;
import com.timevale.infoauth.service.response.agreement.*;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024/12/16 17:31
 */
@JsonDataIgnore
public class CommonAgreementManagerServiceTest extends ApplicationTest {

    @Autowired
    private CommonAgreementManagerService commonAgreementManagerService;
    @Autowired
    private CommonAgreementService commonAgreementService;

    @Test
    public void getAgreementPageList(){
        CommonAgreementListQueryRequest request = new CommonAgreementListQueryRequest();
        CommonAgreementPageListResponse pageListResponse =
                commonAgreementManagerService.queryAgreementPageList(request).getData();
        Assert.assertNotNull(pageListResponse);
        CommonAgreementInfoBaseResponse baseResponse = pageListResponse.getData().get(0);
        Assert.assertNotNull(baseResponse);


        String commonAgreementId  = "4ARG3667029494608691202";
        CommonAgreementQueryRequest queryRequest = new CommonAgreementQueryRequest();
        queryRequest.setCommonAgreementId(commonAgreementId);
        CommonAgreementInfoDetailResponse detailResponse =
                commonAgreementService.queryAgreementInfoDetail(queryRequest).getData();

        CommonAgreementExtendResponse extendResponse =
                commonAgreementService.queryAgreementExtendInfo(queryRequest).getData();
        Assert.assertEquals(detailResponse.getCommonAgreementTitle(), extendResponse.getCommonAgreementTitle());


        CommonAgreementReleaseHistoryQueryRequest releaseHistoryRequest = new CommonAgreementReleaseHistoryQueryRequest();
        releaseHistoryRequest.setCommonAgreementId(commonAgreementId);
        CommonAgreementReleaseHistoryResponse releaseHistoryResponse =
                commonAgreementManagerService.queryAgreementReleaseHistory(releaseHistoryRequest).getData();
        Assert.assertNotNull(releaseHistoryResponse.getData().get(0).getShortName());
    }

//    @Test
    public void crudAgreement(){
        CommonAgreementInfoAddRequest infoAddRequest = new CommonAgreementInfoAddRequest();
        infoAddRequest.setCommonAgreementTitle("测试折袖01");
        infoAddRequest.setCommonAgreementMark("备注01");
        infoAddRequest.setCommonAgreementType(CommonAgreementTypeEnum.common.getType());
        infoAddRequest.setCategory("identity");
        infoAddRequest.setCreator("zhexiu");
        infoAddRequest.setOperator("折袖");
        String commonAgreementId = commonAgreementManagerService.addAgreement(infoAddRequest).getData();


        CommonAgreementInfoModifyRequest modifyRequest = new CommonAgreementInfoModifyRequest();
        modifyRequest.setCommonAgreementId(commonAgreementId);
        modifyRequest.setCommonAgreementTitle("测试折袖02");
        modifyRequest.setCommonAgreementMark("备注02");
        modifyRequest.setOperator("折袖02");
        boolean modifySuccess = commonAgreementManagerService.modifyAgreement(modifyRequest).getData();
        Assert.assertTrue(modifySuccess);

        CommonAgreementChangeStatusRequest statusRequest  =new CommonAgreementChangeStatusRequest();
        statusRequest.setCommonAgreementId(commonAgreementId);
        statusRequest.setStatus(CommonAgreementStatusEnum.AGREE_STATUS_ENABLE.getCode());
        statusRequest.setOperator("折袖03");
        commonAgreementManagerService.changeAgreementStatus(statusRequest);


        CommonAgreementContentAddRequest contentAddRequest = new CommonAgreementContentAddRequest();
        contentAddRequest.setCommonAgreementId(commonAgreementId);
        contentAddRequest.setCreator("折袖02");
        contentAddRequest.setOperator("折袖02");
        contentAddRequest.setLang(CommonAgreementLangEnum.ZH_CN.getLang());
        contentAddRequest.setReleaseVersion("v1.0.1");
        contentAddRequest.setShortName("在学校");
        contentAddRequest.setFullName("折袖02在学校");
        contentAddRequest.setContent("xxxx");
        contentAddRequest.setContentType(CommonAgreementContentTypeEnum.ZEYUAN_URL.getCode());
        String releaseRecordId =  commonAgreementManagerService.agreementContentAdd(contentAddRequest).getData();


        CommonAgreementContentModifyRequest contentModifyRequest = new CommonAgreementContentModifyRequest();
        contentModifyRequest.setReleaseRecordId(releaseRecordId);
        contentModifyRequest.setShortName("学校");
        commonAgreementManagerService.agreementContentModify(contentModifyRequest);


        CommonAgreementContentStatustChangeRequest contentStatustChangeRequest = new CommonAgreementContentStatustChangeRequest();
        contentStatustChangeRequest.setReleaseRecordId(releaseRecordId);
        contentStatustChangeRequest.setOperator("折袖04");
        contentStatustChangeRequest.setEnableStatus(CommonAgreementReleaseStatusEnum.RELEASE_ENABLE.getCode());
        commonAgreementManagerService.agreementContentChangeStatus(contentStatustChangeRequest);


        CommonAgreementBatchQueryRequest batchQueryRequest =  new CommonAgreementBatchQueryRequest();
    batchQueryRequest.setCommonAgreementIds(Arrays.asList(commonAgreementId));
        CommonAgreementInfoExtendBO extendDTO = commonAgreementService.queryBatchAgreementExtendInfo(batchQueryRequest).getData().getAgreementInfos().get(0);
        Assert.assertEquals(extendDTO.getCommonAgreementTitle(), modifyRequest.getCommonAgreementTitle());

        Assert.assertEquals(extendDTO.getShortName(), contentModifyRequest.getShortName());
        Assert.assertEquals(extendDTO.getFullName(), contentAddRequest.getFullName());


        CommonAgreementReleaseRecordQueryRequest recordQueryRequest = new CommonAgreementReleaseRecordQueryRequest();
        recordQueryRequest.setReleaseRecordId(releaseRecordId);
        CommonAgreementReleaseRecordBaseResponse recordBaseResponse =
                commonAgreementService.queryAgreementReleaseRecordBase(recordQueryRequest).getData();
        CommonAgreementReleaseRecordResponse recordResponse  =
                commonAgreementService.queryAgreementReleaseRecordDetail(recordQueryRequest).getData();

        Assert.assertEquals(recordBaseResponse.getContext(), recordResponse.getContext());
    }
}
