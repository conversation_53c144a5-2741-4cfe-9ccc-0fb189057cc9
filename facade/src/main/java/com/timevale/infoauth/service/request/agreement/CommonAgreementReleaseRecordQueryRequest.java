package com.timevale.infoauth.service.request.agreement;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 通用协议发布信息查询
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CommonAgreementReleaseRecordQueryRequest extends ToString {

  /** 协议发布记录ID. */
  private String releaseRecordId;

}
